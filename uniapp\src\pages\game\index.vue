<template>
  <view class="page-box">
    <view class="game-page-container">
      <!-- 关卡详情信息 -->
      <view v-if="currentLevelDetail" class="level-detail-info card">
        <view class="level-header">
          <text class="level-name">{{ currentLevelDetail.name }}</text>
          <!-- <text class="level-difficulty">难度: {{ currentLevelDetail.difficulty }}星</text> -->
        </view>
        <!-- <text class="level-description">{{ currentLevelDetail.description }}</text> -->
        <view class="level-stats">
          <text class="stats-item">词组数量: {{ currentLevelDetail.phrases?.length || 0 }}</text>
          <text class="stats-item" v-if="currentLevelDetail.isCompleted">✅ 已完成</text>
          <text class="stats-item" v-else>🎯 挑战中</text>
        </view>
      </view>

      <!-- 备用：词库信息显示 -->
      <view v-else-if="selectedLibraryInfo" class="selected-library-info card">
        <text class="library-name">{{ selectedLibraryInfo.name }}</text>
      </view>

      <!-- 游戏区域 -->
      <view v-if="currentLevel" class="game-area card">
        <view class="game-info-bar">
          <text>关卡: {{ currentLevel.name }}</text>
          <text>已配对: {{ matchedPairs }}/{{ totalPairs }}</text>
          <text v-if="isProgressSyncing" class="sync-status">同步中...</text>
          <!-- 调试按钮 -->
          <view class="debug-btn" @click="debugMode = !debugMode" v-if="true">
            <text class="debug-btn-text">{{ debugMode ? '关闭调试' : '调试模式' }}</text>
          </view>
          <!-- 重玩按钮 -->
          <view class="replay-btn" @click="resetGame">
            <text class="replay-btn-text">重玩</text>
          </view>
        </view>

        <view class="game-board">
          <view
            v-for="(tile, index) in gameBoard"
            :key="tile.id"
            class="board-tile"
            :class="{
              selected: tile.selected,
              matched: tile.matched,
              'debug-mode': debugMode,
              'tile-english': tile.type === 'english',
              'tile-chinese': tile.type === 'chinese',
              'tile-short': tile.type === 'english' && tile.word && tile.word.english.length <= 4,
              'tile-medium': tile.type === 'english' && tile.word &&
                tile.word.english.length > 4 &&
                tile.word.english.length <= 7,
              'tile-long': tile.type === 'english' && tile.word && tile.word.english.length > 7,
            }"
            :style="{
              backgroundColor: tile.color,
              position: 'absolute',
              left: tile.position.x + 'rpx',
              top: tile.position.y + 'rpx',
            }"
            :data-position="debugMode ? `(${Math.round(tile.position.x)}, ${Math.round(tile.position.y)})` : ''"
            @click="handleTileClick(index)"
          >
            <!-- 英文卡片只显示英文 -->
            <text v-if="tile.type === 'english'" class="tile-word">{{
              tile.word ? tile.word.english : ""
            }}</text>
            <!-- 中文卡片只显示中文 -->
            <text v-if="tile.type === 'chinese'" class="tile-chinese-only">{{
              tile.word ? tile.word.chinese : ""
            }}</text>
          </view>
        </view>
      </view>

      <!-- 关卡完成弹窗 -->
      <view v-if="isGameEndModalVisible" class="modal-overlay">
        <view class="modal-content">
          <text class="modal-title">{{ gameResultText }}</text>
          <view class="modal-buttons">
            <button
              @click="nextLevel"
              class="modal-button primary"
              v-if="gameWon && canGoToNextLevel"
            >
              下一关
            </button>
            <button @click="resetGame" class="modal-button">再试一次</button>
            <button @click="goBackHome" class="modal-button">返回首页</button>
          </view>
        </view>
      </view>
    </view>

    <!-- 右上角设置按钮 -->
    <!-- <view class="floating-settings-btn" @click="showSettings">
      <text class="settings-icon">⚙️</text>
    </view> -->

    <!-- 设置弹窗 -->
    <SettingsModal
      :visible="showSettingsModal"
      @close="closeSettings"
      @settingsChange="handleSettingsChange"
    />

    <!-- 悬浮调试按钮（仅开发环境显示） -->
    <!-- #ifdef MP-WEIXIN -->
    <view v-if="isDevelopment" class="floating-debug-btn" @click="goToDebug">
      <text class="debug-icon">🔧</text>
    </view>
    <!-- #endif -->

    <!-- #ifdef H5 -->
    <view v-if="isDevelopment" class="floating-debug-btn" @click="goToDebug">
      <text class="debug-icon">🔧</text>
    </view>
    <!-- #endif -->
  </view>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from "vue"
  import { onLoad, onShow, onHide } from "@dcloudio/uni-app"
  import weixinApi from '../../api/weixin'
  import { showSuccess, showError, withLoading, createLoadingState } from '../../api/utils'
  import { shareUtils } from '../../utils/share'
  import { audioManager } from '../../utils/audio'
  import { shouldShowDebugFeatures } from '../../utils/development'
  import SettingsModal from '../../components/SettingsModal.vue'
  import { useGlobalConfig } from '../../composables/useGlobalConfig'
  import type { UserInfo, LevelDetail, PhraseInfo, GameSettings } from '../../api/types'

  const selectedLibraryInfo = ref(null)
  const currentLevel = ref(null) // 当前关卡
  const currentLevelId = ref(1) // 当前关卡ID
  const currentLevelDetail = ref<LevelDetail | null>(null) // 当前关卡详情
  const userInfo = ref<UserInfo | null>(null) // 用户信息
  const isProgressSyncing = ref(false) // 进度同步状态
  const selectedLevelData = ref(null) // 从首页传递的关卡数据

  // 分享奖励执行状态
  let isHandlingGameShareReward = false

  const gameBoard = ref([]);
  const selectedTiles = ref([]); // 存储用户选择的卡片
  const matchedPairs = ref(0); // 已配对数量
  const totalPairs = ref(0); // 总配对数量
  const isChecking = ref(false); // 是否正在检查匹配
  const debugMode = ref(false); // 调试模式，显示卡片边界

  let tileIdCounter = 0;

  // 生成随机淡色（纯色：淡粉、淡蓝、淡绿）
  const generateRandomLightColor = () => {
    const colors = [
      "#FFE1E6", // 淡粉色
      "#E1F0FF", // 淡蓝色
      "#E1FFE1", // 淡绿色
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  };

  const isGameEndModalVisible = ref(false);
  const gameResultText = ref("");
  const gameWon = ref(false);

  // 开发环境检测
  const isDevelopment = ref(false)

  // 设置相关
  const showSettingsModal = ref(false)
  const gameSettings = ref<GameSettings>({
    backgroundMusic: true,
    soundEffects: true,
    vibration: true
  })

  // 全局配置
  const {
    globalConfig,
    getBackgroundMusicUrl,
    initializeGlobalConfig
  } = useGlobalConfig()

  // 计算属性：是否可以进入下一关
  const canGoToNextLevel = computed(() => {
    if (!selectedLibraryInfo.value || !selectedLibraryInfo.value.words)
      return false;
    const maxlevel = Math.min(
      Math.floor(selectedLibraryInfo.value.words.length / 8),
      1000
    );
    return currentLevelId.value < maxlevel;
  });

  // 获取当前关卡的单词
  const wordsForCurrentLevel = computed(() => {
    // 优先使用关卡详情中的词组
    if (currentLevelDetail.value && currentLevelDetail.value.phrases) {
      console.log(currentLevelDetail, 'currentLevelDetail')
      return currentLevelDetail.value.phrases.map(phrase => ({
        english: phrase.text,
        chinese: phrase.meaning,
      }))
    }

    // 备用：使用词库中的单词
    if (selectedLibraryInfo.value && selectedLibraryInfo.value.words) {
      // 每个关卡使用8个不同的单词，循环使用词库中的单词
      const words = selectedLibraryInfo.value.words;
      const startIndex = ((currentLevelId.value - 1) * 8) % words.length;
      const selectedWords = [];

      for (let i = 0; i < 8; i++) {
        const wordIndex = (startIndex + i) % words.length;
        selectedWords.push(words[wordIndex]);
      }

      return selectedWords;
    }
    return [];
  });

  onLoad(async () => {
    // 初始化全局配置
    await initializeGlobalConfig()

    await initializeGame()

    // 检测开发环境
    checkDevelopmentEnvironment()

    // 初始化音频设置
    initAudioSettings()
  })

  /**
   * 初始化音频设置
   */
  const initAudioSettings = () => {
    try {
      const settings = audioManager.getSettings()
      gameSettings.value = { ...settings }
      console.log('音频设置初始化完成:', settings)

      // 如果背景音乐开启，播放游戏页面背景音乐
      if (settings.backgroundMusic) {
        const musicUrl = getBackgroundMusicUrl('game')
        console.log('播放游戏页面背景音乐:', musicUrl)
        audioManager.playBackgroundMusic('game', musicUrl)
      }
    } catch (error) {
      console.error('初始化音频设置失败:', error)
    }
  }

  // 页面显示时
  onShow(() => {
    console.log('游戏页面显示')
    audioManager.onPageShow()

    // 播放游戏背景音乐
    const settings = audioManager.getSettings()
    if (settings.backgroundMusic) {
      const musicUrl = getBackgroundMusicUrl('game')
      audioManager.playBackgroundMusic('game', musicUrl)
    }
  })

  // 页面隐藏时
  onHide(() => {
    console.log('游戏页面隐藏')
    audioManager.onPageHide()
  })

  /**
   * 游戏初始化
   */
  const initializeGame = async () => {
    try {
      // 1. 加载用户信息
      await loadUserInfo()

      // 2. 获取选择的关卡信息
      const levelData = uni.getStorageSync("selectedLevel")
      if (levelData) {
        try {
          selectedLevelData.value = JSON.parse(levelData)
          console.log('获取到选择的关卡数据:', selectedLevelData.value)

          // 3. 尝试加载关卡详情
          await loadLevelDetail(selectedLevelData.value.id)

          // 4. 记录游戏开始
          if (userInfo.value) {
            await recordGameStart(selectedLevelData.value.id)
          }

          // 5. 初始化游戏
          resetGame()
        } catch (e) {
          console.error("Failed to parse selected level data:", e)
          // 如果关卡详情加载失败，尝试使用词库数据作为备用
          await loadLibraryDataAsFallback()
        }
      } else {
        // 如果没有关卡数据，尝试使用词库数据作为备用
        await loadLibraryDataAsFallback()
      }
    } catch (error) {
      console.error('游戏初始化失败:', error)
      uni.showToast({ title: "初始化失败", icon: "none" })
    }
  }

  /**
   * 加载关卡详情
   */
  const loadLevelDetail = async (levelId: string) => {
    try {
      console.log('正在加载关卡详情:', levelId)
      const levelDetail = await weixinApi.getLevelDetail(levelId)
      currentLevelDetail.value = levelDetail

      // 设置当前关卡信息
      currentLevel.value = {
        id: levelDetail.id,
        name: levelDetail.name,
        wordsCount: levelDetail.phrases?.length || 8,
      }

      console.log('关卡详情加载成功:', levelDetail)
    } catch (error) {
      console.error('加载关卡详情失败:', error)
      throw error
    }
  }

  /**
   * 记录游戏开始
   */
  const recordGameStart = async (levelId: string) => {
    try {
      if (userInfo.value && userInfo.value.id) {
        await weixinApi.startGame(userInfo.value.id, levelId)
        console.log('游戏开始记录成功')
      }
    } catch (error) {
      console.error('记录游戏开始失败:', error)
      // 不阻断游戏流程
    }
  }

  /**
   * 加载词库数据作为备用
   */
  const loadLibraryDataAsFallback = async () => {
    const libraryData = uni.getStorageSync("selectedLibrary")
    if (libraryData) {
      try {
        selectedLibraryInfo.value = JSON.parse(libraryData)

        // 获取当前关卡进度
        const savedLevelId = getCurrentLevelId(selectedLibraryInfo.value.id)
        currentLevelId.value = savedLevelId

        // 设置当前关卡信息
        currentLevel.value = {
          id: currentLevelId.value,
          name: `第${currentLevelId.value}关`,
          wordsCount: 8,
        }

        console.log('使用词库数据作为备用:', selectedLibraryInfo.value)
        resetGame()
      } catch (e) {
        console.error("Failed to parse selected library data:", e)
        uni.showToast({ title: "加载数据失败", icon: "none" })
        goBackHome()
      }
    } else {
      uni.showToast({ title: "未选择关卡或词库", icon: "none" })
      goBackHome()
    }
  }

  /**
   * 加载用户信息
   */
  const loadUserInfo = async () => {
    try {
      const localUserInfo = weixinApi.getLocalUserInfo()
      if (localUserInfo) {
        userInfo.value = localUserInfo
        console.log('游戏页面获取到用户信息:', localUserInfo)
      }
    } catch (error) {
      console.error('加载用户信息失败:', error)
    }
  }

  // 获取当前关卡进度
  const getCurrentLevelId = (libraryId: number): number => {
    const key = `currentLevel_${libraryId}`
    const saved = uni.getStorageSync(key)
    return saved ? parseInt(saved) : 1 // 默认第一关
  }

  // 保存当前关卡进度
  const saveCurrentLevelId = (libraryId: number, levelId: number): void => {
    const key = `currentLevel_${libraryId}`
    uni.setStorageSync(key, levelId.toString())
  }

  // 获取卡片尺寸的函数（设置最小宽度）
  const getCardSize = (wordLength: number): { width: number; height: number } => {
    const minWidth = 160 // 设置最小宽度
    if (wordLength <= 4) {
      return { width: Math.max(minWidth, 140), height: 100 }
    } else if (wordLength <= 7) {
      return { width: Math.max(minWidth, 160), height: 110 }
    } else {
      return { width: Math.max(minWidth, 180), height: 120 }
    }
  }

  // 检查两个矩形是否重叠（精确的碰撞检测）
  const isOverlapping = (rect1, rect2, safeDistance = 25) => {
    // 使用更大的安全间距，确保卡片之间绝对不会重叠
    return !(
      rect1.x + rect1.width + safeDistance <= rect2.x ||
      rect2.x + rect2.width + safeDistance <= rect1.x ||
      rect1.y + rect1.height + safeDistance <= rect2.y ||
      rect2.y + rect2.height + safeDistance <= rect1.y
    );
  };

  // 检查卡片是否超出边界
  const isOutOfBounds = (rect, containerWidth, containerHeight, margin = 25) => {
    return (
      rect.x < margin ||
      rect.y < margin ||
      rect.x + rect.width > containerWidth - margin ||
      rect.y + rect.height > containerHeight - margin
    );
  };

  // 获取游戏区域的实际尺寸
  const getGameBoardDimensions = () => {
    try {
      // 基于屏幕宽度动态计算游戏区域尺寸
      const systemInfo = uni.getSystemInfoSync();
      const screenWidth = systemInfo.screenWidth || 375; // 默认iPhone宽度
      const screenHeight = systemInfo.screenHeight || 667; // 默认iPhone高度

      // 基于新的网格算法计算所需空间
      const cardWidth = 160; // 统一卡片宽度（最小宽度）
      const cardHeight = 110; // 平均卡片高度
      const spacing = 20; // 卡片间距
      const margin = 30; // 边距

      // 计算4列4行网格所需的最小尺寸
      const cols = 4;
      const rows = 4;
      const minRequiredWidth = cols * cardWidth + (cols - 1) * spacing + margin * 2;
      const minRequiredHeight = rows * cardHeight + (rows - 1) * spacing + margin * 2;

      // CSS限制
      const cssMaxWidth = 750; // 对应CSS中的max-width: 750rpx
      const cssPadding = 25 * 4; // 对应CSS中的padding: 25rpx
      const actualGameAreaWidth = cssMaxWidth - cssPadding; // 实际可用宽度 = 700rpx

      // 确保容器宽度不超过CSS限制，但满足最小要求
      let containerWidth = Math.min(actualGameAreaWidth, minRequiredWidth);

      // 如果CSS限制太小，调整为紧凑布局
      if (containerWidth < minRequiredWidth) {
        console.warn('CSS宽度限制过小，使用紧凑布局');
        const compactSpacing = 15;
        containerWidth = Math.min(actualGameAreaWidth,
          cols * cardWidth + (cols - 1) * compactSpacing + margin * 2);
      }

      // 游戏区域高度：确保能容纳4行卡片
      const maxHeight = Math.floor(screenHeight * 0.7 * (750 / screenWidth));
      let containerHeight = Math.max(minRequiredHeight, Math.floor(containerWidth * 1.2));
      containerHeight = Math.min(containerHeight, maxHeight);

      console.log(`� 随机定位参数:`);
      console.log(`   - 卡片尺寸: ${cardWidth}×${cardHeight}rpx`);
      console.log(`   - 总卡片数: 16张`);
      console.log(`   - 安全距离: 25rpx, 边距: ${margin}rpx`);
      console.log(`   - 最小要求: ${minRequiredWidth}×${minRequiredHeight}rpx`);
      console.log(`   - CSS限制: ${actualGameAreaWidth}rpx`);
      console.log(`   - 实际尺寸: ${containerWidth}×${containerHeight}rpx`);

      return {
        containerWidth: containerWidth,
        containerHeight: containerHeight
      };
    } catch (error) {
      console.warn('获取屏幕信息失败，使用默认尺寸:', error);
      return { containerWidth: 700, containerHeight: 900 }; // 默认尺寸不超过CSS限制(750-50=700)
    }
  };

  // 规则定位算法 - 按照指定规则确保不重复
  const generateRuleBasedPosition = (
    cardWidth,
    cardHeight,
    cardIndex,
    containerWidth,
    containerHeight
  ) => {
    const margin = 30; // 边距

    // 根据您的要求：x轴相差卡片高度，y轴相差卡片宽度/2
    const xStep = cardHeight; // x轴步长 = 卡片高度
    const yStep = cardWidth / 2; // y轴步长 = 卡片宽度/2

    console.log(`定位参数: 卡片尺寸=${cardWidth}×${cardHeight}, x步长=${xStep}, y步长=${yStep}`);

    // 计算可用区域
    const availableWidth = containerWidth - margin * 2 - cardWidth;
    const availableHeight = containerHeight - margin * 2 - cardHeight;

    // 计算每行和每列可以放置的卡片数量
    const maxCols = Math.floor(availableWidth / xStep) + 1;
    const maxRows = Math.floor(availableHeight / yStep) + 1;

    console.log(`容器尺寸: ${containerWidth}×${containerHeight}, 可用区域: ${availableWidth}×${availableHeight}`);
    console.log(`最大布局: ${maxCols}列 × ${maxRows}行 = ${maxCols * maxRows}个位置`);

    // 如果空间不足，使用紧凑布局
    if (maxCols * maxRows < 16) {
      console.warn(`空间不足，需要${16}个位置，但只能提供${maxCols * maxRows}个位置，使用紧凑布局`);
      return generateCompactPosition(cardWidth, cardHeight, cardIndex, containerWidth, containerHeight);
    }

    // 计算当前卡片的行列位置
    const col = cardIndex % maxCols;
    const row = Math.floor(cardIndex / maxCols);

    // 计算实际位置
    let x = margin + col * xStep;
    let y = margin + row * yStep;

    // 确保不超出边界
    x = Math.max(margin, Math.min(x, containerWidth - cardWidth - margin));
    y = Math.max(margin, Math.min(y, containerHeight - cardHeight - margin));

    console.log(`卡片${cardIndex + 1}: 规则定位 -> 第${row + 1}行第${col + 1}列 -> 位置(${Math.round(x)}, ${Math.round(y)})`);

    return { x: Math.round(x), y: Math.round(y) };
  };

  // 随机定位算法 - 作为备用方案
  const generateRandomPosition = (
    cardWidth,
    cardHeight,
    existingPositions,
    containerWidth,
    containerHeight
  ) => {
    const margin = 30; // 边距
    const safeDistance = 25; // 卡片间安全距离
    const maxAttempts = 500; // 增加最大尝试次数

    // 计算可用区域
    const availableWidth = containerWidth - margin * 2 - cardWidth;
    const availableHeight = containerHeight - margin * 2 - cardHeight;

    // 确保有足够空间
    if (availableWidth <= 0 || availableHeight <= 0) {
      console.warn('容器空间不足，使用紧凑布局');
      return generateCompactPosition(cardWidth, cardHeight, existingPositions.length, containerWidth, containerHeight);
    }

    // 智能随机生成位置
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      let x, y;

      // 前50%尝试：完全随机
      if (attempt < maxAttempts * 0.5) {
        x = margin + Math.random() * availableWidth;
        y = margin + Math.random() * availableHeight;
      }
      // 后50%尝试：分区域随机，避开已占用区域
      else {
        // 将容器分成多个区域，优先选择空旷区域
        const gridSize = 100; // 网格大小
        const gridCols = Math.ceil(containerWidth / gridSize);
        const gridRows = Math.ceil(containerHeight / gridSize);

        // 找到相对空旷的网格区域
        let bestGridX = Math.floor(Math.random() * gridCols);
        let bestGridY = Math.floor(Math.random() * gridRows);

        // 在选定的网格区域内随机生成位置
        const gridStartX = bestGridX * gridSize;
        const gridStartY = bestGridY * gridSize;
        const gridEndX = Math.min((bestGridX + 1) * gridSize, containerWidth - margin);
        const gridEndY = Math.min((bestGridY + 1) * gridSize, containerHeight - margin);

        x = Math.max(margin, gridStartX + Math.random() * Math.max(0, gridEndX - gridStartX - cardWidth));
        y = Math.max(margin, gridStartY + Math.random() * Math.max(0, gridEndY - gridStartY - cardHeight));
      }

      const newRect = { x, y, width: cardWidth, height: cardHeight };

      // 检查是否与现有卡片重叠
      let overlapping = false;
      for (const existingPos of existingPositions) {
        if (isOverlapping(newRect, existingPos, safeDistance)) {
          overlapping = true;
          break;
        }
      }

      // 检查是否超出边界
      if (!overlapping && !isOutOfBounds(newRect, containerWidth, containerHeight, margin)) {
        const strategy = attempt < maxAttempts * 0.5 ? "完全随机" : "分区随机";
        console.log(`卡片${existingPositions.length + 1}: ${strategy}定位成功 -> 位置(${Math.round(x)}, ${Math.round(y)}) [尝试${attempt + 1}次]`);
        return { x: Math.round(x), y: Math.round(y) };
      }
    }

    // 如果随机定位失败，使用紧凑布局
    console.warn(`随机定位失败，使用紧凑布局放置第${existingPositions.length + 1}张卡片`);
    return generateCompactPosition(cardWidth, cardHeight, existingPositions.length, containerWidth, containerHeight);
  };

  // 紧凑布局后备方案
  const generateCompactPosition = (cardWidth, cardHeight, cardIndex, containerWidth, containerHeight) => {
    const margin = 25;
    const spacing = 15; // 紧凑间距

    // 计算网格参数
    const availableWidth = containerWidth - margin * 2;
    const cellWidth = cardWidth + spacing;
    const cellHeight = cardHeight + spacing;

    const cols = Math.max(3, Math.floor(availableWidth / cellWidth));
    const col = cardIndex % cols;
    const row = Math.floor(cardIndex / cols);

    let x = margin + col * cellWidth;
    let y = margin + row * cellHeight;

    // 确保不超出边界
    x = Math.max(margin, Math.min(x, containerWidth - cardWidth - margin));
    y = Math.max(margin, Math.min(y, containerHeight - cardHeight - margin));

    console.log(`卡片${cardIndex + 1}: 紧凑布局 -> 位置(${Math.round(x)}, ${Math.round(y)})`);

    return { x: Math.round(x), y: Math.round(y) };
  };



  // 验证卡片布局质量 - 针对规则定位算法优化
  const validateCardLayout = (cards, containerWidth, containerHeight) => {
    let overlappingCount = 0;
    let outOfBoundsCount = 0;
    let ruleViolationCount = 0;
    const margin = 30; // 边距
    const expectedXStep = 110; // 期望的x轴步长（卡片高度）
    const expectedYStep = 80;  // 期望的y轴步长（卡片宽度/2）

    console.log(`开始验证 ${cards.length} 张卡片的规则定位布局质量...`);
    console.log(`期望步长: x轴=${expectedXStep}rpx, y轴=${expectedYStep}rpx`);

    for (let i = 0; i < cards.length; i++) {
      const card1 = cards[i];

      // 检查是否超出边界（考虑边距）
      const cardRight = card1.position.x + card1.cardSize.width;
      const cardBottom = card1.position.y + card1.cardSize.height;

      if (card1.position.x < margin || card1.position.y < margin ||
          cardRight > containerWidth - margin || cardBottom > containerHeight - margin) {
        outOfBoundsCount++;
        console.error(`❌ 卡片${i + 1}(${card1.type}:${card1.word[card1.type]})超出安全边界:`, {
          position: card1.position,
          size: card1.cardSize,
          cardRight: cardRight,
          cardBottom: cardBottom,
          containerWidth: containerWidth,
          containerHeight: containerHeight,
          margin: margin,
          超出左边界: card1.position.x < margin,
          超出上边界: card1.position.y < margin,
          超出右边界: cardRight > containerWidth - margin,
          超出下边界: cardBottom > containerHeight - margin
        });
      }

      // 验证定位规则：检查相邻卡片的间距是否符合规则
      if (i > 0) {
        const prevCard = cards[i - 1];
        const xDiff = Math.abs(card1.position.x - prevCard.position.x);
        const yDiff = Math.abs(card1.position.y - prevCard.position.y);

        // 检查是否符合定位规则（允许一定的误差范围）
        const tolerance = 5; // 允许5rpx的误差
        const isValidXStep = Math.abs(xDiff - expectedXStep) <= tolerance || xDiff === 0;
        const isValidYStep = Math.abs(yDiff - expectedYStep) <= tolerance || yDiff === 0;

        if (!isValidXStep && !isValidYStep && xDiff > 0 && yDiff > 0) {
          ruleViolationCount++;
          console.warn(`⚠️ 卡片${i + 1}与卡片${i}的间距不符合规则:`, {
            实际间距: `x=${xDiff}rpx, y=${yDiff}rpx`,
            期望间距: `x=${expectedXStep}rpx 或 y=${expectedYStep}rpx`,
            卡片1位置: prevCard.position,
            卡片2位置: card1.position
          });
        }
      }

      // 检查与其他卡片是否重叠（规则定位算法应该不会重叠）
      for (let j = i + 1; j < cards.length; j++) {
        const card2 = cards[j];

        // 检查位置是否完全相同（这在规则定位中不应该发生）
        if (card1.position.x === card2.position.x && card1.position.y === card2.position.y) {
          overlappingCount++;
          console.error(`❌ 卡片${i + 1}(${card1.type})和卡片${j + 1}(${card2.type})位置完全重叠:`,
            `位置: (${card1.position.x}, ${card1.position.y})`
          );
        }
      }
    }

    // 输出验证结果
    console.log(`📊 规则定位布局质量检查结果:`);
    console.log(`   - 总卡片数: ${cards.length}`);
    console.log(`   - 超出边界: ${outOfBoundsCount}张`);
    console.log(`   - 位置重叠: ${overlappingCount}对`);
    console.log(`   - 规则违反: ${ruleViolationCount}处`);
    console.log(`   - 容器尺寸: ${containerWidth}×${containerHeight}rpx`);

    if (overlappingCount === 0 && outOfBoundsCount === 0 && ruleViolationCount === 0) {
      console.log('✅ 规则定位布局质量完美，符合所有定位规则');
      return true;
    } else {
      console.warn('⚠️ 规则定位布局存在问题，需要检查算法实现');
      return false;
    }
  };

  const initializeGameBoard = () => {
    const words = wordsForCurrentLevel.value;
    if (words.length < 8) {
      uni.showToast({ title: "词汇数量不足", icon: "none" });
      return;
    }

    // 获取动态游戏区域尺寸
    const { containerWidth, containerHeight } = getGameBoardDimensions();

    console.log(`游戏区域尺寸: ${containerWidth}rpx × ${containerHeight}rpx`);

    // 创建分离的英文和中文卡片
    const cards = [];

    // 使用统一的卡片尺寸以确保规则定位算法正常工作
    const standardCardWidth = 160;
    const standardCardHeight = 110;

    console.log(`使用标准卡片尺寸: ${standardCardWidth}×${standardCardHeight}rpx`);

    // 预先计算所有卡片的颜色
    const cardData = words.slice(0, 8).map(word => ({
      word,
      color: generateRandomLightColor() // 每对单词使用相同颜色
    }));

    // 使用规则定位算法创建所有卡片
    let cardIndex = 0;

    for (let i = 0; i < 8; i++) {
      const { word, color } = cardData[i];

      // 创建英文卡片
      const englishPosition = generateRuleBasedPosition(
        standardCardWidth,
        standardCardHeight,
        cardIndex++,
        containerWidth,
        containerHeight
      );

      cards.push({
        id: tileIdCounter++,
        word: word,
        color: color,
        selected: false,
        matched: false,
        pairId: i, // 用于标识配对
        type: 'english', // 标识为英文卡片
        position: englishPosition,
        cardSize: { width: standardCardWidth, height: standardCardHeight },
      });

      // 创建中文卡片
      const chinesePosition = generateRuleBasedPosition(
        standardCardWidth,
        standardCardHeight,
        cardIndex++,
        containerWidth,
        containerHeight
      );

      cards.push({
        id: tileIdCounter++,
        word: word,
        color: color,
        selected: false,
        matched: false,
        pairId: i, // 相同的pairId表示是一对
        type: 'chinese', // 标识为中文卡片
        position: chinesePosition,
        cardSize: { width: standardCardWidth, height: standardCardHeight },
      });
    }

    // 分别打乱英文和中文卡片的顺序（保持位置不变）
    const englishCards = cards.filter(card => card.type === 'english');
    const chineseCards = cards.filter(card => card.type === 'chinese');

    // 打乱英文卡片的内容分配
    for (let i = englishCards.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      // 只交换卡片的单词内容和颜色，不交换位置
      const tempWord = englishCards[i].word;
      const tempColor = englishCards[i].color;
      const tempPairId = englishCards[i].pairId;

      englishCards[i].word = englishCards[j].word;
      englishCards[i].color = englishCards[j].color;
      englishCards[i].pairId = englishCards[j].pairId;

      englishCards[j].word = tempWord;
      englishCards[j].color = tempColor;
      englishCards[j].pairId = tempPairId;
    }

    // 打乱中文卡片的内容分配
    for (let i = chineseCards.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      // 只交换卡片的单词内容和颜色，不交换位置
      const tempWord = chineseCards[i].word;
      const tempColor = chineseCards[i].color;
      const tempPairId = chineseCards[i].pairId;

      chineseCards[i].word = chineseCards[j].word;
      chineseCards[i].color = chineseCards[j].color;
      chineseCards[i].pairId = chineseCards[j].pairId;

      chineseCards[j].word = tempWord;
      chineseCards[j].color = tempColor;
      chineseCards[j].pairId = tempPairId;
    }

    gameBoard.value = cards;
    totalPairs.value = 8;
    matchedPairs.value = 0;

    // 验证卡片布局质量
    validateCardLayout(cards, containerWidth, containerHeight);

    // 输出调试信息
    console.log(`成功生成 ${cards.length} 张卡片，位置分布:`);

    console.log(`英文卡片 (${englishCards.length}张):`);
    englishCards.forEach((card, index) => {
      console.log(`  英文${index + 1}: ${card.word.english} (pairId:${card.pairId}) 位置(${card.position.x}, ${card.position.y})`);
    });

    console.log(`中文卡片 (${chineseCards.length}张):`);
    chineseCards.forEach((card, index) => {
      console.log(`  中文${index + 1}: ${card.word.chinese} (pairId:${card.pairId}) 位置(${card.position.x}, ${card.position.y})`);
    });
  };

  const handleTileClick = (index) => {
    // 如果正在检查匹配或游戏结束，不允许点击
    if (isChecking.value || isGameEndModalVisible.value) return;

    // 播放点击音效
    audioManager.playSoundEffect('click')

    const tile = gameBoard.value[index];

    // 如果卡片已经匹配，不允许点击
    if (tile.matched) return;

    // 如果已经选择了两张卡片，不允许再选择
    if (selectedTiles.value.length >= 2) return;

    // 如果点击的是已经选中的卡片，取消选择
    if (tile.selected) {
      tile.selected = false;
      selectedTiles.value = selectedTiles.value.filter(
        (item) => item.index !== index
      );
      return;
    }

    // 选择卡片
    tile.selected = true;
    selectedTiles.value.push({ index, tile });

    // 如果选择了两张卡片，检查是否匹配
    if (selectedTiles.value.length === 2) {
      isChecking.value = true;
      setTimeout(() => {
        checkMatch();
      }, 500); // 延迟0.5秒让用户看清楚选择
    }
  };

  const checkMatch = () => {
    const [tile1, tile2] = selectedTiles.value;

    // 检查是否为英文和中文的配对（不能是相同类型）
    const isValidPair = (tile1.tile.type !== tile2.tile.type) &&
                       (tile1.tile.pairId === tile2.tile.pairId);

    if (isValidPair) {
      // 匹配成功：英文和对应的中文配对
      tile1.tile.matched = true;
      tile2.tile.matched = true;
      tile1.tile.selected = false;
      tile2.tile.selected = false;

      matchedPairs.value++;

      const englishWord = tile1.tile.type === 'english' ? tile1.tile.word.english : tile2.tile.word.english;
      const chineseWord = tile1.tile.type === 'chinese' ? tile1.tile.word.chinese : tile2.tile.word.chinese;

      // 播放成功音效和震动
      audioManager.playSoundEffect('success')
      audioManager.vibrate('short')

      console.log(`配对成功！已完成 ${matchedPairs.value}/${totalPairs.value} 对`)

      // 检查是否完成游戏
      if (matchedPairs.value === totalPairs.value) {
        console.log("🎉 恭喜！所有配对完成！")

        // 播放完成音效和长震动
        setTimeout(() => {
          audioManager.playSoundEffect('complete')
          audioManager.vibrate('long')
        }, 500)

        setTimeout(() => {
          endGame(true);
        }, 1000);
      }
    } else {
      // 匹配失败，直接重置当前关卡

      // 播放失败音效
      audioManager.playSoundEffect('fail')

      // 显示重置提示
      showError("匹配错误，已重置本关", 1500)

      // 延迟重置游戏，让用户看到提示信息
      setTimeout(() => {
        resetGame()
      }, 1500)
    }

    // 清除选择状态
    selectedTiles.value = [];
    isChecking.value = false;
  };

  // 返回首页
  const goBackHome = () => {
    uni.navigateBack({ delta: 1 });
  };

  /**
   * 检测开发环境
   */
  const checkDevelopmentEnvironment = () => {
    try {
      isDevelopment.value = shouldShowDebugFeatures()
      console.log('开发环境检测结果:', isDevelopment.value)
    } catch (error) {
      console.error('检测开发环境失败:', error)
      // 默认不显示调试按钮
      isDevelopment.value = false
    }
  }

  /**
   * 跳转到调试页面
   */
  const goToDebug = () => {
    // 播放点击音效
    audioManager.playSoundEffect('click')

    uni.navigateTo({
      url: '/pages/debug/index'
    })
  }

  /**
   * 显示设置弹窗
   */
  const showSettings = () => {
    // 播放点击音效
    audioManager.playSoundEffect('click')

    showSettingsModal.value = true
    console.log('显示设置弹窗')
  }

  /**
   * 关闭设置弹窗
   */
  const closeSettings = () => {
    showSettingsModal.value = false
    console.log('关闭设置弹窗')
  }

  /**
   * 处理设置变更
   */
  const handleSettingsChange = (newSettings: GameSettings) => {
    gameSettings.value = { ...newSettings }
    console.log('设置已更新:', newSettings)

    // 如果背景音乐设置发生变化
    if (newSettings.backgroundMusic !== gameSettings.value.backgroundMusic) {
      if (newSettings.backgroundMusic) {
        // 开启背景音乐
        audioManager.playBackgroundMusic('game')
      } else {
        // 关闭背景音乐
        audioManager.stopBackgroundMusic()
      }
    }
  }

  // 重置游戏
  const resetGame = () => {
    selectedTiles.value = [];
    matchedPairs.value = 0;
    isChecking.value = false;
    isGameEndModalVisible.value = false;
    initializeGameBoard();
  };

  // 下一关
  const nextLevel = () => {
    if (canGoToNextLevel.value) {
      currentLevelId.value++;
      currentLevel.value = {
        id: currentLevelId.value,
        name: `第${currentLevelId.value}关`,
        wordsCount: 8,
      };

      // 保存进度
      if (selectedLibraryInfo.value) {
        saveCurrentLevelId(selectedLibraryInfo.value.id, currentLevelId.value);
      }

      // 重置游戏
      resetGame();

      showSuccess(`进入第${currentLevelId.value}关`, 1000)
    } else {
      showError("已经是最后一关了", 1000)
    }
  };

  const endGame = async (won) => {
    gameWon.value = won
    gameResultText.value = won ? "恭喜过关！" : "挑战失败！"
    isGameEndModalVisible.value = true

    // 如果游戏胜利，保存关卡完成状态并同步到服务器
    if (won) {
      await handleGameCompletion()
    }
  }

  /**
   * 处理游戏完成逻辑
   */
  const handleGameCompletion = async () => {
    try {
      isProgressSyncing.value = true

      // 1. 保存本地进度（保持兼容性）
      const selectedLevel = uni.getStorageSync("selectedLevel")
      if (selectedLevel) {
        try {
          const levelData = JSON.parse(selectedLevel)
          const completedKey = `level_${levelData.id}_completed`
          uni.setStorageSync(completedKey, "true")
          console.log(`Level ${levelData.id} marked as completed locally`)
        } catch (e) {
          console.error("Failed to save local level completion:", e)
        }
      }

      // 2. 调用通关接口
      await callCompleteLevelAPI()

      // 3. 刷新用户信息
      await refreshUserInfo()

    } catch (error) {
      console.error('处理游戏完成失败:', error)
      // 即使同步失败，也不影响游戏体验
    } finally {
      isProgressSyncing.value = false
    }
  }

  /**
   * 调用通关关卡接口
   */
  const callCompleteLevelAPI = async () => {
    try {
      if (!userInfo.value || !userInfo.value.id) {
        console.warn('用户信息不存在，跳过通关接口调用')
        return
      }

      let levelId = ''

      // 优先使用关卡详情中的ID
      if (currentLevelDetail.value) {
        levelId = currentLevelDetail.value.id
      }
      // 备用：使用选择的关卡数据
      else if (selectedLevelData.value) {
        levelId = selectedLevelData.value.id
      }
      // 最后备用：使用当前关卡ID
      else if (currentLevel.value) {
        levelId = currentLevel.value.id.toString()
      }

      if (!levelId) {
        console.warn('未找到关卡ID，跳过通关接口调用')
        return
      }

      console.log('调用通关接口:', {
        userId: userInfo.value.id,
        levelId: levelId
      })

      const completeLevelResponse = await weixinApi.completeLevel(userInfo.value.id, levelId)

      // 更新关卡详情的完成状态
      if (currentLevelDetail.value) {
        currentLevelDetail.value.isCompleted = true
      }

      console.log('通关接口调用成功:', completeLevelResponse)

      // 检查是否解锁了新关卡
      if (completeLevelResponse.hasUnlockedNewLevel) {
        // 播放解锁音效
        audioManager.playSoundEffect('unlock')

        // 显示解锁新关卡的提示
        uni.showModal({
          title: '恭喜通关！',
          content: `${completeLevelResponse.message}\n已解锁 ${completeLevelResponse.unlockedLevelss} 关！`,
          showCancel: false,
          confirmText: '太棒了'
        })
      } else {
        // 检查每日解锁限制
        if (!completeLevelResponse.isVip && completeLevelResponse.remainingUnlocks <= 0) {
          uni.showModal({
            title: '通关成功',
            content: `${completeLevelResponse.message}\n今日解锁次数已用完，明天再来或分享获得额外机会！`,
            showCancel: true,
            cancelText: '明天再来',
            confirmText: '立即分享',
            success: (res) => {
              if (res.confirm) {
                // 触发分享
                uni.showShareMenu({
                  withShareTicket: true
                })
              }
            }
          })
        } else {
          // 显示通关成功提示
          showSuccess('恭喜通关！进度已同步', 2000)
        }
      }

      // 刷新用户信息以获取最新状态
      await refreshUserInfo()

    } catch (error) {
      console.error('调用通关接口失败:', error)
      // 不抛出错误，避免影响游戏体验
      showError('通关记录失败，但不影响游戏', 2000)
    }
  }



  /**
   * 刷新用户信息
   */
  const refreshUserInfo = async () => {
    try {
      const freshUserInfo = await weixinApi.refreshUserInfo()
      if (freshUserInfo) {
        userInfo.value = freshUserInfo

        // 保存更新后的用户信息到本地存储
        uni.setStorageSync('userInfo', JSON.stringify(freshUserInfo))

        console.log('用户信息已刷新并保存到本地:', freshUserInfo)
      }
    } catch (error) {
      console.error('刷新用户信息失败:', error)
    }
  }

  /**
   * 处理游戏页面分享
   * 根据uniapp官方文档实现
   */
  const onShareAppMessage = async (options: any) => {
    console.log('游戏页面分享触发:', options)

    // 构建分享参数
    const shareParams = {
      page: 'pages/game/index',
      levelId: currentLevelDetail.value?.id || selectedLevelData.value?.id,
      userId: userInfo.value?.id
    }

    // 处理分享奖励
    if (userInfo.value?.id) {
      handleGameShareReward(options, shareParams)
    }

    return await shareUtils.handleShareAppMessage(options, shareParams)
  }

  /**
   * 检查今日是否已经获取过分享奖励
   */
  const checkDailyShareReward = (userId: string): boolean => {
    try {
      const today = new Date().toDateString() // 获取今日日期字符串，如 "Mon Dec 25 2023"
      const storageKey = `daily_share_reward_${userId}_${today}`
      const hasSharedToday = uni.getStorageSync(storageKey)

      console.log(`检查每日分享奖励状态 - 用户: ${userId}, 日期: ${today}, 已分享: ${!!hasSharedToday}`)
      return !!hasSharedToday
    } catch (error) {
      console.error('检查每日分享奖励状态失败:', error)
      return false
    }
  }

  /**
   * 标记今日已获取分享奖励
   */
  const markDailyShareReward = (userId: string): void => {
    try {
      const today = new Date().toDateString()
      const storageKey = `daily_share_reward_${userId}_${today}`
      uni.setStorageSync(storageKey, true)

      console.log(`标记每日分享奖励完成 - 用户: ${userId}, 日期: ${today}`)
    } catch (error) {
      console.error('标记每日分享奖励失败:', error)
    }
  }

  /**
   * 处理游戏页面分享奖励
   */
  const handleGameShareReward = async (options: any, shareParams: any) => {
    try {
      if (!userInfo.value?.id) {
        console.warn('用户信息不存在，无法获取分享奖励')
        return
      }

      // 防止重复执行
      if (isHandlingGameShareReward) {
        console.log('游戏分享奖励正在处理中，跳过重复请求')
        return
      }

      // 检查今日是否已经获取过分享奖励
      const hasSharedToday = checkDailyShareReward(userInfo.value.id)
      if (hasSharedToday) {
        console.log('今日已获取过分享奖励，跳过本次请求')
        uni.showToast({
          title: '今日已获得分享奖励',
          icon: 'none',
          duration: 2000
        })
        return
      }

      // 标记开始处理
      isHandlingGameShareReward = true
      console.log('开始处理游戏页面分享奖励:', options)

      // 延迟获取奖励，确保分享完成
      setTimeout(async () => {
        try {
          const rewardResponse = await weixinApi.getShareReward({
            userId: userInfo.value!.id,
            shareType: 'app_message',
            page: shareParams.page,
            levelId: shareParams.levelId,
            timestamp: Date.now()
          })

          if (rewardResponse.success) {
            // 标记今日已获取分享奖励
            markDailyShareReward(userInfo.value!.id)

            // 更新本地用户信息
            if (rewardResponse.userInfo) {
              userInfo.value = rewardResponse.userInfo
              // 保存到本地存储
              uni.setStorageSync('userInfo', JSON.stringify(rewardResponse.userInfo))
            }

            // 显示奖励获得提示
            uni.showModal({
              title: '分享奖励',
              content: `恭喜获得${rewardResponse.reward.description}！可以继续挑战更多关卡了！今日分享奖励已领取完毕。`,
              showCancel: false,
              confirmText: '太棒了',
              success: () => {
                console.log('游戏分享奖励提示已显示')
              }
            })

            console.log('游戏分享奖励获取成功:', rewardResponse.reward)
          } else {
            console.log('游戏分享奖励获取失败:', rewardResponse.message)

            // 如果服务端返回今日已领取的消息，也标记本地状态
            if (rewardResponse.message.includes('今日') || rewardResponse.message.includes('已领取')) {
              markDailyShareReward(userInfo.value!.id)
            }
          }
        } catch (error) {
          console.error('获取游戏分享奖励失败:', error)
          // 不显示错误提示，避免影响用户体验
        } finally {
          // 重置处理状态
          isHandlingGameShareReward = false
          console.log('游戏分享奖励处理完成，重置状态')
        }
      }, 2000) // 延迟2秒，确保分享完成
    } catch (error) {
      console.error('处理游戏分享奖励失败:', error)
      // 重置处理状态
      isHandlingGameShareReward = false
    }
  }

  // 导出分享函数供小程序使用
  defineExpose({
    onShareAppMessage
  })
</script>

<style scoped>
  .page-box {
    height: calc(100vh - 44px);
    background-color: #fdf9f9; 
  }

  .game-page-container {
    padding: 16rpx;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
  }

  .card {
    /* Common card style */
    background-color: white;
    border-radius: 24rpx; /* rounded-xl or 2xl */
    padding: 16rpx;
    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  }

  .library-name {
    display: block;
    font-size: 36rpx; /* text-xl */
    font-weight: 600; /* font-semibold */
    color: #111; /* text-purple-700 */
    margin-bottom: 8rpx;
  }

  /* 删除了关卡选择相关的样式 */
  .game-area {
    /* padding already from .card */
    margin-top: 32rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
  }
  .game-info-bar {
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 100%;
    padding: 10rpx 0;
    margin-bottom: 20rpx;
    font-size: 28rpx;
    color: #333;
  }

  .sync-status {
    font-size: 24rpx !important;
    color: #74b9ff !important;
    font-weight: 500;
  }

  .replay-btn {
    background: linear-gradient(135deg, #fd79a8, #e84393);
    color: white;
    padding: 8rpx 16rpx;
    border-radius: 16rpx;
    font-size: 24rpx;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 60rpx;
    text-align: center;
    box-shadow: 0 2rpx 8rpx rgba(232, 67, 147, 0.3);
  }

  .replay-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 4rpx 12rpx rgba(232, 67, 147, 0.4);
  }

  .replay-btn-text {
    color: white;
    font-size: 24rpx;
    font-weight: 500;
  }

  .debug-btn {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
    padding: 8rpx 16rpx;
    border-radius: 16rpx;
    font-size: 20rpx;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 60rpx;
    text-align: center;
    box-shadow: 0 2rpx 8rpx rgba(108, 117, 125, 0.3);
  }

  .debug-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 4rpx 12rpx rgba(108, 117, 125, 0.4);
  }

  .debug-btn-text {
    color: white;
    font-size: 20rpx;
    font-weight: 500;
  }
  .game-board {
    flex: 1;
    width: 100%;
    max-width: 750rpx;
    min-height: 800rpx; /* 增加最小高度，确保有足够空间 */
    height: auto;
    box-sizing: border-box;
    position: relative;
    margin: 0 auto 20rpx;
    padding: 25rpx; /* 增加内边距 */
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 20rpx;
    overflow: visible; /* 改为visible，避免裁剪卡片 */
    box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    border: 2rpx solid rgba(255, 255, 255, 0.8);
  }

  .board-tile {
    border-radius: 16rpx;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.15), 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
    border: 2rpx solid rgba(255, 255, 255, 0.8);
    padding: 12rpx 8rpx;
    backdrop-filter: blur(10rpx);
    position: absolute;
    z-index: 1;
    min-width: 160rpx;
    height: 35rpx;
    line-height: 35rpx;
  }

  .board-tile:hover {
    transform: scale(1.05) translateZ(0);
    box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.2), 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
    z-index: 2;
  }

  .board-tile.selected {
    transform: scale(1.08) translateZ(0);
    border-color: #ff6b35;
    box-shadow: 0 12rpx 24rpx rgba(255, 107, 53, 0.4), 0 6rpx 12rpx rgba(255, 107, 53, 0.2);
    z-index: 3;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 107, 53, 0.1));
  }

  .board-tile.matched {
    opacity: 0.7;
    transform: scale(0.95) translateZ(0);
    border-color: #28a745;
    box-shadow: 0 4rpx 12rpx rgba(40, 167, 69, 0.3);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(40, 167, 69, 0.1));
    z-index: 0;
  }

  /* 调试模式样式 */
  .board-tile.debug-mode {
    border: 3rpx dashed #dc3545 !important;
    background: rgba(220, 53, 69, 0.1) !important;
  }

  .board-tile.debug-mode::before {
    content: attr(data-position);
    position: absolute;
    top: -25rpx;
    left: 0;
    font-size: 16rpx;
    color: #dc3545;
    background: rgba(255, 255, 255, 0.9);
    padding: 2rpx 6rpx;
    border-radius: 8rpx;
    white-space: nowrap;
    z-index: 10;
  }

  .tile-word {
    font-weight: bold;
    color: #333;
    margin-bottom: 6rpx;
    text-align: center;
    line-height: 1.2;
    word-break: break-word;
  }

  /* 根据卡片大小调整英文字体大小 */
  .tile-short .tile-word {
    font-size: 24rpx;
  }

  .tile-medium .tile-word {
    font-size: 22rpx;
  }

  .tile-long .tile-word {
    font-size: 20rpx;
  }

  .tile-chinese {
    color: #666;
    text-align: center;
    line-height: 1.2;
    word-break: break-word;
  }

  /* 中文卡片专用样式 */
  .tile-chinese-only {
    font-weight: bold;
    color: #333;
    text-align: center;
    line-height: 1.2;
    word-break: break-word;
    font-size: 28rpx;
  }



  /* 根据卡片大小调整中文字体大小 */
  .tile-short .tile-chinese {
    font-size: 20rpx;
  }

  .tile-medium .tile-chinese {
    font-size: 18rpx;
  }

  .tile-long .tile-chinese {
    font-size: 16rpx;
  }

  /* 中文卡片字体大小调整 */
  .tile-short .tile-chinese-only {
    font-size: 26rpx;
  }

  .tile-medium .tile-chinese-only {
    font-size: 24rpx;
  }

  .tile-long .tile-chinese-only {
    font-size: 22rpx;
  }
  .game-controls {
    display: flex;
    justify-content: center;
    gap: 24rpx;
    width: 100%;
    margin-top: 20rpx;
  }
  .control-button {
    padding: 12rpx 24rpx;
    font-size: 24rpx;
    border-radius: 20rpx;
    border: none;
    background-color: #007aff;
    color: white;
    transition: background-color 0.3s ease;
    min-width: 120rpx;
  }

  .control-button:disabled {
    background-color: #ccc;
    color: #999;
  }

  .control-button:hover:not(:disabled) {
    background-color: #0056b3;
  }
  .game-start-text {
    display: block;
    font-size: 36rpx; /* text-xl */
    font-weight: bold;
  }
  /* Modal Styles (simplified) */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  .modal-content {
    background-color: white;
    padding: 40rpx;
    border-radius: 16rpx;
    text-align: center;
    min-width: 500rpx;
  }
  .modal-title {
    font-size: 36rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
    display: block;
  }
  .modal-buttons {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
    margin-top: 32rpx;
  }

  .modal-button {
    padding: 16rpx 32rpx;
    font-size: 28rpx;
    border-radius: 24rpx;
    border: none;
    background-color: #f0f0f0;
    color: #333;
    transition: background-color 0.3s ease;
  }

  .modal-button.primary {
    background-color: #007aff;
    color: white;
  }

  .modal-button:hover {
    opacity: 0.8;
  }

  /* 关卡详情信息样式 */
  .level-detail-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 24rpx;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);
  }

  .level-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12rpx;
  }

  .level-name {
    font-size: 32rpx;
    font-weight: bold;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  }

  .level-difficulty {
    font-size: 24rpx;
    background: rgba(255, 255, 255, 0.2);
    padding: 6rpx 12rpx;
    border-radius: 12rpx;
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
  }

  .level-description {
    font-size: 26rpx;
    opacity: 0.9;
    margin-bottom: 16rpx;
    line-height: 1.4;
  }

  .level-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .stats-item {
    font-size: 22rpx;
    background: rgba(255, 255, 255, 0.15);
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
  }

  /* 备用：词库信息样式 */
  .selected-library-info {
    color: white;
    padding: 20rpx;
    border-radius: 15rpx;
    margin-bottom: 20rpx;
    text-align: center;
  }

  .library-name {
    font-size: 32rpx;
    font-weight: bold;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  }

  /* 右上角设置按钮样式 */
  .floating-settings-btn {
     width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 998;
    transition: all 0.3s ease;
    position: absolute;
    top: 20rpx;
    right: 20rpx;
  }

  .floating-settings-btn:active {
    transform: scale(0.9);
  }

  .settings-icon {
    font-size: 32rpx;
    color: #ffffff;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  }

  /* 悬浮调试按钮样式 */
  .floating-debug-btn {
    position: fixed;
    bottom: 100rpx;
    right: 40rpx;
    width: 100rpx;
    height: 100rpx;
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8rpx 20rpx rgba(255, 107, 107, 0.4);
    z-index: 999;
    transition: all 0.3s ease;
    animation: debugPulse 2s infinite;
  }

  .floating-debug-btn:active {
    transform: scale(0.9);
    box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.6);
  }

  .debug-icon {
    font-size: 36rpx;
    color: #ffffff;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  }

  @keyframes debugPulse {
    0%, 100% {
      transform: scale(1);
      box-shadow: 0 8rpx 20rpx rgba(255, 107, 107, 0.4);
    }
    50% {
      transform: scale(1.05);
      box-shadow: 0 12rpx 24rpx rgba(255, 107, 107, 0.6);
    }
  }
</style>
