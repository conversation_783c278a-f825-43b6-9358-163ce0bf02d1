# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10c0

"@ampproject/remapping@npm:^2.1.2, @ampproject/remapping@npm:^2.2.0":
  version: 2.3.0
  resolution: "@ampproject/remapping@npm:2.3.0"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10c0/81d63cca5443e0f0c72ae18b544cc28c7c0ec2cea46e7cb888bb0e0f411a1191d0d6b7af798d54e30777d8d1488b2ec0732aac2be342d3d7d3ffd271c6f489ed
  languageName: node
  linkType: hard

"@antfu/utils@npm:^0.7.10":
  version: 0.7.10
  resolution: "@antfu/utils@npm:0.7.10"
  checksum: 10c0/98991f66a4752ef097280b4235b27d961a13a2c67ef8e5b716a120eb9823958e20566516711204e2bfb08f0b935814b715f49ecd79c3b9b93ce32747ac297752
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.23.5, @babel/code-frame@npm:^7.26.2, @babel/code-frame@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/code-frame@npm:7.27.1"
  dependencies:
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    js-tokens: "npm:^4.0.0"
    picocolors: "npm:^1.1.1"
  checksum: 10c0/5dd9a18baa5fce4741ba729acc3a3272c49c25cb8736c4b18e113099520e7ef7b545a4096a26d600e4416157e63e87d66db46aa3fbf0a5f2286da2705c12da00
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.22.6, @babel/compat-data@npm:^7.27.2":
  version: 7.27.5
  resolution: "@babel/compat-data@npm:7.27.5"
  checksum: 10c0/da2751fcd0b58eea958f2b2f7ff7d6de1280712b709fa1ad054b73dc7d31f589e353bb50479b9dc96007935f3ed3cada68ac5b45ce93086b7122ddc32e60dc00
  languageName: node
  linkType: hard

"@babel/core@npm:^7.23.3, @babel/core@npm:^7.23.9":
  version: 7.27.4
  resolution: "@babel/core@npm:7.27.4"
  dependencies:
    "@ampproject/remapping": "npm:^2.2.0"
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.27.3"
    "@babel/helper-compilation-targets": "npm:^7.27.2"
    "@babel/helper-module-transforms": "npm:^7.27.3"
    "@babel/helpers": "npm:^7.27.4"
    "@babel/parser": "npm:^7.27.4"
    "@babel/template": "npm:^7.27.2"
    "@babel/traverse": "npm:^7.27.4"
    "@babel/types": "npm:^7.27.3"
    convert-source-map: "npm:^2.0.0"
    debug: "npm:^4.1.0"
    gensync: "npm:^1.0.0-beta.2"
    json5: "npm:^2.2.3"
    semver: "npm:^6.3.1"
  checksum: 10c0/d2d17b106a8d91d3eda754bb3f26b53a12eb7646df73c2b2d2e9b08d90529186bc69e3823f70a96ec6e5719dc2372fb54e14ad499da47ceeb172d2f7008787b5
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.20.5, @babel/generator@npm:^7.27.3":
  version: 7.27.5
  resolution: "@babel/generator@npm:7.27.5"
  dependencies:
    "@babel/parser": "npm:^7.27.5"
    "@babel/types": "npm:^7.27.3"
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    jsesc: "npm:^3.0.2"
  checksum: 10c0/8f649ef4cd81765c832bb11de4d6064b035ffebdecde668ba7abee68a7b0bce5c9feabb5dc5bb8aeba5bd9e5c2afa3899d852d2bd9ca77a711ba8c8379f416f0
  languageName: node
  linkType: hard

"@babel/helper-annotate-as-pure@npm:^7.27.1":
  version: 7.27.3
  resolution: "@babel/helper-annotate-as-pure@npm:7.27.3"
  dependencies:
    "@babel/types": "npm:^7.27.3"
  checksum: 10c0/94996ce0a05b7229f956033e6dcd69393db2b0886d0db6aff41e704390402b8cdcca11f61449cb4f86cfd9e61b5ad3a73e4fa661eeed7846b125bd1c33dbc633
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.22.6, @babel/helper-compilation-targets@npm:^7.27.1, @babel/helper-compilation-targets@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/helper-compilation-targets@npm:7.27.2"
  dependencies:
    "@babel/compat-data": "npm:^7.27.2"
    "@babel/helper-validator-option": "npm:^7.27.1"
    browserslist: "npm:^4.24.0"
    lru-cache: "npm:^5.1.1"
    semver: "npm:^6.3.1"
  checksum: 10c0/f338fa00dcfea931804a7c55d1a1c81b6f0a09787e528ec580d5c21b3ecb3913f6cb0f361368973ce953b824d910d3ac3e8a8ee15192710d3563826447193ad1
  languageName: node
  linkType: hard

"@babel/helper-create-class-features-plugin@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-create-class-features-plugin@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-member-expression-to-functions": "npm:^7.27.1"
    "@babel/helper-optimise-call-expression": "npm:^7.27.1"
    "@babel/helper-replace-supers": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/4ee199671d6b9bdd4988aa2eea4bdced9a73abfc831d81b00c7634f49a8fc271b3ceda01c067af58018eb720c6151322015d463abea7072a368ee13f35adbb4c
  languageName: node
  linkType: hard

"@babel/helper-create-regexp-features-plugin@npm:^7.18.6, @babel/helper-create-regexp-features-plugin@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-create-regexp-features-plugin@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    regexpu-core: "npm:^6.2.0"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/591fe8bd3bb39679cc49588889b83bd628d8c4b99c55bafa81e80b1e605a348b64da955e3fd891c4ba3f36fd015367ba2eadea22af6a7de1610fbb5bcc2d3df0
  languageName: node
  linkType: hard

"@babel/helper-define-polyfill-provider@npm:^0.6.3, @babel/helper-define-polyfill-provider@npm:^0.6.4":
  version: 0.6.4
  resolution: "@babel/helper-define-polyfill-provider@npm:0.6.4"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.22.6"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    debug: "npm:^4.1.1"
    lodash.debounce: "npm:^4.0.8"
    resolve: "npm:^1.14.2"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10c0/b74f2b46e233a178618d19432bdae16e0137d0a603497ee901155e083c4a61f26fe01d79fb95d5f4c22131ade9d958d8f587088d412cca1302633587f070919d
  languageName: node
  linkType: hard

"@babel/helper-member-expression-to-functions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-member-expression-to-functions@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/5762ad009b6a3d8b0e6e79ff6011b3b8fdda0fefad56cfa8bfbe6aa02d5a8a8a9680a45748fe3ac47e735a03d2d88c0a676e3f9f59f20ae9fadcc8d51ccd5a53
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.25.9, @babel/helper-module-imports@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-module-imports@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/e00aace096e4e29290ff8648455c2bc4ed982f0d61dbf2db1b5e750b9b98f318bf5788d75a4f974c151bd318fd549e81dbcab595f46b14b81c12eda3023f51e8
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.27.1, @babel/helper-module-transforms@npm:^7.27.3":
  version: 7.27.3
  resolution: "@babel/helper-module-transforms@npm:7.27.3"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.3"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/fccb4f512a13b4c069af51e1b56b20f54024bcf1591e31e978a30f3502567f34f90a80da6a19a6148c249216292a8074a0121f9e52602510ef0f32dbce95ca01
  languageName: node
  linkType: hard

"@babel/helper-optimise-call-expression@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-optimise-call-expression@npm:7.27.1"
  dependencies:
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/6b861e7fcf6031b9c9fc2de3cd6c005e94a459d6caf3621d93346b52774925800ca29d4f64595a5ceacf4d161eb0d27649ae385110ed69491d9776686fa488e6
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.0.0, @babel/helper-plugin-utils@npm:^7.10.4, @babel/helper-plugin-utils@npm:^7.18.6, @babel/helper-plugin-utils@npm:^7.22.5, @babel/helper-plugin-utils@npm:^7.26.5, @babel/helper-plugin-utils@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-plugin-utils@npm:7.27.1"
  checksum: 10c0/94cf22c81a0c11a09b197b41ab488d416ff62254ce13c57e62912c85700dc2e99e555225787a4099ff6bae7a1812d622c80fbaeda824b79baa10a6c5ac4cf69b
  languageName: node
  linkType: hard

"@babel/helper-remap-async-to-generator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-remap-async-to-generator@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-wrap-function": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/5ba6258f4bb57c7c9fa76b55f416b2d18c867b48c1af4f9f2f7cd7cc933fe6da7514811d08ceb4972f1493be46f4b69c40282b811d1397403febae13c2ec57b5
  languageName: node
  linkType: hard

"@babel/helper-replace-supers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-replace-supers@npm:7.27.1"
  dependencies:
    "@babel/helper-member-expression-to-functions": "npm:^7.27.1"
    "@babel/helper-optimise-call-expression": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/4f2eaaf5fcc196580221a7ccd0f8873447b5d52745ad4096418f6101a1d2e712e9f93722c9a32bc9769a1dc197e001f60d6f5438d4dfde4b9c6a9e4df719354c
  languageName: node
  linkType: hard

"@babel/helper-skip-transparent-expression-wrappers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-skip-transparent-expression-wrappers@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/f625013bcdea422c470223a2614e90d2c1cc9d832e97f32ca1b4f82b34bb4aa67c3904cb4b116375d3b5b753acfb3951ed50835a1e832e7225295c7b0c24dff7
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-string-parser@npm:7.27.1"
  checksum: 10c0/8bda3448e07b5583727c103560bcf9c4c24b3c1051a4c516d4050ef69df37bb9a4734a585fe12725b8c2763de0a265aa1e909b485a4e3270b7cfd3e4dbe4b602
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-identifier@npm:7.27.1"
  checksum: 10c0/c558f11c4871d526498e49d07a84752d1800bf72ac0d3dad100309a2eaba24efbf56ea59af5137ff15e3a00280ebe588560534b0e894a4750f8b1411d8f78b84
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-option@npm:7.27.1"
  checksum: 10c0/6fec5f006eba40001a20f26b1ef5dbbda377b7b68c8ad518c05baa9af3f396e780bdfded24c4eef95d14bb7b8fd56192a6ed38d5d439b97d10efc5f1a191d148
  languageName: node
  linkType: hard

"@babel/helper-wrap-function@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-wrap-function@npm:7.27.1"
  dependencies:
    "@babel/template": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/c472f75c0951bc657ab0a117538c7c116566ae7579ed47ac3f572c42dc78bd6f1e18f52ebe80d38300c991c3fcaa06979e2f8864ee919369dabd59072288de30
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.27.4":
  version: 7.27.6
  resolution: "@babel/helpers@npm:7.27.6"
  dependencies:
    "@babel/template": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.6"
  checksum: 10c0/448bac96ef8b0f21f2294a826df9de6bf4026fd023f8a6bb6c782fe3e61946801ca24381490b8e58d861fee75cd695a1882921afbf1f53b0275ee68c938bd6d3
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.23.9, @babel/parser@npm:^7.26.9, @babel/parser@npm:^7.27.2, @babel/parser@npm:^7.27.4, @babel/parser@npm:^7.27.5":
  version: 7.27.5
  resolution: "@babel/parser@npm:7.27.5"
  dependencies:
    "@babel/types": "npm:^7.27.3"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 10c0/f7faaebf21cc1f25d9ca8ac02c447ed38ef3460ea95be7ea760916dcf529476340d72a5a6010c6641d9ed9d12ad827c8424840277ec2295c5b082ba0f291220a
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-firefox-class-in-computed-class-key@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-firefox-class-in-computed-class-key@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/7dfffa978ae1cd179641a7c4b4ad688c6828c2c58ec96b118c2fb10bc3715223de6b88bff1ebff67056bb5fccc568ae773e3b83c592a1b843423319f80c99ebd
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-safari-class-field-initializer-scope@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-safari-class-field-initializer-scope@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/2cd7a55a856e5e59bbd9484247c092a41e0d9f966778e7019da324d9e0928892d26afc4fbb2ac3d76a3c5a631cd3cf0d72dd2653b44f634f6c663b9e6f80aacd
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/cf29835498c4a25bd470908528919729a0799b2ec94e89004929a5532c94a5e4b1a49bc5d6673a22e5afe05d08465873e14ee3b28c42eb3db489cdf5ca47c680
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
    "@babel/plugin-transform-optional-chaining": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.13.0
  checksum: 10c0/eddcd056f76e198868cbff883eb148acfade8f0890973ab545295df0c08e39573a72e65372bcc0b0bfadba1b043fe1aea6b0907d0b4889453ac154c404194ebc
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/b94e6c3fc019e988b1499490829c327a1067b4ddea8ad402f6d0554793c9124148c2125338c723661b6dff040951abc1f092afbf3f2d234319cd580b68e52445
  languageName: node
  linkType: hard

"@babel/plugin-proposal-private-property-in-object@npm:7.21.0-placeholder-for-preset-env.2":
  version: 7.21.0-placeholder-for-preset-env.2
  resolution: "@babel/plugin-proposal-private-property-in-object@npm:7.21.0-placeholder-for-preset-env.2"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e605e0070da087f6c35579499e65801179a521b6842c15181a1e305c04fded2393f11c1efd09b087be7f8b083d1b75e8f3efcbc1292b4f60d3369e14812cff63
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-assertions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-import-assertions@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/06a954ee672f7a7c44d52b6e55598da43a7064e80df219765c51c37a0692641277e90411028f7cae4f4d1dedeed084f0c453576fa421c35a81f1603c5e3e0146
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-attributes@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-import-attributes@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e66f7a761b8360419bbb93ab67d87c8a97465ef4637a985ff682ce7ba6918b34b29d81190204cf908d0933058ee7b42737423cd8a999546c21b3aabad4affa9a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-meta@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-import-meta@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.10.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/0b08b5e4c3128523d8e346f8cfc86824f0da2697b1be12d71af50a31aff7a56ceb873ed28779121051475010c28d6146a6bfea8518b150b71eeb4e46190172ee
  languageName: node
  linkType: hard

"@babel/plugin-syntax-jsx@npm:^7.25.9":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-jsx@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/bc5afe6a458d5f0492c02a54ad98c5756a0c13bd6d20609aae65acd560a9e141b0876da5f358dce34ea136f271c1016df58b461184d7ae9c4321e0f98588bc84
  languageName: node
  linkType: hard

"@babel/plugin-syntax-typescript@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-typescript@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/11589b4c89c66ef02d57bf56c6246267851ec0c361f58929327dc3e070b0dab644be625bbe7fb4c4df30c3634bfdfe31244e1f517be397d2def1487dbbe3c37d
  languageName: node
  linkType: hard

"@babel/plugin-syntax-unicode-sets-regex@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-syntax-unicode-sets-regex@npm:7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.18.6"
    "@babel/helper-plugin-utils": "npm:^7.18.6"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/9144e5b02a211a4fb9a0ce91063f94fbe1004e80bde3485a0910c9f14897cf83fabd8c21267907cff25db8e224858178df0517f14333cfcf3380ad9a4139cb50
  languageName: node
  linkType: hard

"@babel/plugin-transform-arrow-functions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-arrow-functions@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/19abd7a7d11eef58c9340408a4c2594503f6c4eaea1baa7b0e5fbdda89df097e50663edb3448ad2300170b39efca98a75e5767af05cad3b0facb4944326896a3
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-generator-functions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-async-generator-functions@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-remap-async-to-generator": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/772e449c69ee42a466443acefb07083bd89efb1a1d95679a4dc99ea3be9d8a3c43a2b74d2da95d7c818e9dd9e0b72bfa7c03217a1feaf108f21b7e542f0943c0
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-to-generator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-async-to-generator@npm:7.27.1"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-remap-async-to-generator": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e76b1f6f9c3bbf72e17d7639406d47f09481806de4db99a8de375a0bb40957ea309b20aa705f0c25ab1d7c845e3f365af67eafa368034521151a0e352a03ef2f
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoped-functions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-block-scoped-functions@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/3313130ba3bf0699baad0e60da1c8c3c2f0c2c0a7039cd0063e54e72e739c33f1baadfc9d8c73b3fea8c85dd7250c3964fb09c8e1fa62ba0b24a9fefe0a8dbde
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoping@npm:^7.27.1":
  version: 7.27.5
  resolution: "@babel/plugin-transform-block-scoping@npm:7.27.5"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5c1a61f312f18d3807c4df25868161301a7bd0807092b86951fa6b9918e07ee382d58d61a204c3f9ad0b72b8f6f1d18586f8e485c355a3e959c26a070397e95e
  languageName: node
  linkType: hard

"@babel/plugin-transform-class-properties@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-class-properties@npm:7.27.1"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/cc0662633c0fe6df95819fef223506ddf26c369c8d64ab21a728d9007ec866bf9436a253909819216c24a82186b6ccbc1ec94d7aaf3f82df227c7c02fa6a704b
  languageName: node
  linkType: hard

"@babel/plugin-transform-class-static-block@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-class-static-block@npm:7.27.1"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.12.0
  checksum: 10c0/396997dd81fc1cf242b921e337d25089d6b9dc3596e81322ff11a6359326dc44f2f8b82dcc279c2e514cafaf8964dc7ed39e9fab4b8af1308b57387d111f6a20
  languageName: node
  linkType: hard

"@babel/plugin-transform-classes@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-classes@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-compilation-targets": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-replace-supers": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
    globals: "npm:^11.1.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/1071f4cb1ed5deb5e6f8d0442f2293a540cac5caa5ab3c25ad0571aadcbf961f61e26d367a67894976165a543e02f3a19e40b63b909afbed6e710801a590635c
  languageName: node
  linkType: hard

"@babel/plugin-transform-computed-properties@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-computed-properties@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/template": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e09a12f8c8ae0e6a6144c102956947b4ec05f6c844169121d0ec4529c2d30ad1dc59fee67736193b87a402f44552c888a519a680a31853bdb4d34788c28af3b0
  languageName: node
  linkType: hard

"@babel/plugin-transform-destructuring@npm:^7.27.1, @babel/plugin-transform-destructuring@npm:^7.27.3":
  version: 7.27.3
  resolution: "@babel/plugin-transform-destructuring@npm:7.27.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/f8ac96deef6f9a4cb1dff148dfa2a43116ca1c48434bba433964498c4ef5cef5557693b47463e64a71ffaaf10191c7fab0270844e8dbdc47dc4d120435025df5
  languageName: node
  linkType: hard

"@babel/plugin-transform-dotall-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-dotall-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/f9caddfad9a551b4dabe0dcb7c040f458fbaaa7bbb44200c20198b32c8259be8e050e58d2c853fdac901a4cfe490b86aa857036d8d461b192dd010d0e242dedb
  languageName: node
  linkType: hard

"@babel/plugin-transform-duplicate-keys@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-duplicate-keys@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/22a822e5342b7066f83eaedc4fd9bb044ac6bc68725484690b33ba04a7104980e43ea3229de439286cb8db8e7db4a865733a3f05123ab58a10f189f03553746f
  languageName: node
  linkType: hard

"@babel/plugin-transform-duplicate-named-capturing-groups-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-duplicate-named-capturing-groups-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/121502a252b3206913e1e990a47fea34397b4cbf7804d4cd872d45961bc45b603423f60ca87f3a3023a62528f5feb475ac1c9ec76096899ec182fcb135eba375
  languageName: node
  linkType: hard

"@babel/plugin-transform-dynamic-import@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-dynamic-import@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/8dcd3087aca134b064fc361d2cc34eec1f900f6be039b6368104afcef10bb75dea726bb18cabd046716b89b0edaa771f50189fa16bc5c5914a38cbcf166350f7
  languageName: node
  linkType: hard

"@babel/plugin-transform-exponentiation-operator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-exponentiation-operator@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/953d21e01fed76da8e08fb5094cade7bf8927c1bb79301916bec2db0593b41dbcfbca1024ad5db886b72208a93ada8f57a219525aad048cf15814eeb65cf760d
  languageName: node
  linkType: hard

"@babel/plugin-transform-export-namespace-from@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-export-namespace-from@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/d7165cad11f571a54c8d9263d6c6bf2b817aff4874f747cb51e6e49efb32f2c9b37a6850cdb5e3b81e0b638141bb77dc782a6ec1a94128859fbdf7767581e07c
  languageName: node
  linkType: hard

"@babel/plugin-transform-for-of@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-for-of@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/4635763173a23aae24480681f2b0996b4f54a0cb2368880301a1801638242e263132d1e8adbe112ab272913d1d900ee0d6f7dea79443aef9d3325168cd88b3fb
  languageName: node
  linkType: hard

"@babel/plugin-transform-function-name@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-function-name@npm:7.27.1"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5abdc7b5945fbd807269dcc6e76e52b69235056023b0b35d311e8f5dfd6c09d9f225839798998fc3b663f50cf701457ddb76517025a0d7a5474f3fe56e567a4c
  languageName: node
  linkType: hard

"@babel/plugin-transform-json-strings@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-json-strings@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/2379714aca025516452a7c1afa1ca42a22b9b51a5050a653cc6198a51665ab82bdecf36106d32d731512706a1e373c5637f5ff635737319aa42f3827da2326d6
  languageName: node
  linkType: hard

"@babel/plugin-transform-literals@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/c40dc3eb2f45a92ee476412314a40e471af51a0f51a24e91b85cef5fc59f4fe06758088f541643f07f949d2c67ee7bdce10e11c5ec56791ae09b15c3b451eeca
  languageName: node
  linkType: hard

"@babel/plugin-transform-logical-assignment-operators@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-logical-assignment-operators@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5b0abc7c0d09d562bf555c646dce63a30288e5db46fd2ce809a61d064415da6efc3b2b3c59b8e4fe98accd072c89a2f7c3765b400e4bf488651735d314d9feeb
  languageName: node
  linkType: hard

"@babel/plugin-transform-member-expression-literals@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-member-expression-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/0874ccebbd1c6a155e5f6b3b29729fade1221b73152567c1af1e1a7c12848004dffecbd7eded6dc463955120040ae57c17cb586b53fb5a7a27fcd88177034c30
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-amd@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-amd@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/76e86cd278b6a3c5b8cca8dfb3428e9cd0c81a5df7096e04c783c506696b916a9561386d610a9d846ef64804640e0bd818ea47455fed0ee89b7f66c555b29537
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-commonjs@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-commonjs@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/4def972dcd23375a266ea1189115a4ff61744b2c9366fc1de648b3fab2c650faf1a94092de93a33ff18858d2e6c4dddeeee5384cb42ba0129baeab01a5cdf1e2
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-systemjs@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-systemjs@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/f16fca62d144d9cbf558e7b5f83e13bb6d0f21fdeff3024b0cecd42ffdec0b4151461da42bd0963512783ece31aafa5ffe03446b4869220ddd095b24d414e2b5
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-umd@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-umd@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e5962a8874889da2ab1aa32eb93ec21d419c7423c766e4befb39b4bb512b9ad44b47837b6cd1c8f1065445cbbcc6dc2be10298ac6e734e5ca1059fc23698daed
  languageName: node
  linkType: hard

"@babel/plugin-transform-named-capturing-groups-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-named-capturing-groups-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/8eaa8c9aee00a00f3bd8bd8b561d3f569644d98cb2cfe3026d7398aabf9b29afd62f24f142b4112fa1f572d9b0e1928291b099cde59f56d6b59f4d565e58abf2
  languageName: node
  linkType: hard

"@babel/plugin-transform-new-target@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-new-target@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/9b0581412fcc5ab1b9a2d86a0c5407bd959391f0a1e77a46953fef9f7a57f3f4020d75f71098c5f9e5dcc680a87f9fd99b3205ab12e25ef8c19eed038c1e4b28
  languageName: node
  linkType: hard

"@babel/plugin-transform-nullish-coalescing-operator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-nullish-coalescing-operator@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/a435fc03aaa65c6ef8e99b2d61af0994eb5cdd4a28562d78c3b0b0228ca7e501aa255e1dff091a6996d7d3ea808eb5a65fd50ecd28dfb10687a8a1095dcadc7a
  languageName: node
  linkType: hard

"@babel/plugin-transform-numeric-separator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-numeric-separator@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/b72cbebbfe46fcf319504edc1cf59f3f41c992dd6840db766367f6a1d232cd2c52143c5eaf57e0316710bee251cae94be97c6d646b5022fcd9274ccb131b470c
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-rest-spread@npm:^7.27.2":
  version: 7.27.3
  resolution: "@babel/plugin-transform-object-rest-spread@npm:7.27.3"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.27.2"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/plugin-transform-destructuring": "npm:^7.27.3"
    "@babel/plugin-transform-parameters": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/f2d04f59f773a9480bbaabd082fecdb5fb2b6ae5e77147ae8df34a8b773b6148d0c4260d2beaa4755eb5f548a105f2069124b9cea96f9387128656cbb0730ee4
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-super@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-object-super@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-replace-supers": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/efa2d092ef55105deb06d30aff4e460c57779b94861188128489b72378bf1f0ab0f06a4a4d68b9ae2a59a79719fbb2d148b9a3dca19ceff9c73b1f1a95e0527c
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-catch-binding@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-optional-catch-binding@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/807a4330f1fac08e2682d57bc82e714868fc651c8876f9a8b3a3fd8f53c129e87371f8243e712ac7dae11e090b737a2219a02fe1b6459a29e664fa073c3277bb
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-chaining@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-optional-chaining@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5b18ff5124e503f0a25d6b195be7351a028b3992d6f2a91fb4037e2a2c386400d66bc1df8f6df0a94c708524f318729e81a95c41906e5a7919a06a43e573a525
  languageName: node
  linkType: hard

"@babel/plugin-transform-parameters@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-parameters@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/453a9618735eeff5551d4c7f02c250606586fe1dd210ec9f69a4f15629ace180cd944339ebff2b0f11e1a40567d83a229ba1c567620e70b2ebedea576e12196a
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-methods@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-private-methods@npm:7.27.1"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/232bedfe9d28df215fb03cc7623bdde468b1246bdd6dc24465ff4bf9cc5f5a256ae33daea1fafa6cc59705e4d29da9024bb79baccaa5cd92811ac5db9b9244f2
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-property-in-object@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-private-property-in-object@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/a8c4536273ca716dcc98e74ea25ca76431528554922f184392be3ddaf1761d4aa0e06f1311577755bd1613f7054fb51d29de2ada1130f743d329170a1aa1fe56
  languageName: node
  linkType: hard

"@babel/plugin-transform-property-literals@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-property-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/15713a87edd6db620d6e66eb551b4fbfff5b8232c460c7c76cedf98efdc5cd21080c97040231e19e06594c6d7dfa66e1ab3d0951e29d5814fb25e813f6d6209c
  languageName: node
  linkType: hard

"@babel/plugin-transform-regenerator@npm:^7.27.1":
  version: 7.27.5
  resolution: "@babel/plugin-transform-regenerator@npm:7.27.5"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/4ace8ced76b421cd44dd9fa08bebc2f3fd76ec84e532cd1027738f411afdbc239789edd6c96dd1db412fc4a42cead5c1ac98a8aef94f35102f5de1049e64c07a
  languageName: node
  linkType: hard

"@babel/plugin-transform-regexp-modifiers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-regexp-modifiers@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/31ae596ab56751cf43468a6c0a9d6bc3521d306d2bee9c6957cdb64bea53812ce24bd13a32f766150d62b737bca5b0650b2c62db379382fff0dccbf076055c33
  languageName: node
  linkType: hard

"@babel/plugin-transform-reserved-words@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-reserved-words@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e1a87691cce21a644a474d7c9a8107d4486c062957be32042d40f0a3d0cc66e00a3150989655019c255ff020d2640ac16aaf544792717d586f219f3bad295567
  languageName: node
  linkType: hard

"@babel/plugin-transform-shorthand-properties@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-shorthand-properties@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/bd5544b89520a22c41a6df5ddac9039821d3334c0ef364d18b0ba9674c5071c223bcc98be5867dc3865cb10796882b7594e2c40dedaff38e1b1273913fe353e1
  languageName: node
  linkType: hard

"@babel/plugin-transform-spread@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-spread@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/b34fc58b33bd35b47d67416655c2cbc8578fbb3948b4592bc15eb6d8b4046986e25c06e3b9929460fa4ab08e9653582415e7ef8b87d265e1239251bdf5a4c162
  languageName: node
  linkType: hard

"@babel/plugin-transform-sticky-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-sticky-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5698df2d924f0b1b7bdb7ef370e83f99ed3f0964eb3b9c27d774d021bee7f6d45f9a73e2be369d90b4aff1603ce29827f8743f091789960e7669daf9c3cda850
  languageName: node
  linkType: hard

"@babel/plugin-transform-template-literals@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-template-literals@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/c90f403e42ef062b60654d1c122c70f3ec6f00c2f304b0931ebe6d0b432498ef8a5ef9266ddf00debc535f8390842207e44d3900eff1d2bab0cc1a700f03e083
  languageName: node
  linkType: hard

"@babel/plugin-transform-typeof-symbol@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-typeof-symbol@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/a13c68015311fefa06a51830bc69d5badd06c881b13d5cf9ba04bf7c73e3fc6311cc889e18d9645ce2a64a79456dc9c7be88476c0b6802f62a686cb6f662ecd6
  languageName: node
  linkType: hard

"@babel/plugin-transform-typescript@npm:^7.23.3":
  version: 7.27.1
  resolution: "@babel/plugin-transform-typescript@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
    "@babel/plugin-syntax-typescript": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/48f1db5de17a0f9fc365ff4fb046010aedc7aad813a7aa42fb73fcdab6442f9e700dde2cc0481086e01b0dae662ae4d3e965a52cde154f0f146d243a8ac68e93
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-escapes@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-unicode-escapes@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/a6809e0ca69d77ee9804e0c1164e8a2dea5e40718f6dcf234aeddf7292e7414f7ee331d87f17eb6f160823a329d1d6751bd49b35b392ac4a6efc032e4d3038d8
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-property-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-unicode-property-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/a332bc3cb3eeea67c47502bc52d13a0f8abae5a7bfcb08b93a8300ddaff8d9e1238f912969494c1b494c1898c6f19687054440706700b6d12cb0b90d88beb4d0
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-unicode-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/6abda1bcffb79feba6f5c691859cdbe984cc96481ea65d5af5ba97c2e843154005f0886e25006a37a2d213c0243506a06eaeafd93a040dbe1f79539016a0d17a
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-sets-regex@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-unicode-sets-regex@npm:7.27.1"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/236645f4d0a1fba7c18dc8ffe3975933af93e478f2665650c2d91cf528cfa1587cde5cfe277e0e501fc03b5bf57638369575d6539cef478632fb93bd7d7d7178
  languageName: node
  linkType: hard

"@babel/preset-env@npm:^7.23.9":
  version: 7.27.2
  resolution: "@babel/preset-env@npm:7.27.2"
  dependencies:
    "@babel/compat-data": "npm:^7.27.2"
    "@babel/helper-compilation-targets": "npm:^7.27.2"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-validator-option": "npm:^7.27.1"
    "@babel/plugin-bugfix-firefox-class-in-computed-class-key": "npm:^7.27.1"
    "@babel/plugin-bugfix-safari-class-field-initializer-scope": "npm:^7.27.1"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": "npm:^7.27.1"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": "npm:^7.27.1"
    "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly": "npm:^7.27.1"
    "@babel/plugin-proposal-private-property-in-object": "npm:7.21.0-placeholder-for-preset-env.2"
    "@babel/plugin-syntax-import-assertions": "npm:^7.27.1"
    "@babel/plugin-syntax-import-attributes": "npm:^7.27.1"
    "@babel/plugin-syntax-unicode-sets-regex": "npm:^7.18.6"
    "@babel/plugin-transform-arrow-functions": "npm:^7.27.1"
    "@babel/plugin-transform-async-generator-functions": "npm:^7.27.1"
    "@babel/plugin-transform-async-to-generator": "npm:^7.27.1"
    "@babel/plugin-transform-block-scoped-functions": "npm:^7.27.1"
    "@babel/plugin-transform-block-scoping": "npm:^7.27.1"
    "@babel/plugin-transform-class-properties": "npm:^7.27.1"
    "@babel/plugin-transform-class-static-block": "npm:^7.27.1"
    "@babel/plugin-transform-classes": "npm:^7.27.1"
    "@babel/plugin-transform-computed-properties": "npm:^7.27.1"
    "@babel/plugin-transform-destructuring": "npm:^7.27.1"
    "@babel/plugin-transform-dotall-regex": "npm:^7.27.1"
    "@babel/plugin-transform-duplicate-keys": "npm:^7.27.1"
    "@babel/plugin-transform-duplicate-named-capturing-groups-regex": "npm:^7.27.1"
    "@babel/plugin-transform-dynamic-import": "npm:^7.27.1"
    "@babel/plugin-transform-exponentiation-operator": "npm:^7.27.1"
    "@babel/plugin-transform-export-namespace-from": "npm:^7.27.1"
    "@babel/plugin-transform-for-of": "npm:^7.27.1"
    "@babel/plugin-transform-function-name": "npm:^7.27.1"
    "@babel/plugin-transform-json-strings": "npm:^7.27.1"
    "@babel/plugin-transform-literals": "npm:^7.27.1"
    "@babel/plugin-transform-logical-assignment-operators": "npm:^7.27.1"
    "@babel/plugin-transform-member-expression-literals": "npm:^7.27.1"
    "@babel/plugin-transform-modules-amd": "npm:^7.27.1"
    "@babel/plugin-transform-modules-commonjs": "npm:^7.27.1"
    "@babel/plugin-transform-modules-systemjs": "npm:^7.27.1"
    "@babel/plugin-transform-modules-umd": "npm:^7.27.1"
    "@babel/plugin-transform-named-capturing-groups-regex": "npm:^7.27.1"
    "@babel/plugin-transform-new-target": "npm:^7.27.1"
    "@babel/plugin-transform-nullish-coalescing-operator": "npm:^7.27.1"
    "@babel/plugin-transform-numeric-separator": "npm:^7.27.1"
    "@babel/plugin-transform-object-rest-spread": "npm:^7.27.2"
    "@babel/plugin-transform-object-super": "npm:^7.27.1"
    "@babel/plugin-transform-optional-catch-binding": "npm:^7.27.1"
    "@babel/plugin-transform-optional-chaining": "npm:^7.27.1"
    "@babel/plugin-transform-parameters": "npm:^7.27.1"
    "@babel/plugin-transform-private-methods": "npm:^7.27.1"
    "@babel/plugin-transform-private-property-in-object": "npm:^7.27.1"
    "@babel/plugin-transform-property-literals": "npm:^7.27.1"
    "@babel/plugin-transform-regenerator": "npm:^7.27.1"
    "@babel/plugin-transform-regexp-modifiers": "npm:^7.27.1"
    "@babel/plugin-transform-reserved-words": "npm:^7.27.1"
    "@babel/plugin-transform-shorthand-properties": "npm:^7.27.1"
    "@babel/plugin-transform-spread": "npm:^7.27.1"
    "@babel/plugin-transform-sticky-regex": "npm:^7.27.1"
    "@babel/plugin-transform-template-literals": "npm:^7.27.1"
    "@babel/plugin-transform-typeof-symbol": "npm:^7.27.1"
    "@babel/plugin-transform-unicode-escapes": "npm:^7.27.1"
    "@babel/plugin-transform-unicode-property-regex": "npm:^7.27.1"
    "@babel/plugin-transform-unicode-regex": "npm:^7.27.1"
    "@babel/plugin-transform-unicode-sets-regex": "npm:^7.27.1"
    "@babel/preset-modules": "npm:0.1.6-no-external-plugins"
    babel-plugin-polyfill-corejs2: "npm:^0.4.10"
    babel-plugin-polyfill-corejs3: "npm:^0.11.0"
    babel-plugin-polyfill-regenerator: "npm:^0.6.1"
    core-js-compat: "npm:^3.40.0"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/fd7ec310832a9ff26ed8d56bc0832cdbdb3a188e022050b74790796650649fb8373568af05b320b58b3ff922507979bad50ff95a4d504ab0081134480103504e
  languageName: node
  linkType: hard

"@babel/preset-modules@npm:0.1.6-no-external-plugins":
  version: 0.1.6-no-external-plugins
  resolution: "@babel/preset-modules@npm:0.1.6-no-external-plugins"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.0.0"
    "@babel/types": "npm:^7.4.4"
    esutils: "npm:^2.0.2"
  peerDependencies:
    "@babel/core": ^7.0.0-0 || ^8.0.0-0 <8.0.0
  checksum: 10c0/9d02f70d7052446c5f3a4fb39e6b632695fb6801e46d31d7f7c5001f7c18d31d1ea8369212331ca7ad4e7877b73231f470b0d559162624128f1b80fe591409e6
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.7.2":
  version: 7.27.6
  resolution: "@babel/runtime@npm:7.27.6"
  checksum: 10c0/89726be83f356f511dcdb74d3ea4d873a5f0cf0017d4530cb53aa27380c01ca102d573eff8b8b77815e624b1f8c24e7f0311834ad4fb632c90a770fda00bd4c8
  languageName: node
  linkType: hard

"@babel/template@npm:^7.26.9, @babel/template@npm:^7.27.1, @babel/template@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/template@npm:7.27.2"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/parser": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/ed9e9022651e463cc5f2cc21942f0e74544f1754d231add6348ff1b472985a3b3502041c0be62dc99ed2d12cfae0c51394bf827452b98a2f8769c03b87aadc81
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.26.9, @babel/traverse@npm:^7.27.1, @babel/traverse@npm:^7.27.3, @babel/traverse@npm:^7.27.4":
  version: 7.27.4
  resolution: "@babel/traverse@npm:7.27.4"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.27.3"
    "@babel/parser": "npm:^7.27.4"
    "@babel/template": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.3"
    debug: "npm:^4.3.1"
    globals: "npm:^11.1.0"
  checksum: 10c0/6de8aa2a0637a6ee6d205bf48b9e923928a02415771fdec60085ed754dcdf605e450bb3315c2552fa51c31a4662275b45d5ae4ad527ce55a7db9acebdbbbb8ed
  languageName: node
  linkType: hard

"@babel/types@npm:^7.20.7, @babel/types@npm:^7.26.9, @babel/types@npm:^7.27.1, @babel/types@npm:^7.27.3, @babel/types@npm:^7.27.6, @babel/types@npm:^7.4.4":
  version: 7.27.6
  resolution: "@babel/types@npm:7.27.6"
  dependencies:
    "@babel/helper-string-parser": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
  checksum: 10c0/39d556be114f2a6d874ea25ad39826a9e3a0e98de0233ae6d932f6d09a4b222923a90a7274c635ed61f1ba49bbd345329226678800900ad1c8d11afabd573aaf
  languageName: node
  linkType: hard

"@dcloudio/types@npm:^3.4.8":
  version: 3.4.15
  resolution: "@dcloudio/types@npm:3.4.15"
  checksum: 10c0/81738c17d0ec174a36a43ec5e0bfaedaa09f794bf27994eeb1244aeafc25325a853ae2d34029d16ab5d59081810c76ac76553426d998e1191b66e29ba7fd7e10
  languageName: node
  linkType: hard

"@dcloudio/uni-app-harmony@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-app-harmony@npm:3.0.0-4030620241128001"
  dependencies:
    "@dcloudio/uni-app-vite": "npm:3.0.0-4030620241128001"
    debug: "npm:^4.3.3"
    fs-extra: "npm:^10.0.0"
    licia: "npm:^1.29.0"
    postcss-selector-parser: "npm:^6.0.6"
  checksum: 10c0/27dcacef95af9f4454bc2a761dd223cd0f32f2145f1bf1e557a8d75c392440e91161fa4ee6b4347cda10a4e75fede9ddce7e396120839d29420305412d321cf3
  languageName: node
  linkType: hard

"@dcloudio/uni-app-plus@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-app-plus@npm:3.0.0-4030620241128001"
  dependencies:
    "@dcloudio/uni-app-uts": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-app-vite": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-app-vue": "npm:3.0.0-4030620241128001"
    debug: "npm:^4.3.3"
    fs-extra: "npm:^10.0.0"
    licia: "npm:^1.29.0"
    postcss-selector-parser: "npm:^6.0.6"
  checksum: 10c0/93fa6fcf999fb1dc0443c4be041c571b70c73ef5113eb88538892c904c8752c9c660f70f8d37eea276da8874d5d0b252d20b608acf367db2f9430a2c1ef41980
  languageName: node
  linkType: hard

"@dcloudio/uni-app-uts@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-app-uts@npm:3.0.0-4030620241128001"
  dependencies:
    "@babel/parser": "npm:^7.23.9"
    "@babel/types": "npm:^7.20.7"
    "@dcloudio/uni-cli-shared": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-i18n": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-nvue-styler": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-shared": "npm:3.0.0-4030620241128001"
    "@jridgewell/gen-mapping": "npm:^0.3.3"
    "@jridgewell/trace-mapping": "npm:^0.3.19"
    "@rollup/pluginutils": "npm:^5.0.5"
    "@vue/compiler-core": "npm:3.4.21"
    "@vue/compiler-dom": "npm:3.4.21"
    "@vue/compiler-sfc": "npm:3.4.21"
    "@vue/consolidate": "npm:^1.0.0"
    "@vue/shared": "npm:3.4.21"
    debug: "npm:^4.3.3"
    es-module-lexer: "npm:^1.2.1"
    estree-walker: "npm:^2.0.2"
    fs-extra: "npm:^10.0.0"
    magic-string: "npm:^0.30.7"
    picocolors: "npm:^1.0.0"
    source-map-js: "npm:^1.0.2"
    unimport: "npm:^3.11.1"
  checksum: 10c0/a91a6d848f21bf3a77941b46b46bf689181a4c202d5db82202e1520e1916a0d832f658cda3503d750a849f9f9fb048f8e6a9db757ff99e243a1545036ef07020
  languageName: node
  linkType: hard

"@dcloudio/uni-app-vite@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-app-vite@npm:3.0.0-4030620241128001"
  dependencies:
    "@dcloudio/uni-cli-shared": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-i18n": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-nvue-styler": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-shared": "npm:3.0.0-4030620241128001"
    "@rollup/pluginutils": "npm:^5.0.5"
    "@vitejs/plugin-vue": "npm:5.1.0"
    "@vue/compiler-dom": "npm:3.4.21"
    "@vue/compiler-sfc": "npm:3.4.21"
    debug: "npm:^4.3.3"
    fs-extra: "npm:^10.0.0"
    picocolors: "npm:^1.0.0"
  checksum: 10c0/b5737bc599d798feb95aacfdd6b5e035d3fbe500a0b3b7de80032d2922a8e86290b3978eaf53e93e38efe033ddfc44a842b7880a8418d7bde4ee03e87f7156c1
  languageName: node
  linkType: hard

"@dcloudio/uni-app-vue@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-app-vue@npm:3.0.0-4030620241128001"
  checksum: 10c0/b37f78308f4532fae9f6f0832353bad53e80d0bdf57deac21ec49182f1041126516ba5c38f0a09c14e1a093924561a0485a7b30dc243a9e09d4b5038d4dca911
  languageName: node
  linkType: hard

"@dcloudio/uni-app@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-app@npm:3.0.0-4030620241128001"
  dependencies:
    "@dcloudio/uni-cloud": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-components": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-i18n": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-push": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-shared": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-stat": "npm:3.0.0-4030620241128001"
    "@vue/shared": "npm:3.4.21"
  peerDependencies:
    "@dcloudio/types": ^3.4.14
  checksum: 10c0/87a9244818d133abb78299865b0a8de64804c9d934fa71133c94d13d7f744d3a9b44270784426e638b513b5d4e67e38460e552542259f5bac3bcdafa27540f06
  languageName: node
  linkType: hard

"@dcloudio/uni-automator@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-automator@npm:3.0.0-4030620241128001"
  dependencies:
    "@dcloudio/uni-cli-shared": "npm:3.0.0-4030620241128001"
    address: "npm:^1.1.2"
    cross-env: "npm:^7.0.3"
    debug: "npm:^4.3.3"
    default-gateway: "npm:^6.0.3"
    fs-extra: "npm:^10.0.0"
    jsonc-parser: "npm:^3.2.0"
    licia: "npm:^1.29.0"
    merge: "npm:^2.1.1"
    qrcode-reader: "npm:^1.0.4"
    qrcode-terminal: "npm:^0.12.0"
    ws: "npm:^8.4.2"
  peerDependencies:
    jest: 27.0.4
    jest-environment-node: 27.5.1
  checksum: 10c0/c0fe1acf8ff4715384bac046d7c129d03bb39dc8bc6fbf6a9ad7dbf1277e5c61afc0f7b5097dd119f666807faf4677fca350d9cef9d4b3ce77c51ebab6a864a9
  languageName: node
  linkType: hard

"@dcloudio/uni-cli-shared@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-cli-shared@npm:3.0.0-4030620241128001"
  dependencies:
    "@ampproject/remapping": "npm:^2.1.2"
    "@babel/code-frame": "npm:^7.23.5"
    "@babel/core": "npm:^7.23.3"
    "@babel/parser": "npm:^7.23.9"
    "@babel/types": "npm:^7.20.7"
    "@dcloudio/uni-i18n": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-shared": "npm:3.0.0-4030620241128001"
    "@intlify/core-base": "npm:9.1.9"
    "@intlify/shared": "npm:9.1.9"
    "@intlify/vue-devtools": "npm:9.1.9"
    "@rollup/pluginutils": "npm:^5.0.5"
    "@vue/compiler-core": "npm:3.4.21"
    "@vue/compiler-dom": "npm:3.4.21"
    "@vue/compiler-sfc": "npm:3.4.21"
    "@vue/compiler-ssr": "npm:3.4.21"
    "@vue/server-renderer": "npm:3.4.21"
    "@vue/shared": "npm:3.4.21"
    adm-zip: "npm:^0.5.12"
    autoprefixer: "npm:^10.4.19"
    base64url: "npm:^3.0.1"
    chokidar: "npm:^3.5.3"
    compare-versions: "npm:^3.6.0"
    debug: "npm:^4.3.3"
    es-module-lexer: "npm:^1.2.1"
    esbuild: "npm:^0.20.1"
    estree-walker: "npm:^2.0.2"
    fast-glob: "npm:^3.2.11"
    fs-extra: "npm:^10.0.0"
    hash-sum: "npm:^2.0.0"
    isbinaryfile: "npm:^5.0.2"
    jsonc-parser: "npm:^3.2.0"
    lines-and-columns: "npm:^2.0.4"
    magic-string: "npm:^0.30.7"
    merge: "npm:^2.1.1"
    mime: "npm:^3.0.0"
    module-alias: "npm:^2.2.2"
    os-locale-s-fix: "npm:^1.0.8-fix-1"
    picocolors: "npm:^1.0.0"
    postcss-import: "npm:^14.0.2"
    postcss-load-config: "npm:^3.1.1"
    postcss-modules: "npm:^4.3.0"
    postcss-selector-parser: "npm:^6.0.6"
    resolve: "npm:^1.22.1"
    source-map-js: "npm:^1.0.2"
    tapable: "npm:^2.2.0"
    unplugin-auto-import: "npm:^0.18.2"
    xregexp: "npm:3.1.0"
  checksum: 10c0/4b9470bfa44e7731c6d5cd74c41153246c5d48a29d168fa6d609300d84b3b09cfc25b155c3f6b34db4de96651a14905ae5107b3217cc7fc08bca3f853a599676
  languageName: node
  linkType: hard

"@dcloudio/uni-cloud@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-cloud@npm:3.0.0-4030620241128001"
  dependencies:
    "@dcloudio/uni-cli-shared": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-i18n": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-shared": "npm:3.0.0-4030620241128001"
    "@vue/shared": "npm:3.4.21"
    fast-glob: "npm:^3.2.11"
  checksum: 10c0/bb860b5a7d84acaa46ec77a3fb4553e704d9a64779417b53c80cb8d510216bebc36572cf36633577f26f6e9321d01adf084b05909e80a484a768f015bf3a3987
  languageName: node
  linkType: hard

"@dcloudio/uni-components@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-components@npm:3.0.0-4030620241128001"
  dependencies:
    "@dcloudio/uni-cloud": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-h5": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-i18n": "npm:3.0.0-4030620241128001"
  checksum: 10c0/2441115a894349e063fc3df6ed3a53ed4b31e78be4e98cd4b367a967e5168a462f246c0c5a2c0d262b7eabc22ae4be3ca467b492f50b3bd177e7527cc62ec64a
  languageName: node
  linkType: hard

"@dcloudio/uni-h5-vite@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-h5-vite@npm:3.0.0-4030620241128001"
  dependencies:
    "@dcloudio/uni-cli-shared": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-shared": "npm:3.0.0-4030620241128001"
    "@rollup/pluginutils": "npm:^5.0.5"
    "@vue/compiler-dom": "npm:3.4.21"
    "@vue/compiler-sfc": "npm:3.4.21"
    "@vue/server-renderer": "npm:3.4.21"
    "@vue/shared": "npm:3.4.21"
    debug: "npm:^4.3.3"
    fs-extra: "npm:^10.0.0"
    mime: "npm:^3.0.0"
    module-alias: "npm:^2.2.2"
  checksum: 10c0/618babae8e07e9ffcafd75eca720e5a1965f79684df65a37028f504f0100650f57cfdb6bb3723ddea3378b6e6cbc78157b60ecfc7a30ffc0c27a66e758eef03f
  languageName: node
  linkType: hard

"@dcloudio/uni-h5-vue@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-h5-vue@npm:3.0.0-4030620241128001"
  dependencies:
    "@dcloudio/uni-shared": "npm:3.0.0-4030620241128001"
    "@vue/server-renderer": "npm:3.4.21"
  checksum: 10c0/f213b6336c44f936a65201cea6c9214d3a277e4e8f4e96791243fe635cb6828c32489fe1c95498a52211986b64b526de5e632e9106ea875dc6072585054a1886
  languageName: node
  linkType: hard

"@dcloudio/uni-h5@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-h5@npm:3.0.0-4030620241128001"
  dependencies:
    "@dcloudio/uni-h5-vite": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-h5-vue": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-i18n": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-shared": "npm:3.0.0-4030620241128001"
    "@vue/server-renderer": "npm:3.4.21"
    "@vue/shared": "npm:3.4.21"
    debug: "npm:^4.3.3"
    localstorage-polyfill: "npm:^1.0.1"
    postcss-selector-parser: "npm:^6.0.6"
    safe-area-insets: "npm:^1.4.1"
    vue-router: "npm:^4.3.0"
    xmlhttprequest: "npm:^1.8.0"
  checksum: 10c0/fdb5dfa16de043f100c9051e734a2405087acf12681fc3c85a96e7b0301460d1b744fdcb5c47b46e5431d22dbfc89afc096f388425e5518a49d68d96ebac1a02
  languageName: node
  linkType: hard

"@dcloudio/uni-i18n@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-i18n@npm:3.0.0-4030620241128001"
  checksum: 10c0/191f75f4c393978c8b28203e5d6b097ad61fb3e969d84839a305232b76958f8d2a36187888f7ce9dd039ae008376285df4e9537aaeb0b81e3237b806f7cf28e5
  languageName: node
  linkType: hard

"@dcloudio/uni-mp-alipay@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-mp-alipay@npm:3.0.0-4030620241128001"
  dependencies:
    "@dcloudio/uni-cli-shared": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-vite": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-vue": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-shared": "npm:3.0.0-4030620241128001"
    "@vue/compiler-core": "npm:3.4.21"
    "@vue/shared": "npm:3.4.21"
  checksum: 10c0/15d5a9055926b7813d85aaba82cc514031e21ff4b08aa4c46df414c647e32dc0641e00301eb9f3d93c8622cb80aae9a278c7e6461830048a446217ccb792722f
  languageName: node
  linkType: hard

"@dcloudio/uni-mp-baidu@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-mp-baidu@npm:3.0.0-4030620241128001"
  dependencies:
    "@dcloudio/uni-cli-shared": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-compiler": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-vite": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-vue": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-weixin": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-shared": "npm:3.0.0-4030620241128001"
    "@vue/compiler-core": "npm:3.4.21"
    "@vue/shared": "npm:3.4.21"
    jimp: "npm:^0.10.1"
    licia: "npm:^1.29.0"
    qrcode-reader: "npm:^1.0.4"
    qrcode-terminal: "npm:^0.12.0"
    ws: "npm:^8.4.2"
  checksum: 10c0/bb46151f4c030b24f1b8098653fc6b906fbdcca2f929d8f9bbb3573cd4b45873248c3cdfe31ed25173ec752e33dc982bed5552de15643208bbcac1a3e026e79d
  languageName: node
  linkType: hard

"@dcloudio/uni-mp-compiler@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-mp-compiler@npm:3.0.0-4030620241128001"
  dependencies:
    "@babel/generator": "npm:^7.20.5"
    "@babel/parser": "npm:^7.23.9"
    "@babel/types": "npm:^7.20.7"
    "@dcloudio/uni-cli-shared": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-shared": "npm:3.0.0-4030620241128001"
    "@vue/compiler-core": "npm:3.4.21"
    "@vue/compiler-dom": "npm:3.4.21"
    "@vue/shared": "npm:3.4.21"
    estree-walker: "npm:^2.0.2"
  checksum: 10c0/feb89937154c7e3cd70b4adb5078568ac3b9b748312954b09867d47ddc59d26e27aa7fbfd2f2b2243b8efcb1e63dbdeab7becf73ab14ff24ab1b2e56b330c2ff
  languageName: node
  linkType: hard

"@dcloudio/uni-mp-jd@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-mp-jd@npm:3.0.0-4030620241128001"
  dependencies:
    "@dcloudio/uni-cli-shared": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-compiler": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-vite": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-vue": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-shared": "npm:3.0.0-4030620241128001"
    "@vue/shared": "npm:3.4.21"
  checksum: 10c0/7bdbb847d6c426dc9580b97a77cb8ffa36541670fbff389efbdfaebc95fa7c9db05cc890866e326ac6af09a88c75a03ac76ce0e76d2f79098d07c7807ef7a5ca
  languageName: node
  linkType: hard

"@dcloudio/uni-mp-kuaishou@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-mp-kuaishou@npm:3.0.0-4030620241128001"
  dependencies:
    "@dcloudio/uni-cli-shared": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-compiler": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-vite": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-vue": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-weixin": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-shared": "npm:3.0.0-4030620241128001"
    "@vue/compiler-core": "npm:3.4.21"
    "@vue/shared": "npm:3.4.21"
  checksum: 10c0/e91eda3b5f5b7f10ead0b310bd69b94124a62a0dfb67cd1473fb1bfabf7769e2d9c124ffcab64a90c78906d7cf9d4a6396dc3e98f3a3e2a6025de9e8a1eeb3d2
  languageName: node
  linkType: hard

"@dcloudio/uni-mp-lark@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-mp-lark@npm:3.0.0-4030620241128001"
  dependencies:
    "@dcloudio/uni-cli-shared": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-compiler": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-toutiao": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-vite": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-vue": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-shared": "npm:3.0.0-4030620241128001"
    "@vue/compiler-core": "npm:3.4.21"
    "@vue/shared": "npm:3.4.21"
  checksum: 10c0/25dbd23deb05bb1685ad7cc83a48d52027fb6ed2c65443ec85192a0fbdc1dd402f74d5d9a5cae6f643ee34ebde6e750142b9fe68fe359522a45c80c081bc92c1
  languageName: node
  linkType: hard

"@dcloudio/uni-mp-qq@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-mp-qq@npm:3.0.0-4030620241128001"
  dependencies:
    "@dcloudio/uni-cli-shared": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-vite": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-vue": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-shared": "npm:3.0.0-4030620241128001"
    "@vue/shared": "npm:3.4.21"
    fs-extra: "npm:^10.0.0"
  checksum: 10c0/1f4971c83adc2312ee3dada16635b53ac893939f4aaefde64bbe403365f6997a776d76881f9a28c5c63c66c01490bba81274f470e7a6a2985aaccd1e3fd4194f
  languageName: node
  linkType: hard

"@dcloudio/uni-mp-toutiao@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-mp-toutiao@npm:3.0.0-4030620241128001"
  dependencies:
    "@dcloudio/uni-cli-shared": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-compiler": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-vite": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-vue": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-shared": "npm:3.0.0-4030620241128001"
    "@vue/compiler-core": "npm:3.4.21"
    "@vue/shared": "npm:3.4.21"
  checksum: 10c0/1096a978d3437ffe37de322b1a84d5887262ab91ecb79798e91f97e07f806f151fd0897fedaf390b2f9dba3fdd1f5fbad0d27565439e3a82a75459178175c96a
  languageName: node
  linkType: hard

"@dcloudio/uni-mp-vite@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-mp-vite@npm:3.0.0-4030620241128001"
  dependencies:
    "@dcloudio/uni-cli-shared": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-i18n": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-compiler": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-vue": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-shared": "npm:3.0.0-4030620241128001"
    "@vue/compiler-dom": "npm:3.4.21"
    "@vue/compiler-sfc": "npm:3.4.21"
    "@vue/shared": "npm:3.4.21"
    debug: "npm:^4.3.3"
  checksum: 10c0/394bed336654f50b8b3c4c5092592d9cec1c4681cbea9e9cba5c8e50f238be6a8b81e2262c908d4eaca84bfcb4bfc9c90818c1c0b968d86c83eb9dc10c1cc2c2
  languageName: node
  linkType: hard

"@dcloudio/uni-mp-vue@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-mp-vue@npm:3.0.0-4030620241128001"
  dependencies:
    "@dcloudio/uni-shared": "npm:3.0.0-4030620241128001"
    "@vue/shared": "npm:3.4.21"
  checksum: 10c0/c2e439d97f7f7d553fbaa0fb284ab71f78c7ee2eb9b2ee83c0a89e0e69c4acdb4c14cf7118137a500cef7eab333d1edc141d9b9263aecd7ffac70d9e5ac3f9a0
  languageName: node
  linkType: hard

"@dcloudio/uni-mp-weixin@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-mp-weixin@npm:3.0.0-4030620241128001"
  dependencies:
    "@dcloudio/uni-cli-shared": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-vite": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-vue": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-shared": "npm:3.0.0-4030620241128001"
    "@vue/shared": "npm:3.4.21"
    jimp: "npm:^0.10.1"
    licia: "npm:^1.29.0"
    qrcode-reader: "npm:^1.0.4"
    qrcode-terminal: "npm:^0.12.0"
    ws: "npm:^8.4.2"
  checksum: 10c0/1b23410b23775b1af3a9f6f1d56a280c950dfbabed94419bd947548c8dbdde6d59be82ba497cd64f32d8fbf9419205eb340f53d6ed2f9ee610e34440976fca4e
  languageName: node
  linkType: hard

"@dcloudio/uni-mp-xhs@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-mp-xhs@npm:3.0.0-4030620241128001"
  dependencies:
    "@dcloudio/uni-cli-shared": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-compiler": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-vite": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-vue": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-shared": "npm:3.0.0-4030620241128001"
    "@vue/shared": "npm:3.4.21"
  checksum: 10c0/cd875f9e8fea58ff77ad150ef2f8160e01f40623e0cc65b6f3d1f013396ea6351c76f9b424c13040bfcf52ddfdfb0fba0128f500bb2f81a4ea81592b336b0ac8
  languageName: node
  linkType: hard

"@dcloudio/uni-nvue-styler@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-nvue-styler@npm:3.0.0-4030620241128001"
  dependencies:
    parse-css-font: "npm:^4.0.0"
    postcss: "npm:^8.4.35"
  checksum: 10c0/bac41d741c7cf752cccfdda6e705d473888898271e0ff56d2901a53a0e58ab3db2baabfb9fac9cac75339c3c5f6d8e3128262fbd06b010b53ccee4ae3d75f4d2
  languageName: node
  linkType: hard

"@dcloudio/uni-push@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-push@npm:3.0.0-4030620241128001"
  dependencies:
    "@dcloudio/uni-cli-shared": "npm:3.0.0-4030620241128001"
  checksum: 10c0/838fa5e11860119efa385a1828148185d31e92e3596aca9912dfccfec5977256a234caf1f19a052ab2746a1cbfc6c977b05bcfccb3dadcb3b6d12ef4e2fb9192
  languageName: node
  linkType: hard

"@dcloudio/uni-quickapp-webview@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-quickapp-webview@npm:3.0.0-4030620241128001"
  dependencies:
    "@dcloudio/uni-cli-shared": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-vite": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-vue": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-shared": "npm:3.0.0-4030620241128001"
    "@vue/shared": "npm:3.4.21"
  checksum: 10c0/4861a75e99d00b4c50ef972a77c9f6115aec1a2b2456a533689bc3141022e922667848c710d3e0a794775712c5481ae09aa7bf88f8736549bfddff10ca326805
  languageName: node
  linkType: hard

"@dcloudio/uni-shared@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-shared@npm:3.0.0-4030620241128001"
  dependencies:
    "@vue/shared": "npm:3.4.21"
  checksum: 10c0/7d5400d14758cf5afeb9ff86616abebeee97995cea939a7d8312a045efaeb37ab83569d6245d2eb3c85532d87fe9520c53fd774553d02b06917d1dee6eec3e8f
  languageName: node
  linkType: hard

"@dcloudio/uni-stacktracey@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-stacktracey@npm:3.0.0-4030620241128001"
  checksum: 10c0/52909c0d4924460ecfe186e7597904dc2b6bf633231f7b5747cc5083e961f632576d4029043f16f426d840a9d27c6cb2967fff3f02f120617f7f72ed98d9fd2a
  languageName: node
  linkType: hard

"@dcloudio/uni-stat@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/uni-stat@npm:3.0.0-4030620241128001"
  dependencies:
    "@dcloudio/uni-cli-shared": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-shared": "npm:3.0.0-4030620241128001"
    debug: "npm:^4.3.3"
  checksum: 10c0/4b26f9064bb3b65ec95249a7159776b7fea631cac65609ede2dd90854b033842b27c44e7906239c83cef162d104f3a801a1c378f1b387f8bb957b6fa8ba3f0e7
  languageName: node
  linkType: hard

"@dcloudio/uni-ui@npm:^1.5.7":
  version: 1.5.7
  resolution: "@dcloudio/uni-ui@npm:1.5.7"
  checksum: 10c0/339d115accebdf70f93d7fe0312a969b07e16b1a584929ad87d6fbb51831954c628062cefbd7641ee65f7064b4495089078abea03be833909dc8552864903254
  languageName: node
  linkType: hard

"@dcloudio/vite-plugin-uni@npm:3.0.0-4030620241128001":
  version: 3.0.0-4030620241128001
  resolution: "@dcloudio/vite-plugin-uni@npm:3.0.0-4030620241128001"
  dependencies:
    "@babel/core": "npm:^7.23.3"
    "@babel/plugin-syntax-import-meta": "npm:^7.10.4"
    "@babel/plugin-transform-typescript": "npm:^7.23.3"
    "@dcloudio/uni-cli-shared": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-shared": "npm:3.0.0-4030620241128001"
    "@rollup/pluginutils": "npm:^5.0.5"
    "@vitejs/plugin-legacy": "npm:5.3.2"
    "@vitejs/plugin-vue": "npm:5.1.0"
    "@vitejs/plugin-vue-jsx": "npm:3.1.0"
    "@vue/compiler-core": "npm:3.4.21"
    "@vue/compiler-dom": "npm:3.4.21"
    "@vue/compiler-sfc": "npm:3.4.21"
    "@vue/shared": "npm:3.4.21"
    cac: "npm:6.7.9"
    debug: "npm:^4.3.3"
    estree-walker: "npm:^2.0.2"
    express: "npm:^4.17.1"
    fast-glob: "npm:^3.2.11"
    fs-extra: "npm:^10.0.0"
    hash-sum: "npm:^2.0.0"
    jsonc-parser: "npm:^3.2.0"
    magic-string: "npm:^0.30.7"
    picocolors: "npm:^1.0.0"
    terser: "npm:^5.4.0"
    unplugin-auto-import: "npm:^0.18.2"
  peerDependencies:
    vite: ^5.2.8
  bin:
    uni: bin/uni.js
  checksum: 10c0/9f6044f1ad479e8133e7498ec63779709ffa4f1828d33bb3460b98aacb3911ea7d3065b67ef13cf4b873fbc0859939688025fc2d1665c289724558f7c7bd8842
  languageName: node
  linkType: hard

"@esbuild/aix-ppc64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/aix-ppc64@npm:0.20.2"
  conditions: os=aix & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/android-arm64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/android-arm64@npm:0.20.2"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/android-arm@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/android-arm@npm:0.20.2"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/android-x64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/android-x64@npm:0.20.2"
  conditions: os=android & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/darwin-arm64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/darwin-arm64@npm:0.20.2"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/darwin-x64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/darwin-x64@npm:0.20.2"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/freebsd-arm64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/freebsd-arm64@npm:0.20.2"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/freebsd-x64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/freebsd-x64@npm:0.20.2"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/linux-arm64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/linux-arm64@npm:0.20.2"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/linux-arm@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/linux-arm@npm:0.20.2"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/linux-ia32@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/linux-ia32@npm:0.20.2"
  conditions: os=linux & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/linux-loong64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/linux-loong64@npm:0.20.2"
  conditions: os=linux & cpu=loong64
  languageName: node
  linkType: hard

"@esbuild/linux-mips64el@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/linux-mips64el@npm:0.20.2"
  conditions: os=linux & cpu=mips64el
  languageName: node
  linkType: hard

"@esbuild/linux-ppc64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/linux-ppc64@npm:0.20.2"
  conditions: os=linux & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/linux-riscv64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/linux-riscv64@npm:0.20.2"
  conditions: os=linux & cpu=riscv64
  languageName: node
  linkType: hard

"@esbuild/linux-s390x@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/linux-s390x@npm:0.20.2"
  conditions: os=linux & cpu=s390x
  languageName: node
  linkType: hard

"@esbuild/linux-x64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/linux-x64@npm:0.20.2"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/netbsd-x64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/netbsd-x64@npm:0.20.2"
  conditions: os=netbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/openbsd-x64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/openbsd-x64@npm:0.20.2"
  conditions: os=openbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/sunos-x64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/sunos-x64@npm:0.20.2"
  conditions: os=sunos & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/win32-arm64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/win32-arm64@npm:0.20.2"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/win32-ia32@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/win32-ia32@npm:0.20.2"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/win32-x64@npm:0.20.2":
  version: 0.20.2
  resolution: "@esbuild/win32-x64@npm:0.20.2"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@intlify/core-base@npm:9.1.9":
  version: 9.1.9
  resolution: "@intlify/core-base@npm:9.1.9"
  dependencies:
    "@intlify/devtools-if": "npm:9.1.9"
    "@intlify/message-compiler": "npm:9.1.9"
    "@intlify/message-resolver": "npm:9.1.9"
    "@intlify/runtime": "npm:9.1.9"
    "@intlify/shared": "npm:9.1.9"
    "@intlify/vue-devtools": "npm:9.1.9"
  checksum: 10c0/63ffa6c4eb86f9217dbbff7c56b08c0cd98a5e538e3f0bdd94038cd57dc81903afa24b5f6448d0b6439f2efca5577e6f206a6bc0577fa01eba4bb805783cce26
  languageName: node
  linkType: hard

"@intlify/core-base@npm:9.14.4":
  version: 9.14.4
  resolution: "@intlify/core-base@npm:9.14.4"
  dependencies:
    "@intlify/message-compiler": "npm:9.14.4"
    "@intlify/shared": "npm:9.14.4"
  checksum: 10c0/531856914c6b31c771e74cede91af914f90ebf13bdad61392222ad808e4ff2f336fe3f50e58a4294dddc24f943129be64828989f79cb65b1e7ac37ef48babc83
  languageName: node
  linkType: hard

"@intlify/devtools-if@npm:9.1.9":
  version: 9.1.9
  resolution: "@intlify/devtools-if@npm:9.1.9"
  dependencies:
    "@intlify/shared": "npm:9.1.9"
  checksum: 10c0/e1086a94bd54275fc811403b52ce056266b437564cba3b8a9f196ae682b3dd81d128e802179d30b27ccfcf55638860d85d0d04ba7a4d3658b105126cda451852
  languageName: node
  linkType: hard

"@intlify/message-compiler@npm:9.1.9":
  version: 9.1.9
  resolution: "@intlify/message-compiler@npm:9.1.9"
  dependencies:
    "@intlify/message-resolver": "npm:9.1.9"
    "@intlify/shared": "npm:9.1.9"
    source-map: "npm:0.6.1"
  checksum: 10c0/b96fed0fd0ac1c656e1b0720ad6aa93e42da664273692d8de5f426d4f5d8003d06106eb7e9dca3590167c58bb729f202e6c61cb77715034ca1eab9a49252e94a
  languageName: node
  linkType: hard

"@intlify/message-compiler@npm:9.14.4":
  version: 9.14.4
  resolution: "@intlify/message-compiler@npm:9.14.4"
  dependencies:
    "@intlify/shared": "npm:9.14.4"
    source-map-js: "npm:^1.0.2"
  checksum: 10c0/d8495f84d29101cae3a14dd8187f861a70755be86e03bb46019eccb39a3ee870cead0e05779db1b3efb9221167d8b2773cd59048554e56c0b47fe710f06eafa5
  languageName: node
  linkType: hard

"@intlify/message-resolver@npm:9.1.9":
  version: 9.1.9
  resolution: "@intlify/message-resolver@npm:9.1.9"
  checksum: 10c0/9bf8c1b23d4ab6841ed0b12fdc5b42fdff2a715dede4eb4bec78a7d915e2077011ca26de5dba854426dfa58b71bd0e9f0503eaf836fd7e0a9a52f5dd8112f725
  languageName: node
  linkType: hard

"@intlify/runtime@npm:9.1.9":
  version: 9.1.9
  resolution: "@intlify/runtime@npm:9.1.9"
  dependencies:
    "@intlify/message-compiler": "npm:9.1.9"
    "@intlify/message-resolver": "npm:9.1.9"
    "@intlify/shared": "npm:9.1.9"
  checksum: 10c0/181a690e79bab554de1898a020252c5cc123fb2b6e3bd3033de15e3f8205e19784bd7fac61c84fdd24d9d86c085f9571d0f90b9ddbe85b8e90491017b3e1cedf
  languageName: node
  linkType: hard

"@intlify/shared@npm:9.1.9":
  version: 9.1.9
  resolution: "@intlify/shared@npm:9.1.9"
  checksum: 10c0/c140096d67555320f3a9dc9bf057280ea59567f7606d6e04890b88e055f0c3cfbae37e208878f386c151c08d01b477ebf25949a54e4aa90187e8edeac10734a6
  languageName: node
  linkType: hard

"@intlify/shared@npm:9.14.4":
  version: 9.14.4
  resolution: "@intlify/shared@npm:9.14.4"
  checksum: 10c0/dd202fef82ba50db228182f6c55be47be42db7f8a264b62e289b6882ce095b95b9309d3dca79bd05e1526c2cf5afc74d93be1e41edbd6aaeb5751b8ca0989b32
  languageName: node
  linkType: hard

"@intlify/vue-devtools@npm:9.1.9":
  version: 9.1.9
  resolution: "@intlify/vue-devtools@npm:9.1.9"
  dependencies:
    "@intlify/message-resolver": "npm:9.1.9"
    "@intlify/runtime": "npm:9.1.9"
    "@intlify/shared": "npm:9.1.9"
  checksum: 10c0/bc934db8054b6f206c883a48688e5fa15719a5f3d200b58848dee0f31b49c4997a17283d8db07b31c2e221cd1ed4ef25edfb4354c1e01b477c001bbb2229b146
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 10c0/b1bf42535d49f11dc137f18d5e4e63a28c5569de438a221c369483731e9dac9fb797af554e8bf02b6192d1e5eba6e6402cf93900c3d0ac86391d00d04876789e
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
  checksum: 10c0/c25b6dc1598790d5b55c0947a9b7d111cfa92594db5296c3b907e2f533c033666f692a3939eadac17b1c7c40d362d0b0635dc874cbfe3e70db7c2b07cc97a5d2
  languageName: node
  linkType: hard

"@jimp/bmp@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/bmp@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/utils": "npm:^0.10.3"
    bmp-js: "npm:^0.1.0"
    core-js: "npm:^3.4.1"
  peerDependencies:
    "@jimp/custom": ">=0.3.5"
  checksum: 10c0/fd5f8eafc202df140b1d170c01c09df1691d30bb80119b81c8605bf7c3976fec9f4db35c11585c3ac70de1c9f0211bd793b96d8888f77ff7748c2a72efb4d3b7
  languageName: node
  linkType: hard

"@jimp/core@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/core@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/utils": "npm:^0.10.3"
    any-base: "npm:^1.1.0"
    buffer: "npm:^5.2.0"
    core-js: "npm:^3.4.1"
    exif-parser: "npm:^0.1.12"
    file-type: "npm:^9.0.0"
    load-bmfont: "npm:^1.3.1"
    mkdirp: "npm:^0.5.1"
    phin: "npm:^2.9.1"
    pixelmatch: "npm:^4.0.2"
    tinycolor2: "npm:^1.4.1"
  checksum: 10c0/ac75773534f692a05491eb22ded300b4c9f88ca882b4dcdbd49dada209776a942aec44b58c457b6d226cf6a75c42123d98732aed9c4b14a82929514921f0785a
  languageName: node
  linkType: hard

"@jimp/custom@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/custom@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/core": "npm:^0.10.3"
    core-js: "npm:^3.4.1"
  checksum: 10c0/46f63de2ce800c3242929896bd7ea3eb6239618d66f195602cf814d97db612bf6793210d598b743964a84cc5ba016f45a5ec64037cf24ba89407691d6c386e59
  languageName: node
  linkType: hard

"@jimp/gif@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/gif@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/utils": "npm:^0.10.3"
    core-js: "npm:^3.4.1"
    omggif: "npm:^1.0.9"
  peerDependencies:
    "@jimp/custom": ">=0.3.5"
  checksum: 10c0/501940e65db9a1973c234fc6b55e8d6cc6d81b675ec8a2ad467e30efbcd0009e28522f3bd110829cd8a65aee5c6d717ecb590bb1059fcc9a57ec055a3723a72b
  languageName: node
  linkType: hard

"@jimp/jpeg@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/jpeg@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/utils": "npm:^0.10.3"
    core-js: "npm:^3.4.1"
    jpeg-js: "npm:^0.3.4"
  peerDependencies:
    "@jimp/custom": ">=0.3.5"
  checksum: 10c0/54f11f7c5c0293bf6158eb292beb12d6f9c5f9a711af78cff61e69f2c30980b0c075a39dd917ceb942eb1ee99f0bdc0e10a3880831f1aa47270be48eebb0f22f
  languageName: node
  linkType: hard

"@jimp/plugin-blit@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/plugin-blit@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/utils": "npm:^0.10.3"
    core-js: "npm:^3.4.1"
  peerDependencies:
    "@jimp/custom": ">=0.3.5"
  checksum: 10c0/4cb4356bf2e1e8a7117c8e0526ab444e987a70a2211090bfea3f9a215678ecc9e701c63d67e535a04144c6e2f4ae6ffd90309fcbd0dab568776d3f283e3d6f98
  languageName: node
  linkType: hard

"@jimp/plugin-blur@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/plugin-blur@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/utils": "npm:^0.10.3"
    core-js: "npm:^3.4.1"
  peerDependencies:
    "@jimp/custom": ">=0.3.5"
  checksum: 10c0/81253f78d96306ec4a4d4363bd3d545d896798c8d9b7fade1dbc6730c62ff3179fd6964f9fbfd48f4c9d6a6eab5bf187518d2c8a1924b22bd11487810b7e93d4
  languageName: node
  linkType: hard

"@jimp/plugin-circle@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/plugin-circle@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/utils": "npm:^0.10.3"
    core-js: "npm:^3.4.1"
  peerDependencies:
    "@jimp/custom": ">=0.3.5"
  checksum: 10c0/7be33fc3b8fef8f0ed2d9fcbde7724d615725dc59199163c60fb656ecfe25a214fb40d6429988afd1538f50ac0592489ffab63e925446e70bdb10ed56b1bdfdc
  languageName: node
  linkType: hard

"@jimp/plugin-color@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/plugin-color@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/utils": "npm:^0.10.3"
    core-js: "npm:^3.4.1"
    tinycolor2: "npm:^1.4.1"
  peerDependencies:
    "@jimp/custom": ">=0.3.5"
  checksum: 10c0/61f2da12f87d469804113b4da8498fe7fb1229e0a5d97b6ef6bb01c6b922e88492965c52968192cde695705fc961189a167efea756ae9c4f23bdb1024f911e3a
  languageName: node
  linkType: hard

"@jimp/plugin-contain@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/plugin-contain@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/utils": "npm:^0.10.3"
    core-js: "npm:^3.4.1"
  peerDependencies:
    "@jimp/custom": ">=0.3.5"
    "@jimp/plugin-blit": ">=0.3.5"
    "@jimp/plugin-resize": ">=0.3.5"
    "@jimp/plugin-scale": ">=0.3.5"
  checksum: 10c0/3cdb7ee74b070571628e3e46c9da7744589d0b2ae2be9675ff92767e23f8736831c829bf42972962cc313b122f38971d2830076dd943caf31fea4a3922540927
  languageName: node
  linkType: hard

"@jimp/plugin-cover@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/plugin-cover@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/utils": "npm:^0.10.3"
    core-js: "npm:^3.4.1"
  peerDependencies:
    "@jimp/custom": ">=0.3.5"
    "@jimp/plugin-crop": ">=0.3.5"
    "@jimp/plugin-resize": ">=0.3.5"
    "@jimp/plugin-scale": ">=0.3.5"
  checksum: 10c0/bd9aab3aa91d01ac9a2b5fe58dc342963b3555f64c882dc82540acd79c4351662c5d5e2fa304e1cd4df01b36ffdd5d0a054bcbc65285ec24067c3c71025fcc7b
  languageName: node
  linkType: hard

"@jimp/plugin-crop@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/plugin-crop@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/utils": "npm:^0.10.3"
    core-js: "npm:^3.4.1"
  peerDependencies:
    "@jimp/custom": ">=0.3.5"
  checksum: 10c0/cb59012b7f022ad8e28f2ca8a7426517183f126eb83bf946d8f284750c122b31e93502458c0f4d7e2911f617fd5a13080b178cdc548bed6a9f7b3c8933383ea0
  languageName: node
  linkType: hard

"@jimp/plugin-displace@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/plugin-displace@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/utils": "npm:^0.10.3"
    core-js: "npm:^3.4.1"
  peerDependencies:
    "@jimp/custom": ">=0.3.5"
  checksum: 10c0/ebc32f34953e2a593e74bd83254029dc04fb9031a6261ec574d009eb6d8196d8cfc53be970b6b7e88aae672954bba2d542a362a37a4eb991977812593e56a7e7
  languageName: node
  linkType: hard

"@jimp/plugin-dither@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/plugin-dither@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/utils": "npm:^0.10.3"
    core-js: "npm:^3.4.1"
  peerDependencies:
    "@jimp/custom": ">=0.3.5"
  checksum: 10c0/6369ed09be1ac50eacd8eda1fd6cc591ac922b3ffaf8b31aee7746907769bad069fc89aa0ecae8100631c95c2591f564fb443953f8dd69080cbaa695663dbee5
  languageName: node
  linkType: hard

"@jimp/plugin-fisheye@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/plugin-fisheye@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/utils": "npm:^0.10.3"
    core-js: "npm:^3.4.1"
  peerDependencies:
    "@jimp/custom": ">=0.3.5"
  checksum: 10c0/34963650470a6ddca4b7fefc79dadb8f3aee7c22e7a82408513cbfd4bb18f287eb1c86fe24c8f1fb2e4cc3c09d4f53ffc8a0d4317fac9f2605260432d9b403d0
  languageName: node
  linkType: hard

"@jimp/plugin-flip@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/plugin-flip@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/utils": "npm:^0.10.3"
    core-js: "npm:^3.4.1"
  peerDependencies:
    "@jimp/custom": ">=0.3.5"
    "@jimp/plugin-rotate": ">=0.3.5"
  checksum: 10c0/b06195d5468e2df3b8a7750cb13cccf2eac9570a2c243d5fcded334994ee621a38f34147ff2a4793cd69df13219cb7e6314f6d1c14f06c7620c713d5f242d889
  languageName: node
  linkType: hard

"@jimp/plugin-gaussian@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/plugin-gaussian@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/utils": "npm:^0.10.3"
    core-js: "npm:^3.4.1"
  peerDependencies:
    "@jimp/custom": ">=0.3.5"
  checksum: 10c0/7742a80c96c84fe8c59e59fd48dd3d6781463b5eeb9ea7969edd4551e80f99065a5f976cdcfeb9a0ac6bf7ae810cd7a40ef41e88472a5bbc31d6a31285c41d51
  languageName: node
  linkType: hard

"@jimp/plugin-invert@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/plugin-invert@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/utils": "npm:^0.10.3"
    core-js: "npm:^3.4.1"
  peerDependencies:
    "@jimp/custom": ">=0.3.5"
  checksum: 10c0/88cd59495a16521853b06e3b418fe95644d183cef9cfd678e01db19b75fa522fb69c57fd04db65da92d244500ebde3da9882d2759eb411b42111bf5ba8ac0a28
  languageName: node
  linkType: hard

"@jimp/plugin-mask@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/plugin-mask@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/utils": "npm:^0.10.3"
    core-js: "npm:^3.4.1"
  peerDependencies:
    "@jimp/custom": ">=0.3.5"
  checksum: 10c0/dea389e5010785ab801824b43b042f74d56727a63a71093179f2fbe1b590754aba100cf73362c28995f62839e56b3fecedc6086532466b23dafc10c8f330fb36
  languageName: node
  linkType: hard

"@jimp/plugin-normalize@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/plugin-normalize@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/utils": "npm:^0.10.3"
    core-js: "npm:^3.4.1"
  peerDependencies:
    "@jimp/custom": ">=0.3.5"
  checksum: 10c0/fd4db9d4c41cf3addd63e47e576042a47d3ca1305502f3132118b512556270ea48ff700f4dfa6cbb89f2f236a8546e5c39778df35805ba95abe8fc2712fe492f
  languageName: node
  linkType: hard

"@jimp/plugin-print@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/plugin-print@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/utils": "npm:^0.10.3"
    core-js: "npm:^3.4.1"
    load-bmfont: "npm:^1.4.0"
  peerDependencies:
    "@jimp/custom": ">=0.3.5"
    "@jimp/plugin-blit": ">=0.3.5"
  checksum: 10c0/92d1406729527aca799acdb2a0377f4f75fe39d814e673d9e937dee815cbb4116b196bfd0cb2dd76a76ccc126ff93298acebad7204d8767329a1632edb2b04cb
  languageName: node
  linkType: hard

"@jimp/plugin-resize@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/plugin-resize@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/utils": "npm:^0.10.3"
    core-js: "npm:^3.4.1"
  peerDependencies:
    "@jimp/custom": ">=0.3.5"
  checksum: 10c0/89ab13a269f34467c843f13337dc099baced99f6d933b26baa7ef416501589c93e8eef796b75c7372e4f06968262b5e621d648e79681ef148ceb9c5b1162f643
  languageName: node
  linkType: hard

"@jimp/plugin-rotate@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/plugin-rotate@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/utils": "npm:^0.10.3"
    core-js: "npm:^3.4.1"
  peerDependencies:
    "@jimp/custom": ">=0.3.5"
    "@jimp/plugin-blit": ">=0.3.5"
    "@jimp/plugin-crop": ">=0.3.5"
    "@jimp/plugin-resize": ">=0.3.5"
  checksum: 10c0/2d54ca696807aabc1666e072fa25de894e1627d816d3a87f731a7befe6ec9b6b0b84b0c6e127c191396d0d7af3d41091d84ff2831df4c284833a32419c53efe0
  languageName: node
  linkType: hard

"@jimp/plugin-scale@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/plugin-scale@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/utils": "npm:^0.10.3"
    core-js: "npm:^3.4.1"
  peerDependencies:
    "@jimp/custom": ">=0.3.5"
    "@jimp/plugin-resize": ">=0.3.5"
  checksum: 10c0/0f7de9622e01dea681cbaa5a38fb20f6e5f36cdbeb55f5996243ab5ecdbfa3a73be6ca78329a9966be6e39894b37ceda90144c1cbe45b7c002b33f523cb1ba95
  languageName: node
  linkType: hard

"@jimp/plugin-shadow@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/plugin-shadow@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/utils": "npm:^0.10.3"
    core-js: "npm:^3.4.1"
  peerDependencies:
    "@jimp/custom": ">=0.3.5"
    "@jimp/plugin-blur": ">=0.3.5"
    "@jimp/plugin-resize": ">=0.3.5"
  checksum: 10c0/1f8070906ebb33a8ed5df85b642a810908dabd0e8a1f2904cc0d7bfd10a89b4d7916804ffea3d16d129ae8a912ce39669d03108b74d62e6138c7497ff3af9b16
  languageName: node
  linkType: hard

"@jimp/plugin-threshold@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/plugin-threshold@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/utils": "npm:^0.10.3"
    core-js: "npm:^3.4.1"
  peerDependencies:
    "@jimp/custom": ">=0.3.5"
    "@jimp/plugin-color": ">=0.8.0"
    "@jimp/plugin-resize": ">=0.8.0"
  checksum: 10c0/d9a8993ec5c7232552752c9a6e7bc59df1f50e93e8690f627f07fd764e515dd5653c9ad575c16bc8512f4f7a31381a5947a1c635e3695ebba14948bc0ebedf31
  languageName: node
  linkType: hard

"@jimp/plugins@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/plugins@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/plugin-blit": "npm:^0.10.3"
    "@jimp/plugin-blur": "npm:^0.10.3"
    "@jimp/plugin-circle": "npm:^0.10.3"
    "@jimp/plugin-color": "npm:^0.10.3"
    "@jimp/plugin-contain": "npm:^0.10.3"
    "@jimp/plugin-cover": "npm:^0.10.3"
    "@jimp/plugin-crop": "npm:^0.10.3"
    "@jimp/plugin-displace": "npm:^0.10.3"
    "@jimp/plugin-dither": "npm:^0.10.3"
    "@jimp/plugin-fisheye": "npm:^0.10.3"
    "@jimp/plugin-flip": "npm:^0.10.3"
    "@jimp/plugin-gaussian": "npm:^0.10.3"
    "@jimp/plugin-invert": "npm:^0.10.3"
    "@jimp/plugin-mask": "npm:^0.10.3"
    "@jimp/plugin-normalize": "npm:^0.10.3"
    "@jimp/plugin-print": "npm:^0.10.3"
    "@jimp/plugin-resize": "npm:^0.10.3"
    "@jimp/plugin-rotate": "npm:^0.10.3"
    "@jimp/plugin-scale": "npm:^0.10.3"
    "@jimp/plugin-shadow": "npm:^0.10.3"
    "@jimp/plugin-threshold": "npm:^0.10.3"
    core-js: "npm:^3.4.1"
    timm: "npm:^1.6.1"
  peerDependencies:
    "@jimp/custom": ">=0.3.5"
  checksum: 10c0/fcd0c872ffcb99fd4c0a71bf0b39037a6138cefaf51f44f7d4dade625cb43841401388af5b4bff97cb2678953b0589c22d0f6989b3b244cdbc64482d1f4fa23d
  languageName: node
  linkType: hard

"@jimp/png@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/png@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/utils": "npm:^0.10.3"
    core-js: "npm:^3.4.1"
    pngjs: "npm:^3.3.3"
  peerDependencies:
    "@jimp/custom": ">=0.3.5"
  checksum: 10c0/9f12b720a65cc334c0a8d41f3a729a14945f91fcbe3d27d3b381a6f3efa81cae55f524ef32627483e4e424ea555603205c491d3af06a2e5406e11968b526180a
  languageName: node
  linkType: hard

"@jimp/tiff@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/tiff@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    core-js: "npm:^3.4.1"
    utif: "npm:^2.0.1"
  peerDependencies:
    "@jimp/custom": ">=0.3.5"
  checksum: 10c0/6e61ea25cfca3a52245e4d7147d7fa9aa8634bab778f2952b2eb49f26ec35e595cb8601af0abc2ec1ef74f063d0102ebde55dceb98e3c137fe185e8b8900e32d
  languageName: node
  linkType: hard

"@jimp/types@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/types@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/bmp": "npm:^0.10.3"
    "@jimp/gif": "npm:^0.10.3"
    "@jimp/jpeg": "npm:^0.10.3"
    "@jimp/png": "npm:^0.10.3"
    "@jimp/tiff": "npm:^0.10.3"
    core-js: "npm:^3.4.1"
    timm: "npm:^1.6.1"
  peerDependencies:
    "@jimp/custom": ">=0.3.5"
  checksum: 10c0/189678cd168324865e67b3b5e10e9ca78cdf6397dd9943da60c839908500e80e123644d58be9fc85e3cf92665acfcbee2d50470a5910152eead030f34b5e33d5
  languageName: node
  linkType: hard

"@jimp/utils@npm:^0.10.3":
  version: 0.10.3
  resolution: "@jimp/utils@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    core-js: "npm:^3.4.1"
    regenerator-runtime: "npm:^0.13.3"
  checksum: 10c0/b7a33cf9903642b8f5dd6960d0f2ed2099a0037899eb33700cda83822d2d9b89a01fc9d6b73e4ba2a466e734195897ecb29541d23492ddc1ea79f0c3f4a16745
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.3, @jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.8
  resolution: "@jridgewell/gen-mapping@npm:0.3.8"
  dependencies:
    "@jridgewell/set-array": "npm:^1.2.1"
    "@jridgewell/sourcemap-codec": "npm:^1.4.10"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10c0/c668feaf86c501d7c804904a61c23c67447b2137b813b9ce03eca82cb9d65ac7006d766c218685d76e3d72828279b6ee26c347aa1119dab23fbaf36aed51585a
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 10c0/d502e6fb516b35032331406d4e962c21fe77cdf1cbdb49c6142bcbd9e30507094b18972778a6e27cbad756209cfe34b1a27729e6fa08a2eb92b33943f680cf1e
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.2.1":
  version: 1.2.1
  resolution: "@jridgewell/set-array@npm:1.2.1"
  checksum: 10c0/2a5aa7b4b5c3464c895c802d8ae3f3d2b92fcbe84ad12f8d0bfbb1f5ad006717e7577ee1fd2eac00c088abe486c7adb27976f45d2941ff6b0b92b2c3302c60f4
  languageName: node
  linkType: hard

"@jridgewell/source-map@npm:^0.3.3":
  version: 0.3.6
  resolution: "@jridgewell/source-map@npm:0.3.6"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
  checksum: 10c0/6a4ecc713ed246ff8e5bdcc1ef7c49aaa93f7463d948ba5054dda18b02dcc6a055e2828c577bcceee058f302ce1fc95595713d44f5c45e43d459f88d267f2f04
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.14, @jridgewell/sourcemap-codec@npm:^1.5.0":
  version: 1.5.0
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.0"
  checksum: 10c0/2eb864f276eb1096c3c11da3e9bb518f6d9fc0023c78344cdc037abadc725172c70314bdb360f2d4b7bffec7f5d657ce006816bc5d4ecb35e61b66132db00c18
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.19, @jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25":
  version: 0.3.25
  resolution: "@jridgewell/trace-mapping@npm:0.3.25"
  dependencies:
    "@jridgewell/resolve-uri": "npm:^3.1.0"
    "@jridgewell/sourcemap-codec": "npm:^1.4.14"
  checksum: 10c0/3d1ce6ebc69df9682a5a8896b414c6537e428a1d68b02fcc8363b04284a8ca0df04d0ee3013132252ab14f2527bc13bea6526a912ecb5658f0e39fd2860b4df4
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": "npm:2.0.5"
    run-parallel: "npm:^1.1.9"
  checksum: 10c0/732c3b6d1b1e967440e65f284bd06e5821fedf10a1bea9ed2bb75956ea1f30e08c44d3def9d6a230666574edbaf136f8cfd319c14fd1f87c66e6a44449afb2eb
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 10c0/88dafe5e3e29a388b07264680dc996c17f4bda48d163a9d4f5c1112979f0ce8ec72aa7116122c350b4e7976bc5566dc3ddb579be1ceaacc727872eb4ed93926d
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": "npm:2.1.5"
    fastq: "npm:^1.6.0"
  checksum: 10c0/db9de047c3bb9b51f9335a7bb46f4fcfb6829fb628318c12115fbaf7d369bfce71c15b103d1fc3b464812d936220ee9bc1c8f762d032c9f6be9acc99249095b1
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: "npm:^7.1.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.1"
    lru-cache: "npm:^10.0.1"
    socks-proxy-agent: "npm:^8.0.3"
  checksum: 10c0/efe37b982f30740ee77696a80c196912c274ecd2cb243bc6ae7053a50c733ce0f6c09fda085145f33ecf453be19654acca74b69e81eaad4c90f00ccffe2f9271
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10c0/c90935d5ce670c87b6b14fab04a965a3b8137e585f8b2a6257263bd7f97756dd736cb165bb470e5156a9e718ecd99413dccc54b1138c1a46d6ec7cf325982fe5
  languageName: node
  linkType: hard

"@parcel/watcher-android-arm64@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-android-arm64@npm:2.5.1"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@parcel/watcher-darwin-arm64@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-darwin-arm64@npm:2.5.1"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@parcel/watcher-darwin-x64@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-darwin-x64@npm:2.5.1"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@parcel/watcher-freebsd-x64@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-freebsd-x64@npm:2.5.1"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@parcel/watcher-linux-arm-glibc@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-linux-arm-glibc@npm:2.5.1"
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@parcel/watcher-linux-arm-musl@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-linux-arm-musl@npm:2.5.1"
  conditions: os=linux & cpu=arm & libc=musl
  languageName: node
  linkType: hard

"@parcel/watcher-linux-arm64-glibc@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-linux-arm64-glibc@npm:2.5.1"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@parcel/watcher-linux-arm64-musl@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-linux-arm64-musl@npm:2.5.1"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@parcel/watcher-linux-x64-glibc@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-linux-x64-glibc@npm:2.5.1"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@parcel/watcher-linux-x64-musl@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-linux-x64-musl@npm:2.5.1"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@parcel/watcher-win32-arm64@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-win32-arm64@npm:2.5.1"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@parcel/watcher-win32-ia32@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-win32-ia32@npm:2.5.1"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@parcel/watcher-win32-x64@npm:2.5.1":
  version: 2.5.1
  resolution: "@parcel/watcher-win32-x64@npm:2.5.1"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@parcel/watcher@npm:^2.4.1":
  version: 2.5.1
  resolution: "@parcel/watcher@npm:2.5.1"
  dependencies:
    "@parcel/watcher-android-arm64": "npm:2.5.1"
    "@parcel/watcher-darwin-arm64": "npm:2.5.1"
    "@parcel/watcher-darwin-x64": "npm:2.5.1"
    "@parcel/watcher-freebsd-x64": "npm:2.5.1"
    "@parcel/watcher-linux-arm-glibc": "npm:2.5.1"
    "@parcel/watcher-linux-arm-musl": "npm:2.5.1"
    "@parcel/watcher-linux-arm64-glibc": "npm:2.5.1"
    "@parcel/watcher-linux-arm64-musl": "npm:2.5.1"
    "@parcel/watcher-linux-x64-glibc": "npm:2.5.1"
    "@parcel/watcher-linux-x64-musl": "npm:2.5.1"
    "@parcel/watcher-win32-arm64": "npm:2.5.1"
    "@parcel/watcher-win32-ia32": "npm:2.5.1"
    "@parcel/watcher-win32-x64": "npm:2.5.1"
    detect-libc: "npm:^1.0.3"
    is-glob: "npm:^4.0.3"
    micromatch: "npm:^4.0.5"
    node-addon-api: "npm:^7.0.0"
    node-gyp: "npm:latest"
  dependenciesMeta:
    "@parcel/watcher-android-arm64":
      optional: true
    "@parcel/watcher-darwin-arm64":
      optional: true
    "@parcel/watcher-darwin-x64":
      optional: true
    "@parcel/watcher-freebsd-x64":
      optional: true
    "@parcel/watcher-linux-arm-glibc":
      optional: true
    "@parcel/watcher-linux-arm-musl":
      optional: true
    "@parcel/watcher-linux-arm64-glibc":
      optional: true
    "@parcel/watcher-linux-arm64-musl":
      optional: true
    "@parcel/watcher-linux-x64-glibc":
      optional: true
    "@parcel/watcher-linux-x64-musl":
      optional: true
    "@parcel/watcher-win32-arm64":
      optional: true
    "@parcel/watcher-win32-ia32":
      optional: true
    "@parcel/watcher-win32-x64":
      optional: true
  checksum: 10c0/8f35073d0c0b34a63d4c8d2213482f0ebc6a25de7b2cdd415d19cb929964a793cb285b68d1d50bfb732b070b3c82a2fdb4eb9c250eab709a1cd9d63345455a82
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 10c0/5bd7576bb1b38a47a7fc7b51ac9f38748e772beebc56200450c4a817d712232b8f1d3ef70532c80840243c657d491cf6a6be1e3a214cff907645819fdc34aadd
  languageName: node
  linkType: hard

"@rollup/pluginutils@npm:^5.0.5, @rollup/pluginutils@npm:^5.1.3, @rollup/pluginutils@npm:^5.1.4":
  version: 5.1.4
  resolution: "@rollup/pluginutils@npm:5.1.4"
  dependencies:
    "@types/estree": "npm:^1.0.0"
    estree-walker: "npm:^2.0.2"
    picomatch: "npm:^4.0.2"
  peerDependencies:
    rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
  peerDependenciesMeta:
    rollup:
      optional: true
  checksum: 10c0/6d58fbc6f1024eb4b087bc9bf59a1d655a8056a60c0b4021d3beaeec3f0743503f52467fd89d2cf0e7eccf2831feb40a05ad541a17637ea21ba10b21c2004deb
  languageName: node
  linkType: hard

"@rollup/rollup-android-arm-eabi@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-android-arm-eabi@npm:4.43.0"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@rollup/rollup-android-arm64@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-android-arm64@npm:4.43.0"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-darwin-arm64@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-darwin-arm64@npm:4.43.0"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-darwin-x64@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-darwin-x64@npm:4.43.0"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@rollup/rollup-freebsd-arm64@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-freebsd-arm64@npm:4.43.0"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-freebsd-x64@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-freebsd-x64@npm:4.43.0"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm-gnueabihf@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-linux-arm-gnueabihf@npm:4.43.0"
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm-musleabihf@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-linux-arm-musleabihf@npm:4.43.0"
  conditions: os=linux & cpu=arm & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm64-gnu@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-linux-arm64-gnu@npm:4.43.0"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm64-musl@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-linux-arm64-musl@npm:4.43.0"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-loongarch64-gnu@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-linux-loongarch64-gnu@npm:4.43.0"
  conditions: os=linux & cpu=loong64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-powerpc64le-gnu@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-linux-powerpc64le-gnu@npm:4.43.0"
  conditions: os=linux & cpu=ppc64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-riscv64-gnu@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-linux-riscv64-gnu@npm:4.43.0"
  conditions: os=linux & cpu=riscv64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-riscv64-musl@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-linux-riscv64-musl@npm:4.43.0"
  conditions: os=linux & cpu=riscv64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-s390x-gnu@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-linux-s390x-gnu@npm:4.43.0"
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-x64-gnu@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-linux-x64-gnu@npm:4.43.0"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-x64-musl@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-linux-x64-musl@npm:4.43.0"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-win32-arm64-msvc@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-win32-arm64-msvc@npm:4.43.0"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-win32-ia32-msvc@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-win32-ia32-msvc@npm:4.43.0"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@rollup/rollup-win32-x64-msvc@npm:4.43.0":
  version: 4.43.0
  resolution: "@rollup/rollup-win32-x64-msvc@npm:4.43.0"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@types/estree@npm:1.0.7":
  version: 1.0.7
  resolution: "@types/estree@npm:1.0.7"
  checksum: 10c0/be815254316882f7c40847336cd484c3bc1c3e34f710d197160d455dc9d6d050ffbf4c3bc76585dba86f737f020ab20bdb137ebe0e9116b0c86c7c0342221b8c
  languageName: node
  linkType: hard

"@types/estree@npm:^1.0.0":
  version: 1.0.8
  resolution: "@types/estree@npm:1.0.8"
  checksum: 10c0/39d34d1afaa338ab9763f37ad6066e3f349444f9052b9676a7cc0252ef9485a41c6d81c9c4e0d26e9077993354edf25efc853f3224dd4b447175ef62bdcc86a5
  languageName: node
  linkType: hard

"@types/json-schema@npm:^7.0.8":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 10c0/a996a745e6c5d60292f36731dd41341339d4eeed8180bb09226e5c8d23759067692b1d88e5d91d72ee83dfc00d3aca8e7bd43ea120516c17922cbcb7c3e252db
  languageName: node
  linkType: hard

"@vitejs/plugin-legacy@npm:5.3.2":
  version: 5.3.2
  resolution: "@vitejs/plugin-legacy@npm:5.3.2"
  dependencies:
    "@babel/core": "npm:^7.23.9"
    "@babel/preset-env": "npm:^7.23.9"
    browserslist: "npm:^4.23.0"
    browserslist-to-esbuild: "npm:^2.1.1"
    core-js: "npm:^3.36.0"
    magic-string: "npm:^0.30.7"
    regenerator-runtime: "npm:^0.14.1"
    systemjs: "npm:^6.14.3"
  peerDependencies:
    terser: ^5.4.0
    vite: ^5.0.0
  checksum: 10c0/f5fee7982c6a8c8e58ba9bb63157cb92bf76baa6d45638c7fc61991a4abe834934f30c253fd6249e0b152c5915f17081de70f732fabf82dfab19ffbe60de43f3
  languageName: node
  linkType: hard

"@vitejs/plugin-vue-jsx@npm:3.1.0":
  version: 3.1.0
  resolution: "@vitejs/plugin-vue-jsx@npm:3.1.0"
  dependencies:
    "@babel/core": "npm:^7.23.3"
    "@babel/plugin-transform-typescript": "npm:^7.23.3"
    "@vue/babel-plugin-jsx": "npm:^1.1.5"
  peerDependencies:
    vite: ^4.0.0 || ^5.0.0
    vue: ^3.0.0
  checksum: 10c0/9d3d10d9ceae9710e819638a4c882d743b04d13fd962edf5b070d57b05b23f77fa1ec4989ed1707f33e64991865bcf062c6036081190f87b72b6610c9ef6a678
  languageName: node
  linkType: hard

"@vitejs/plugin-vue@npm:5.1.0":
  version: 5.1.0
  resolution: "@vitejs/plugin-vue@npm:5.1.0"
  peerDependencies:
    vite: ^5.0.0
    vue: ^3.2.25
  checksum: 10c0/072b8ed20713fdb3f1e4680c5c4418e3a815118ba04564e4bddad35b7779095881cb6d201bf05375269fe9e5ba6cfd8312e6e3fb2ef0a469dd478588b92e78d4
  languageName: node
  linkType: hard

"@volar/language-core@npm:1.11.1, @volar/language-core@npm:~1.11.1":
  version: 1.11.1
  resolution: "@volar/language-core@npm:1.11.1"
  dependencies:
    "@volar/source-map": "npm:1.11.1"
  checksum: 10c0/92c4439e3a9ccc534c970031388c318740f6fa032283d03e136c6c8c0228f549c68a7c363af1a28252617a0dca6069e14028329ac906d5acf1912931d0cdcb69
  languageName: node
  linkType: hard

"@volar/source-map@npm:1.11.1, @volar/source-map@npm:~1.11.1":
  version: 1.11.1
  resolution: "@volar/source-map@npm:1.11.1"
  dependencies:
    muggle-string: "npm:^0.3.1"
  checksum: 10c0/0bfc639889802705f8036ea8b2052a95a4d691a68bc2b6744ba8b9d312d887393dd3278101180a5ee5304972899d493972a483afafd41e097968746c77d724cb
  languageName: node
  linkType: hard

"@volar/typescript@npm:~1.11.1":
  version: 1.11.1
  resolution: "@volar/typescript@npm:1.11.1"
  dependencies:
    "@volar/language-core": "npm:1.11.1"
    path-browserify: "npm:^1.0.1"
  checksum: 10c0/86fe153db3a14d8eb3632784a1d7fcbfbfb51fa5517c3878bfdd49ee8d15a83b1a09f9c589454b7396454c104d3a8e2db3a987dc99b37c33816772fc3e292bf2
  languageName: node
  linkType: hard

"@vue/babel-helper-vue-transform-on@npm:1.4.0":
  version: 1.4.0
  resolution: "@vue/babel-helper-vue-transform-on@npm:1.4.0"
  checksum: 10c0/9ab3ece9ec79f957e5d4c1eb4b2879120ae54ad359c52fc9c66c3ca36db0da925785d6191317bba90e3cc9fae437cde5d9c6f3c375f7d9225eba8a20c1d03235
  languageName: node
  linkType: hard

"@vue/babel-plugin-jsx@npm:^1.1.5":
  version: 1.4.0
  resolution: "@vue/babel-plugin-jsx@npm:1.4.0"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.25.9"
    "@babel/helper-plugin-utils": "npm:^7.26.5"
    "@babel/plugin-syntax-jsx": "npm:^7.25.9"
    "@babel/template": "npm:^7.26.9"
    "@babel/traverse": "npm:^7.26.9"
    "@babel/types": "npm:^7.26.9"
    "@vue/babel-helper-vue-transform-on": "npm:1.4.0"
    "@vue/babel-plugin-resolve-type": "npm:1.4.0"
    "@vue/shared": "npm:^3.5.13"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  peerDependenciesMeta:
    "@babel/core":
      optional: true
  checksum: 10c0/c3c2373ff140c29a2292aa56d57bc8b2d26f962b71c324b773d6fb77b5eec145326199a43497fe39993309ea1d571d8c400dc3d6804fce8597151c48ed3cb3e1
  languageName: node
  linkType: hard

"@vue/babel-plugin-resolve-type@npm:1.4.0":
  version: 1.4.0
  resolution: "@vue/babel-plugin-resolve-type@npm:1.4.0"
  dependencies:
    "@babel/code-frame": "npm:^7.26.2"
    "@babel/helper-module-imports": "npm:^7.25.9"
    "@babel/helper-plugin-utils": "npm:^7.26.5"
    "@babel/parser": "npm:^7.26.9"
    "@vue/compiler-sfc": "npm:^3.5.13"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/289c8893e3477dbfa95d0b777ed00ad7bace77605090a5858a9b2d4294f93bc3d81b6091e3c2ffd98aa92fb676643dec9d7a9428c87a0a67de1d7dc546a32c83
  languageName: node
  linkType: hard

"@vue/compiler-core@npm:3.4.21":
  version: 3.4.21
  resolution: "@vue/compiler-core@npm:3.4.21"
  dependencies:
    "@babel/parser": "npm:^7.23.9"
    "@vue/shared": "npm:3.4.21"
    entities: "npm:^4.5.0"
    estree-walker: "npm:^2.0.2"
    source-map-js: "npm:^1.0.2"
  checksum: 10c0/3ee871b95e17948d10375093c8dd3265923f844528a24ac67512c201ddb9b628021c010565f3e50f2e551b217c502e80a7901384f616a977a04f81e68c64a37c
  languageName: node
  linkType: hard

"@vue/compiler-core@npm:3.5.16":
  version: 3.5.16
  resolution: "@vue/compiler-core@npm:3.5.16"
  dependencies:
    "@babel/parser": "npm:^7.27.2"
    "@vue/shared": "npm:3.5.16"
    entities: "npm:^4.5.0"
    estree-walker: "npm:^2.0.2"
    source-map-js: "npm:^1.2.1"
  checksum: 10c0/1a6f1446320467eac382c9ee567bd6017a61d374eebe48cbf948badb13e14beb0f96645e2cb8c4bfff565aa4948f1d836352bea511e5f3322c51cc5921caf42a
  languageName: node
  linkType: hard

"@vue/compiler-dom@npm:3.4.21":
  version: 3.4.21
  resolution: "@vue/compiler-dom@npm:3.4.21"
  dependencies:
    "@vue/compiler-core": "npm:3.4.21"
    "@vue/shared": "npm:3.4.21"
  checksum: 10c0/b4a1099eddacded2663d12388b48088ca0be0d8969a070476f49e4e65da9b22851fc897cc693662b178e7e7fdee98fcf9ea3617a1f626c3a1b2089815cb1264e
  languageName: node
  linkType: hard

"@vue/compiler-dom@npm:3.5.16, @vue/compiler-dom@npm:^3.3.0":
  version: 3.5.16
  resolution: "@vue/compiler-dom@npm:3.5.16"
  dependencies:
    "@vue/compiler-core": "npm:3.5.16"
    "@vue/shared": "npm:3.5.16"
  checksum: 10c0/e3ed5b50977cbb240abc2b8a71497254810e433a2e4a5eb254900c46abc6494b99df7e69ae79d7122c29c2e1cc4605c5b4925bf280e83e4f918098194b2894cf
  languageName: node
  linkType: hard

"@vue/compiler-sfc@npm:3.4.21":
  version: 3.4.21
  resolution: "@vue/compiler-sfc@npm:3.4.21"
  dependencies:
    "@babel/parser": "npm:^7.23.9"
    "@vue/compiler-core": "npm:3.4.21"
    "@vue/compiler-dom": "npm:3.4.21"
    "@vue/compiler-ssr": "npm:3.4.21"
    "@vue/shared": "npm:3.4.21"
    estree-walker: "npm:^2.0.2"
    magic-string: "npm:^0.30.7"
    postcss: "npm:^8.4.35"
    source-map-js: "npm:^1.0.2"
  checksum: 10c0/8d9a6ee07a9c542528f09b7a99e5d40e9752dca39251994e4309cb6121997c47db6818be75555aa69fb4f0bd54820bc7675c0c6e2ea5afe339f09d40890d26a9
  languageName: node
  linkType: hard

"@vue/compiler-sfc@npm:3.5.16, @vue/compiler-sfc@npm:^3.5.13":
  version: 3.5.16
  resolution: "@vue/compiler-sfc@npm:3.5.16"
  dependencies:
    "@babel/parser": "npm:^7.27.2"
    "@vue/compiler-core": "npm:3.5.16"
    "@vue/compiler-dom": "npm:3.5.16"
    "@vue/compiler-ssr": "npm:3.5.16"
    "@vue/shared": "npm:3.5.16"
    estree-walker: "npm:^2.0.2"
    magic-string: "npm:^0.30.17"
    postcss: "npm:^8.5.3"
    source-map-js: "npm:^1.2.1"
  checksum: 10c0/b9614aadde2c85f87fac3ee6f6b31209cec294c9b7c7aeb23d0dc92bdb7a0ee7ec6dc6d11f2bbc7403c19b4eb8df5d3dc9ae915e8cd74d77c50024608cabc02b
  languageName: node
  linkType: hard

"@vue/compiler-ssr@npm:3.4.21":
  version: 3.4.21
  resolution: "@vue/compiler-ssr@npm:3.4.21"
  dependencies:
    "@vue/compiler-dom": "npm:3.4.21"
    "@vue/shared": "npm:3.4.21"
  checksum: 10c0/bae2b76f8619f258a90e2964cdcebef44aa240ae64be6bb08227f3404239c66f3d77fb25b88a809d9b29063a2f0f423595c8be8e5f7c80dc8337da2aad4f6fdc
  languageName: node
  linkType: hard

"@vue/compiler-ssr@npm:3.5.16":
  version: 3.5.16
  resolution: "@vue/compiler-ssr@npm:3.5.16"
  dependencies:
    "@vue/compiler-dom": "npm:3.5.16"
    "@vue/shared": "npm:3.5.16"
  checksum: 10c0/e2f0e652981ded459f7bf1c9821d34332207e01b43db819281f632bdc0e7b8518c485019e65e010724f86acf520c715ae4b35cb717d2c0ad0f7cfacf83933df6
  languageName: node
  linkType: hard

"@vue/consolidate@npm:^1.0.0":
  version: 1.0.0
  resolution: "@vue/consolidate@npm:1.0.0"
  checksum: 10c0/e61605fc2e6c0be79e121c367e46ba9e9417376744fd548d8618e63f3419983236ca6f20fdadae261b0b93b7c0fe41db5591d88c9f56ac85319daa2c15d37807
  languageName: node
  linkType: hard

"@vue/devtools-api@npm:^6.5.0, @vue/devtools-api@npm:^6.6.4":
  version: 6.6.4
  resolution: "@vue/devtools-api@npm:6.6.4"
  checksum: 10c0/0a993ae23618166e1bee5a7c14cebd8312752b93c143cbdd48fb2d0f7ade070d0e6baf757cd920d4681fef8f9acf29515162160f38cc7410f9a684d2df21b6de
  languageName: node
  linkType: hard

"@vue/language-core@npm:1.8.27":
  version: 1.8.27
  resolution: "@vue/language-core@npm:1.8.27"
  dependencies:
    "@volar/language-core": "npm:~1.11.1"
    "@volar/source-map": "npm:~1.11.1"
    "@vue/compiler-dom": "npm:^3.3.0"
    "@vue/shared": "npm:^3.3.0"
    computeds: "npm:^0.0.1"
    minimatch: "npm:^9.0.3"
    muggle-string: "npm:^0.3.1"
    path-browserify: "npm:^1.0.1"
    vue-template-compiler: "npm:^2.7.14"
  peerDependencies:
    typescript: "*"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/2018214d8ce2643d19e8e84eddaeacddca28b2980984d7916d97f97556c3716be184cf9f8c4f506d072a11f265401e3bc0391117cf7cfcc1e4a25048f4432dc7
  languageName: node
  linkType: hard

"@vue/reactivity@npm:3.5.16":
  version: 3.5.16
  resolution: "@vue/reactivity@npm:3.5.16"
  dependencies:
    "@vue/shared": "npm:3.5.16"
  checksum: 10c0/88e8ff0b62518dac6db5adf7b130ec7e389ef5133c861223aa7ec89d310ee4fbc4cde42a53873187d98a8a113c507e41c366c3a3249c6714dfb5d1b47d409402
  languageName: node
  linkType: hard

"@vue/runtime-core@npm:3.5.16, @vue/runtime-core@npm:^3.4.21":
  version: 3.5.16
  resolution: "@vue/runtime-core@npm:3.5.16"
  dependencies:
    "@vue/reactivity": "npm:3.5.16"
    "@vue/shared": "npm:3.5.16"
  checksum: 10c0/8695a37049020a789b77c3ada3f4bb595b1149644d8c98e42c37889c863fa4bad5a2c8afa105ce1adf767d0e5f7e7869def7a9864dc18bce1c79877f3006246a
  languageName: node
  linkType: hard

"@vue/runtime-dom@npm:3.5.16":
  version: 3.5.16
  resolution: "@vue/runtime-dom@npm:3.5.16"
  dependencies:
    "@vue/reactivity": "npm:3.5.16"
    "@vue/runtime-core": "npm:3.5.16"
    "@vue/shared": "npm:3.5.16"
    csstype: "npm:^3.1.3"
  checksum: 10c0/3e18a6c80b51fde47efa65b8da4cd3876ac49a295bf8e2f26755db97fde413248d72697e288085e90f882c9e14731f6aa0eb2b8f85e7e169e4cfe519c2df4e76
  languageName: node
  linkType: hard

"@vue/server-renderer@npm:3.4.21":
  version: 3.4.21
  resolution: "@vue/server-renderer@npm:3.4.21"
  dependencies:
    "@vue/compiler-ssr": "npm:3.4.21"
    "@vue/shared": "npm:3.4.21"
  peerDependencies:
    vue: 3.4.21
  checksum: 10c0/3ff91392473cea8d85a11e8315bf378fd0cb4b5e4f650acad3b1bc672ceb3a0e29d22d4860186b06697b72a8ab544d67ba7969e77fed07a402c3528f90c764ed
  languageName: node
  linkType: hard

"@vue/server-renderer@npm:3.5.16":
  version: 3.5.16
  resolution: "@vue/server-renderer@npm:3.5.16"
  dependencies:
    "@vue/compiler-ssr": "npm:3.5.16"
    "@vue/shared": "npm:3.5.16"
  peerDependencies:
    vue: 3.5.16
  checksum: 10c0/c6adccf54caf8bac495cf53cd41ae804eeee8257b5f30c611ff5bcb7741c2488b7c9126fe5857b93753742cba62be43fbe8db1be155b8f3f90b256d2f7a39236
  languageName: node
  linkType: hard

"@vue/shared@npm:3.4.21":
  version: 3.4.21
  resolution: "@vue/shared@npm:3.4.21"
  checksum: 10c0/79cba4228c3c1769ba8024302d7dbebf6ed1b77fb2e7a69e635cdebaa1c18b409e9c27ce27ccbe3a98e702a7e2dae1b87754d87f0b29adfe2a8f9e1e7c7899d5
  languageName: node
  linkType: hard

"@vue/shared@npm:3.5.16, @vue/shared@npm:^3.3.0, @vue/shared@npm:^3.5.13":
  version: 3.5.16
  resolution: "@vue/shared@npm:3.5.16"
  checksum: 10c0/242ecc41f4c4e8f7f5d8714d715f4a78e31ead988da47cb369b88bd2f53aacc0f1db8c15dfac726e2a3ebe1104689bddd65c5c349ca5097e6657b2af2098c2f7
  languageName: node
  linkType: hard

"@vue/tsconfig@npm:^0.1.3":
  version: 0.1.3
  resolution: "@vue/tsconfig@npm:0.1.3"
  peerDependencies:
    "@types/node": "*"
  peerDependenciesMeta:
    "@types/node":
      optional: true
  checksum: 10c0/c41861b2a2e5528f894c19bd59b9225500fe6a0e85dde636e3023e57c66e3121f6d4afa1d278b93c9142f0526354e960ee5565ee5a6f16a5a898d3988a383f77
  languageName: node
  linkType: hard

"abbrev@npm:^3.0.0":
  version: 3.0.1
  resolution: "abbrev@npm:3.0.1"
  checksum: 10c0/21ba8f574ea57a3106d6d35623f2c4a9111d9ee3e9a5be47baed46ec2457d2eac46e07a5c4a60186f88cb98abbe3e24f2d4cca70bc2b12f1692523e2209a9ccf
  languageName: node
  linkType: hard

"accepts@npm:~1.3.8":
  version: 1.3.8
  resolution: "accepts@npm:1.3.8"
  dependencies:
    mime-types: "npm:~2.1.34"
    negotiator: "npm:0.6.3"
  checksum: 10c0/3a35c5f5586cfb9a21163ca47a5f77ac34fa8ceb5d17d2fa2c0d81f41cbd7f8c6fa52c77e2c039acc0f4d09e71abdc51144246900f6bef5e3c4b333f77d89362
  languageName: node
  linkType: hard

"acorn@npm:^8.14.0":
  version: 8.15.0
  resolution: "acorn@npm:8.15.0"
  bin:
    acorn: bin/acorn
  checksum: 10c0/dec73ff59b7d6628a01eebaece7f2bdb8bb62b9b5926dcad0f8931f2b8b79c2be21f6c68ac095592adb5adb15831a3635d9343e6a91d028bbe85d564875ec3ec
  languageName: node
  linkType: hard

"address@npm:^1.1.2":
  version: 1.2.2
  resolution: "address@npm:1.2.2"
  checksum: 10c0/1c8056b77fb124456997b78ed682ecc19d2fd7ea8bd5850a2aa8c3e3134c913847c57bcae418622efd32ba858fa1e242a40a251ac31da0515664fc0ac03a047d
  languageName: node
  linkType: hard

"adm-zip@npm:^0.5.12":
  version: 0.5.16
  resolution: "adm-zip@npm:0.5.16"
  checksum: 10c0/6f10119d4570c7ba76dcf428abb8d3f69e63f92e51f700a542b43d4c0130373dd2ddfc8f85059f12d4a843703a90c3970cfd17876844b4f3f48bf042bfa6b49f
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 10c0/6192b580c5b1d8fb399b9c62bf8343d76654c2dd62afcb9a52b2cf44a8b6ace1e3b704d3fe3547d91555c857d3df02603341ff2cb961b9cfe2b12f9f3c38ee11
  languageName: node
  linkType: hard

"ajv-keywords@npm:^3.5.2":
  version: 3.5.2
  resolution: "ajv-keywords@npm:3.5.2"
  peerDependencies:
    ajv: ^6.9.1
  checksum: 10c0/0c57a47cbd656e8cdfd99d7c2264de5868918ffa207c8d7a72a7f63379d4333254b2ba03d69e3c035e996a3fd3eb6d5725d7a1597cca10694296e32510546360
  languageName: node
  linkType: hard

"ajv@npm:^6.12.5":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: "npm:^3.1.1"
    fast-json-stable-stringify: "npm:^2.0.0"
    json-schema-traverse: "npm:^0.4.1"
    uri-js: "npm:^4.2.2"
  checksum: 10c0/41e23642cbe545889245b9d2a45854ebba51cda6c778ebced9649420d9205f2efb39cb43dbc41e358409223b1ea43303ae4839db682c848b891e4811da1a5a71
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 10c0/9a64bb8627b434ba9327b60c027742e5d17ac69277960d041898596271d992d4d52ba7267a63ca10232e29f6107fc8a835f6ce8d719b88c5f8493f8254813737
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 10c0/a91daeddd54746338478eef88af3439a7edf30f8e23196e2d6ed182da9add559c601266dbef01c2efa46a958ad6f1f8b176799657616c702b5b02e799e7fd8dc
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: 10c0/895a23929da416f2bd3de7e9cb4eabd340949328ab85ddd6e484a637d8f6820d485f53933446f5291c3b760cbc488beb8e88573dd0f9c7daf83dccc8fe81b041
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 10c0/5d1ec38c123984bcedd996eac680d548f31828bd679a66db2bdf11844634dde55fec3efa9c6bb1d89056a5e79c1ac540c4c784d592ea1d25028a92227d2f2d5c
  languageName: node
  linkType: hard

"any-base@npm:^1.1.0":
  version: 1.1.0
  resolution: "any-base@npm:1.1.0"
  checksum: 10c0/1255cccb2c2ead4aa182eca000fd8aa0c1991d91781ff54e26323c132117ed23eb10eef057c0dcf1d919ab30d78e5b11bd8d88940352fc84586ecb961cc76466
  languageName: node
  linkType: hard

"anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: "npm:^3.0.0"
    picomatch: "npm:^2.0.4"
  checksum: 10c0/57b06ae984bc32a0d22592c87384cd88fe4511b1dd7581497831c56d41939c8a001b28e7b853e1450f2bf61992dfcaa8ae2d0d161a0a90c4fb631ef07098fbac
  languageName: node
  linkType: hard

"array-flatten@npm:1.1.1":
  version: 1.1.1
  resolution: "array-flatten@npm:1.1.1"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"autoprefixer@npm:^10.4.19":
  version: 10.4.21
  resolution: "autoprefixer@npm:10.4.21"
  dependencies:
    browserslist: "npm:^4.24.4"
    caniuse-lite: "npm:^1.0.30001702"
    fraction.js: "npm:^4.3.7"
    normalize-range: "npm:^0.1.2"
    picocolors: "npm:^1.1.1"
    postcss-value-parser: "npm:^4.2.0"
  peerDependencies:
    postcss: ^8.1.0
  bin:
    autoprefixer: bin/autoprefixer
  checksum: 10c0/de5b71d26d0baff4bbfb3d59f7cf7114a6030c9eeb66167acf49a32c5b61c68e308f1e0f869d92334436a221035d08b51cd1b2f2c4689b8d955149423c16d4d4
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs2@npm:^0.4.10":
  version: 0.4.13
  resolution: "babel-plugin-polyfill-corejs2@npm:0.4.13"
  dependencies:
    "@babel/compat-data": "npm:^7.22.6"
    "@babel/helper-define-polyfill-provider": "npm:^0.6.4"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10c0/b4a54561606d388e6f9499f39f03171af4be7f9ce2355e737135e40afa7086cf6790fdd706c2e59f488c8fa1f76123d28783708e07ddc84647dca8ed8fb98e06
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs3@npm:^0.11.0":
  version: 0.11.1
  resolution: "babel-plugin-polyfill-corejs3@npm:0.11.1"
  dependencies:
    "@babel/helper-define-polyfill-provider": "npm:^0.6.3"
    core-js-compat: "npm:^3.40.0"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10c0/025f754b6296d84b20200aff63a3c1acdd85e8c621781f2bd27fe2512d0060526192d02329326947c6b29c27cf475fbcfaaff8c51eab1d2bfc7b79086bb64229
  languageName: node
  linkType: hard

"babel-plugin-polyfill-regenerator@npm:^0.6.1":
  version: 0.6.4
  resolution: "babel-plugin-polyfill-regenerator@npm:0.6.4"
  dependencies:
    "@babel/helper-define-polyfill-provider": "npm:^0.6.4"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 10c0/ebaaf9e4e53201c02f496d3f686d815e94177b3e55b35f11223b99c60d197a29f907a2e87bbcccced8b7aff22a807fccc1adaf04722864a8e1862c8845ab830a
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 10c0/9308baf0a7e4838a82bbfd11e01b1cb0f0cf2893bc1676c27c2a8c0e70cbae1c59120c3268517a8ae7fb6376b4639ef81ca22582611dbee4ed28df945134aaee
  languageName: node
  linkType: hard

"base64-js@npm:^1.3.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 10c0/f23823513b63173a001030fae4f2dabe283b99a9d324ade3ad3d148e218134676f1ee8568c877cd79ec1c53158dcf2d2ba527a97c606618928ba99dd930102bf
  languageName: node
  linkType: hard

"base64url@npm:^3.0.1":
  version: 3.0.1
  resolution: "base64url@npm:3.0.1"
  checksum: 10c0/5ca9d6064e9440a2a45749558dddd2549ca439a305793d4f14a900b7256b5f4438ef1b7a494e1addc66ced5d20f5c010716d353ed267e4b769e6c78074991241
  languageName: node
  linkType: hard

"big.js@npm:^5.2.2":
  version: 5.2.2
  resolution: "big.js@npm:5.2.2"
  checksum: 10c0/230520f1ff920b2d2ce3e372d77a33faa4fa60d802fe01ca4ffbc321ee06023fe9a741ac02793ee778040a16b7e497f7d60c504d1c402b8fdab6f03bb785a25f
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.3.0
  resolution: "binary-extensions@npm:2.3.0"
  checksum: 10c0/75a59cafc10fb12a11d510e77110c6c7ae3f4ca22463d52487709ca7f18f69d886aa387557cc9864fbdb10153d0bdb4caacabf11541f55e89ed6e18d12ece2b5
  languageName: node
  linkType: hard

"bmp-js@npm:^0.1.0":
  version: 0.1.0
  resolution: "bmp-js@npm:0.1.0"
  checksum: 10c0/c651bd5936dcf8d67900050fac14dcbe30baf87c3d21c58f4934fcdf46172e152a87d8c0c3ca25caa2b4b2c7780ef3b5fcc6cd20afd8f0351856cadb1bef9694
  languageName: node
  linkType: hard

"body-parser@npm:1.20.3":
  version: 1.20.3
  resolution: "body-parser@npm:1.20.3"
  dependencies:
    bytes: "npm:3.1.2"
    content-type: "npm:~1.0.5"
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    on-finished: "npm:2.4.1"
    qs: "npm:6.13.0"
    raw-body: "npm:2.5.2"
    type-is: "npm:~1.6.18"
    unpipe: "npm:1.0.0"
  checksum: 10c0/0a9a93b7518f222885498dcecaad528cf010dd109b071bf471c93def4bfe30958b83e03496eb9c1ad4896db543d999bb62be1a3087294162a88cfa1b42c16310
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.2
  resolution: "brace-expansion@npm:2.0.2"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: 10c0/6d117a4c793488af86b83172deb6af143e94c17bc53b0b3cec259733923b4ca84679d506ac261f4ba3c7ed37c46018e2ff442f9ce453af8643ecd64f4a54e6cf
  languageName: node
  linkType: hard

"braces@npm:^3.0.3, braces@npm:~3.0.2":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: "npm:^7.1.1"
  checksum: 10c0/7c6dfd30c338d2997ba77500539227b9d1f85e388a5f43220865201e407e076783d0881f2d297b9f80951b4c957fcf0b51c1d2d24227631643c3f7c284b0aa04
  languageName: node
  linkType: hard

"browserslist-to-esbuild@npm:^2.1.1":
  version: 2.1.1
  resolution: "browserslist-to-esbuild@npm:2.1.1"
  dependencies:
    meow: "npm:^13.0.0"
  peerDependencies:
    browserslist: "*"
  bin:
    browserslist-to-esbuild: cli/index.js
  checksum: 10c0/4d1968efd72850949d5dfa355f4663c695a8fd7b259b19cc81ecd0ed33dd37bbd327475c644de3f2beb53e737c6e511c6e84ed3e97a2ff49f117505a7707c72d
  languageName: node
  linkType: hard

"browserslist@npm:^4.23.0, browserslist@npm:^4.24.0, browserslist@npm:^4.24.4, browserslist@npm:^4.25.0":
  version: 4.25.0
  resolution: "browserslist@npm:4.25.0"
  dependencies:
    caniuse-lite: "npm:^1.0.30001718"
    electron-to-chromium: "npm:^1.5.160"
    node-releases: "npm:^2.0.19"
    update-browserslist-db: "npm:^1.1.3"
  bin:
    browserslist: cli.js
  checksum: 10c0/cc16c55b4468b18684a0e1ca303592b38635b1155d6724f172407192737a2f405b8030d87a05813729592793445b3d15e737b0055f901cdecccb29b1e580a1c5
  languageName: node
  linkType: hard

"buffer-equal@npm:0.0.1":
  version: 0.0.1
  resolution: "buffer-equal@npm:0.0.1"
  checksum: 10c0/2fdcc84ac89032c1db8f393a550f6936f407796c15c9a2eac7b5d1cd1481621215e33d7c768a948e906ea6ae9cf054bbd5aa4492dd68d343f5a12fd48e1c1f67
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 10c0/124fff9d66d691a86d3b062eff4663fe437a9d9ee4b47b1b9e97f5a5d14f6d5399345db80f796827be7c95e70a8e765dd404b7c3ff3b3324f98e9b0c8826cc34
  languageName: node
  linkType: hard

"buffer@npm:^5.2.0":
  version: 5.7.1
  resolution: "buffer@npm:5.7.1"
  dependencies:
    base64-js: "npm:^1.3.1"
    ieee754: "npm:^1.1.13"
  checksum: 10c0/27cac81cff434ed2876058d72e7c4789d11ff1120ef32c9de48f59eab58179b66710c488987d295ae89a228f835fc66d088652dffeb8e3ba8659f80eb091d55e
  languageName: node
  linkType: hard

"bytes@npm:3.1.2":
  version: 3.1.2
  resolution: "bytes@npm:3.1.2"
  checksum: 10c0/76d1c43cbd602794ad8ad2ae94095cddeb1de78c5dddaa7005c51af10b0176c69971a6d88e805a90c2b6550d76636e43c40d8427a808b8645ede885de4a0358e
  languageName: node
  linkType: hard

"cac@npm:6.7.9":
  version: 6.7.9
  resolution: "cac@npm:6.7.9"
  checksum: 10c0/799092274421521ab6a153c30d84721fe34481c2b566e8881e2723d3ab4306e01690b06cd49c6161e28ea2913910dc8619f547ac7663959085355e3d033bbfed
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": "npm:^4.0.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^7.0.2"
    ssri: "npm:^12.0.0"
    tar: "npm:^7.4.3"
    unique-filename: "npm:^4.0.0"
  checksum: 10c0/01f2134e1bd7d3ab68be851df96c8d63b492b1853b67f2eecb2c37bb682d37cb70bb858a16f2f0554d3c0071be6dfe21456a1ff6fa4b7eed996570d6a25ffe9c
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.1, call-bind-apply-helpers@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind-apply-helpers@npm:1.0.2"
  dependencies:
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
  checksum: 10c0/47bd9901d57b857590431243fea704ff18078b16890a6b3e021e12d279bbf211d039155e27d7566b374d49ee1f8189344bac9833dec7a20cdec370506361c938
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2":
  version: 1.0.4
  resolution: "call-bound@npm:1.0.4"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    get-intrinsic: "npm:^1.3.0"
  checksum: 10c0/f4796a6a0941e71c766aea672f63b72bc61234c4f4964dc6d7606e3664c307e7d77845328a8f3359ce39ddb377fed67318f9ee203dea1d47e46165dcf2917644
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001702, caniuse-lite@npm:^1.0.30001718":
  version: 1.0.30001723
  resolution: "caniuse-lite@npm:1.0.30001723"
  checksum: 10c0/e019503061759b96017c4d27ddd7ca1b48533eabcd0431b51d2e3156f99f6b031075e46c279c0db63424cdfc874bba992caec2db51b922a0f945e686246886f6
  languageName: node
  linkType: hard

"centra@npm:^2.7.0":
  version: 2.7.0
  resolution: "centra@npm:2.7.0"
  dependencies:
    follow-redirects: "npm:^1.15.6"
  checksum: 10c0/ff0184fb3a8c72532550065dfee15f64ce619517e9e1323f2f2cf31333bd3b5f0b3fa1f10be51c26f43a83ed49b1d6c660d4c793a1e8b896ba1e288f766b7c35
  languageName: node
  linkType: hard

"chokidar@npm:^3.5.3":
  version: 3.6.0
  resolution: "chokidar@npm:3.6.0"
  dependencies:
    anymatch: "npm:~3.1.2"
    braces: "npm:~3.0.2"
    fsevents: "npm:~2.3.2"
    glob-parent: "npm:~5.1.2"
    is-binary-path: "npm:~2.1.0"
    is-glob: "npm:~4.0.1"
    normalize-path: "npm:~3.0.0"
    readdirp: "npm:~3.6.0"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 10c0/8361dcd013f2ddbe260eacb1f3cb2f2c6f2b0ad118708a343a5ed8158941a39cb8fb1d272e0f389712e74ee90ce8ba864eece9e0e62b9705cb468a2f6d917462
  languageName: node
  linkType: hard

"chokidar@npm:^4.0.0":
  version: 4.0.3
  resolution: "chokidar@npm:4.0.3"
  dependencies:
    readdirp: "npm:^4.0.1"
  checksum: 10c0/a58b9df05bb452f7d105d9e7229ac82fa873741c0c40ddcc7bb82f8a909fbe3f7814c9ebe9bc9a2bef9b737c0ec6e2d699d179048ef06ad3ec46315df0ebe6ad
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: 10c0/43925b87700f7e3893296c8e9c56cc58f926411cce3a6e5898136daaf08f08b9a8eb76d37d3267e707d0dcc17aed2e2ebdf5848c0c3ce95cf910a919935c1b10
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 10c0/37e1150172f2e311fe1b2df62c6293a342ee7380da7b9cfdba67ea539909afbd74da27033208d01d6d5cfc65ee7868a22e18d7e7648e004425441c0f8a15a7d7
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: 10c0/a1a3f914156960902f46f7f56bc62effc6c94e84b2cae157a526b1c1f74b677a47ec602bf68a61abfa2b42d15b7c5651c6dbe72a43af720bc588dff885b10f95
  languageName: node
  linkType: hard

"commander@npm:^2.20.0":
  version: 2.20.3
  resolution: "commander@npm:2.20.3"
  checksum: 10c0/74c781a5248c2402a0a3e966a0a2bba3c054aad144f5c023364be83265e796b20565aa9feff624132ff629aa64e16999fa40a743c10c12f7c61e96a794b99288
  languageName: node
  linkType: hard

"compare-versions@npm:^3.6.0":
  version: 3.6.0
  resolution: "compare-versions@npm:3.6.0"
  checksum: 10c0/11d4cad6f8da9e246d1d7b02912fdd38f33c7167257c1860defbe8a0ea846f774c1e17da081afb277c54549ba5cb2bef4e4350449ba2749f7b721f0203ba0cc7
  languageName: node
  linkType: hard

"computeds@npm:^0.0.1":
  version: 0.0.1
  resolution: "computeds@npm:0.0.1"
  checksum: 10c0/8a8736f1f43e4a99286519785d71a10ece8f444a2fa1fc2fe1f03dedf63f3477b45094002c85a2826f7631759c9f5a00b4ace47456997f253073fc525e8983de
  languageName: node
  linkType: hard

"confbox@npm:^0.1.8":
  version: 0.1.8
  resolution: "confbox@npm:0.1.8"
  checksum: 10c0/fc2c68d97cb54d885b10b63e45bd8da83a8a71459d3ecf1825143dd4c7f9f1b696b3283e07d9d12a144c1301c2ebc7842380bdf0014e55acc4ae1c9550102418
  languageName: node
  linkType: hard

"confbox@npm:^0.2.1":
  version: 0.2.2
  resolution: "confbox@npm:0.2.2"
  checksum: 10c0/7c246588d533d31e8cdf66cb4701dff6de60f9be77ab54c0d0338e7988750ac56863cc0aca1b3f2046f45ff223a765d3e5d4977a7674485afcd37b6edf3fd129
  languageName: node
  linkType: hard

"content-disposition@npm:0.5.4":
  version: 0.5.4
  resolution: "content-disposition@npm:0.5.4"
  dependencies:
    safe-buffer: "npm:5.2.1"
  checksum: 10c0/bac0316ebfeacb8f381b38285dc691c9939bf0a78b0b7c2d5758acadad242d04783cee5337ba7d12a565a19075af1b3c11c728e1e4946de73c6ff7ce45f3f1bb
  languageName: node
  linkType: hard

"content-type@npm:~1.0.4, content-type@npm:~1.0.5":
  version: 1.0.5
  resolution: "content-type@npm:1.0.5"
  checksum: 10c0/b76ebed15c000aee4678c3707e0860cb6abd4e680a598c0a26e17f0bfae723ec9cc2802f0ff1bc6e4d80603719010431d2231018373d4dde10f9ccff9dadf5af
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: 10c0/8f2f7a27a1a011cc6cc88cc4da2d7d0cfa5ee0369508baae3d98c260bb3ac520691464e5bbe4ae7cdf09860c1d69ecc6f70c63c6e7c7f7e3f18ec08484dc7d9b
  languageName: node
  linkType: hard

"cookie-signature@npm:1.0.6":
  version: 1.0.6
  resolution: "cookie-signature@npm:1.0.6"
  checksum: 10c0/b36fd0d4e3fef8456915fcf7742e58fbfcc12a17a018e0eb9501c9d5ef6893b596466f03b0564b81af29ff2538fd0aa4b9d54fe5ccbfb4c90ea50ad29fe2d221
  languageName: node
  linkType: hard

"cookie@npm:0.7.1":
  version: 0.7.1
  resolution: "cookie@npm:0.7.1"
  checksum: 10c0/5de60c67a410e7c8dc8a46a4b72eb0fe925871d057c9a5d2c0e8145c4270a4f81076de83410c4d397179744b478e33cd80ccbcc457abf40a9409ad27dcd21dde
  languageName: node
  linkType: hard

"core-js-compat@npm:^3.40.0":
  version: 3.43.0
  resolution: "core-js-compat@npm:3.43.0"
  dependencies:
    browserslist: "npm:^4.25.0"
  checksum: 10c0/923804c16faf91bacb747a697640a907cb2a3e63078d467a75eb7ea4187d62d36347a94e5826d1b36739012e81a2ea435922cc8bd8e228fa68efaf00a9ce94af
  languageName: node
  linkType: hard

"core-js@npm:^3.36.0, core-js@npm:^3.4.1":
  version: 3.43.0
  resolution: "core-js@npm:3.43.0"
  checksum: 10c0/9d4ad66296e60380777de51d019b5c3e6cce023b7999750a5094f9a4b0ea53bf3600beb4ef11c56548f2c8791d43d4056e270d1cf55ba87273011aa7d4597871
  languageName: node
  linkType: hard

"cross-env@npm:^7.0.3":
  version: 7.0.3
  resolution: "cross-env@npm:7.0.3"
  dependencies:
    cross-spawn: "npm:^7.0.1"
  bin:
    cross-env: src/bin/cross-env.js
    cross-env-shell: src/bin/cross-env-shell.js
  checksum: 10c0/f3765c25746c69fcca369655c442c6c886e54ccf3ab8c16847d5ad0e91e2f337d36eedc6599c1227904bf2a228d721e690324446876115bc8e7b32a866735ecf
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.1, cross-spawn@npm:^7.0.3, cross-spawn@npm:^7.0.6":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: 10c0/053ea8b2135caff68a9e81470e845613e374e7309a47731e81639de3eaeb90c3d01af0e0b44d2ab9d50b43467223b88567dfeb3262db942dc063b9976718ffc1
  languageName: node
  linkType: hard

"css-font-size-keywords@npm:^1.0.0":
  version: 1.0.0
  resolution: "css-font-size-keywords@npm:1.0.0"
  checksum: 10c0/afc3e296a20e533da5fe84e1436914b5ec789d2db851b856688a1aa80f88c2ee408580a8a0a1e0581cc0952b65bfb37a769c841df185c7b1d95198c563996465
  languageName: node
  linkType: hard

"css-font-stretch-keywords@npm:^1.0.1":
  version: 1.0.1
  resolution: "css-font-stretch-keywords@npm:1.0.1"
  checksum: 10c0/9f5c405c99962f2bf77ed79f9f137ac7eecbc2e0f64cfe448277af9924ec2717214369d0e9a02b4520e3e0812b6a5289fad36911678a13f5505e972889cb27a9
  languageName: node
  linkType: hard

"css-font-style-keywords@npm:^1.0.1":
  version: 1.0.1
  resolution: "css-font-style-keywords@npm:1.0.1"
  checksum: 10c0/431bb7ee0346302abc9c94a74df0c20b8f5173ef8b29284c571bbba4eec4e22a461dcb6a7d0ae764f9b02f9a7a185612193906122c744af96eb6e4be72e646d2
  languageName: node
  linkType: hard

"css-font-weight-keywords@npm:^1.0.0":
  version: 1.0.0
  resolution: "css-font-weight-keywords@npm:1.0.0"
  checksum: 10c0/99fbf6ca55ca86cbb083786f6ca83d47c0284eab1a96929b71401a50969e18a901474ffe8b1a895226f9bbde461c77d88ca50fcc871ce8a8926a2d2ba65bb847
  languageName: node
  linkType: hard

"css-list-helpers@npm:^2.0.0":
  version: 2.0.0
  resolution: "css-list-helpers@npm:2.0.0"
  checksum: 10c0/5a86b54ee8c78c190d2c5aafbf93b541ea36fe361d51bad69c6fc8666d0d71c31eb36dda7d0ddb4debf63581f5d6385c55522ebe1030c2fd4f47d5cccc480e35
  languageName: node
  linkType: hard

"css-system-font-keywords@npm:^1.0.0":
  version: 1.0.0
  resolution: "css-system-font-keywords@npm:1.0.0"
  checksum: 10c0/8eb3c1d70f70f4727d273cfdc37c53cb0e312286c070f9ec1cf24b7c2602cf8b128f2a127004f1f9feca45848442ad99c22875a440457a238fcd3b9307c8f0ce
  languageName: node
  linkType: hard

"cssesc@npm:^3.0.0":
  version: 3.0.0
  resolution: "cssesc@npm:3.0.0"
  bin:
    cssesc: bin/cssesc
  checksum: 10c0/6bcfd898662671be15ae7827120472c5667afb3d7429f1f917737f3bf84c4176003228131b643ae74543f17a394446247df090c597bb9a728cce298606ed0aa7
  languageName: node
  linkType: hard

"csstype@npm:^3.1.3":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: 10c0/80c089d6f7e0c5b2bd83cf0539ab41474198579584fa10d86d0cafe0642202343cbc119e076a0b1aece191989477081415d66c9fefbf3c957fc2fc4b7009f248
  languageName: node
  linkType: hard

"de-indent@npm:^1.0.2":
  version: 1.0.2
  resolution: "de-indent@npm:1.0.2"
  checksum: 10c0/7058ce58abd6dfc123dd204e36be3797abd419b59482a634605420f47ae97639d0c183ec5d1b904f308a01033f473673897afc2bd59bc620ebf1658763ef4291
  languageName: node
  linkType: hard

"debug@npm:2.6.9":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: "npm:2.0.0"
  checksum: 10c0/121908fb839f7801180b69a7e218a40b5a0b718813b886b7d6bdb82001b931c938e2941d1e4450f33a1b1df1da653f5f7a0440c197f29fbf8a6e9d45ff6ef589
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.1, debug@npm:^4.3.3, debug@npm:^4.3.4":
  version: 4.4.1
  resolution: "debug@npm:4.4.1"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/d2b44bc1afd912b49bb7ebb0d50a860dc93a4dd7d946e8de94abc957bb63726b7dd5aa48c18c2386c379ec024c46692e15ed3ed97d481729f929201e671fcd55
  languageName: node
  linkType: hard

"default-gateway@npm:^6.0.3":
  version: 6.0.3
  resolution: "default-gateway@npm:6.0.3"
  dependencies:
    execa: "npm:^5.0.0"
  checksum: 10c0/5184f9e6e105d24fb44ade9e8741efa54bb75e84625c1ea78c4ef8b81dff09ca52d6dbdd1185cf0dc655bb6b282a64fffaf7ed2dd561b8d9ad6f322b1f039aba
  languageName: node
  linkType: hard

"depd@npm:2.0.0":
  version: 2.0.0
  resolution: "depd@npm:2.0.0"
  checksum: 10c0/58bd06ec20e19529b06f7ad07ddab60e504d9e0faca4bd23079fac2d279c3594334d736508dc350e06e510aba5e22e4594483b3a6562ce7c17dd797f4cc4ad2c
  languageName: node
  linkType: hard

"destroy@npm:1.2.0":
  version: 1.2.0
  resolution: "destroy@npm:1.2.0"
  checksum: 10c0/bd7633942f57418f5a3b80d5cb53898127bcf53e24cdf5d5f4396be471417671f0fee48a4ebe9a1e9defbde2a31280011af58a57e090ff822f589b443ed4e643
  languageName: node
  linkType: hard

"detect-libc@npm:^1.0.3":
  version: 1.0.3
  resolution: "detect-libc@npm:1.0.3"
  bin:
    detect-libc: ./bin/detect-libc.js
  checksum: 10c0/4da0deae9f69e13bc37a0902d78bf7169480004b1fed3c19722d56cff578d16f0e11633b7fbf5fb6249181236c72e90024cbd68f0b9558ae06e281f47326d50d
  languageName: node
  linkType: hard

"dom-walk@npm:^0.1.0":
  version: 0.1.2
  resolution: "dom-walk@npm:0.1.2"
  checksum: 10c0/4d2ad9062a9423d890f8577aa202b597a6b85f9489bdde656b9443901b8b322b289655c3affefc58ec2e41931e0828dfee0a1d2db6829a607d76def5901fc5a9
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.2.0"
  checksum: 10c0/199f2a0c1c16593ca0a145dbf76a962f8033ce3129f01284d48c45ed4e14fea9bbacd7b3610b6cdc33486cef20385ac054948fefc6272fcce645c09468f93031
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 10c0/26f364ebcdb6395f95124fda411f63137a4bfb5d3a06453f7f23dfe52502905bd84e0488172e0f9ec295fdc45f05c23d5d91baf16bd26f0fe9acd777a188dc39
  languageName: node
  linkType: hard

"ee-first@npm:1.1.1":
  version: 1.1.1
  resolution: "ee-first@npm:1.1.1"
  checksum: 10c0/b5bb125ee93161bc16bfe6e56c6b04de5ad2aa44234d8f644813cc95d861a6910903132b05093706de2b706599367c4130eb6d170f6b46895686b95f87d017b7
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.160":
  version: 1.5.167
  resolution: "electron-to-chromium@npm:1.5.167"
  checksum: 10c0/eba07d2d8ae99e1e29f1af380d005c378f71608617ca904cbe4e2b5b72b102b46c5687bdbef855e2214876729655661b2c20248cce425d54c8d40f0785cb998a
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: 10c0/b6053ad39951c4cf338f9092d7bfba448cdfd46fe6a2a034700b149ac9ffbc137e361cbd3c442297f86bed2e5f7576c1b54cc0a6bf8ef5106cc62f496af35010
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 10c0/af014e759a72064cf66e6e694a7fc6b0ed3d8db680427b021a89727689671cefe9d04151b2cad51dbaf85d5ba790d061cd167f1cf32eb7b281f6368b3c181639
  languageName: node
  linkType: hard

"emojis-list@npm:^3.0.0":
  version: 3.0.0
  resolution: "emojis-list@npm:3.0.0"
  checksum: 10c0/7dc4394b7b910444910ad64b812392159a21e1a7ecc637c775a440227dcb4f80eff7fe61f4453a7d7603fa23d23d30cc93fe9e4b5ed985b88d6441cd4a35117b
  languageName: node
  linkType: hard

"encodeurl@npm:~1.0.2":
  version: 1.0.2
  resolution: "encodeurl@npm:1.0.2"
  checksum: 10c0/f6c2387379a9e7c1156c1c3d4f9cb7bb11cf16dd4c1682e1f6746512564b053df5781029b6061296832b59fb22f459dbe250386d217c2f6e203601abb2ee0bec
  languageName: node
  linkType: hard

"encodeurl@npm:~2.0.0":
  version: 2.0.0
  resolution: "encodeurl@npm:2.0.0"
  checksum: 10c0/5d317306acb13e6590e28e27924c754163946a2480de11865c991a3a7eed4315cd3fba378b543ca145829569eefe9b899f3d84bb09870f675ae60bc924b01ceb
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: 10c0/36d938712ff00fe1f4bac88b43bcffb5930c1efa57bbcdca9d67e1d9d6c57cfb1200fb01efe0f3109b2ce99b231f90779532814a81370a1bd3274a0f58585039
  languageName: node
  linkType: hard

"entities@npm:^4.5.0":
  version: 4.5.0
  resolution: "entities@npm:4.5.0"
  checksum: 10c0/5b039739f7621f5d1ad996715e53d964035f75ad3b9a4d38c6b3804bb226e282ffeae2443624d8fdd9c47d8e926ae9ac009c54671243f0c3294c26af7cc85250
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 10c0/285325677bf00e30845e330eec32894f5105529db97496ee3f598478e50f008c5352a41a30e5e72ec9de8a542b5a570b85699cd63bd2bc646dbcb9f311d83bc4
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 10c0/b642f7b4dd4a376e954947550a3065a9ece6733ab8e51ad80db727aaae0817c2e99b02a97a3d6cecc648a97848305e728289cf312d09af395403a90c9d4d8a66
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 10c0/3f54eb49c16c18707949ff25a1456728c883e81259f045003499efba399c08bad00deebf65cccde8c0e07908c1a225c9d472b7107e558f2a48e28d530e34527c
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: 10c0/0a61325670072f98d8ae3b914edab3559b6caa980f08054a3b872052640d91da01d38df55df797fcc916389d77fc92b8d5906cf028f4db46d7e3003abecbca85
  languageName: node
  linkType: hard

"es-module-lexer@npm:^1.2.1":
  version: 1.7.0
  resolution: "es-module-lexer@npm:1.7.0"
  checksum: 10c0/4c935affcbfeba7fb4533e1da10fa8568043df1e3574b869385980de9e2d475ddc36769891936dbb07036edb3c3786a8b78ccf44964cd130dedc1f2c984b6c7b
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0, es-object-atoms@npm:^1.1.1":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: "npm:^1.3.0"
  checksum: 10c0/65364812ca4daf48eb76e2a3b7a89b3f6a2e62a1c420766ce9f692665a29d94fe41fe88b65f24106f449859549711e4b40d9fb8002d862dfd7eb1c512d10be0c
  languageName: node
  linkType: hard

"esbuild@npm:^0.20.1":
  version: 0.20.2
  resolution: "esbuild@npm:0.20.2"
  dependencies:
    "@esbuild/aix-ppc64": "npm:0.20.2"
    "@esbuild/android-arm": "npm:0.20.2"
    "@esbuild/android-arm64": "npm:0.20.2"
    "@esbuild/android-x64": "npm:0.20.2"
    "@esbuild/darwin-arm64": "npm:0.20.2"
    "@esbuild/darwin-x64": "npm:0.20.2"
    "@esbuild/freebsd-arm64": "npm:0.20.2"
    "@esbuild/freebsd-x64": "npm:0.20.2"
    "@esbuild/linux-arm": "npm:0.20.2"
    "@esbuild/linux-arm64": "npm:0.20.2"
    "@esbuild/linux-ia32": "npm:0.20.2"
    "@esbuild/linux-loong64": "npm:0.20.2"
    "@esbuild/linux-mips64el": "npm:0.20.2"
    "@esbuild/linux-ppc64": "npm:0.20.2"
    "@esbuild/linux-riscv64": "npm:0.20.2"
    "@esbuild/linux-s390x": "npm:0.20.2"
    "@esbuild/linux-x64": "npm:0.20.2"
    "@esbuild/netbsd-x64": "npm:0.20.2"
    "@esbuild/openbsd-x64": "npm:0.20.2"
    "@esbuild/sunos-x64": "npm:0.20.2"
    "@esbuild/win32-arm64": "npm:0.20.2"
    "@esbuild/win32-ia32": "npm:0.20.2"
    "@esbuild/win32-x64": "npm:0.20.2"
  dependenciesMeta:
    "@esbuild/aix-ppc64":
      optional: true
    "@esbuild/android-arm":
      optional: true
    "@esbuild/android-arm64":
      optional: true
    "@esbuild/android-x64":
      optional: true
    "@esbuild/darwin-arm64":
      optional: true
    "@esbuild/darwin-x64":
      optional: true
    "@esbuild/freebsd-arm64":
      optional: true
    "@esbuild/freebsd-x64":
      optional: true
    "@esbuild/linux-arm":
      optional: true
    "@esbuild/linux-arm64":
      optional: true
    "@esbuild/linux-ia32":
      optional: true
    "@esbuild/linux-loong64":
      optional: true
    "@esbuild/linux-mips64el":
      optional: true
    "@esbuild/linux-ppc64":
      optional: true
    "@esbuild/linux-riscv64":
      optional: true
    "@esbuild/linux-s390x":
      optional: true
    "@esbuild/linux-x64":
      optional: true
    "@esbuild/netbsd-x64":
      optional: true
    "@esbuild/openbsd-x64":
      optional: true
    "@esbuild/sunos-x64":
      optional: true
    "@esbuild/win32-arm64":
      optional: true
    "@esbuild/win32-ia32":
      optional: true
    "@esbuild/win32-x64":
      optional: true
  bin:
    esbuild: bin/esbuild
  checksum: 10c0/66398f9fb2c65e456a3e649747b39af8a001e47963b25e86d9c09d2a48d61aa641b27da0ce5cad63df95ad246105e1d83e7fee0e1e22a0663def73b1c5101112
  languageName: node
  linkType: hard

"escalade@npm:^3.2.0":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 10c0/ced4dd3a78e15897ed3be74e635110bbf3b08877b0a41be50dcb325ee0e0b5f65fc2d50e9845194d7c4633f327e2e1c6cce00a71b617c5673df0374201d67f65
  languageName: node
  linkType: hard

"escape-html@npm:~1.0.3":
  version: 1.0.3
  resolution: "escape-html@npm:1.0.3"
  checksum: 10c0/524c739d776b36c3d29fa08a22e03e8824e3b2fd57500e5e44ecf3cc4707c34c60f9ca0781c0e33d191f2991161504c295e98f68c78fe7baa6e57081ec6ac0a3
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^5.0.0":
  version: 5.0.0
  resolution: "escape-string-regexp@npm:5.0.0"
  checksum: 10c0/6366f474c6f37a802800a435232395e04e9885919873e382b157ab7e8f0feb8fed71497f84a6f6a81a49aab41815522f5839112bd38026d203aea0c91622df95
  languageName: node
  linkType: hard

"estree-walker@npm:^2.0.2":
  version: 2.0.2
  resolution: "estree-walker@npm:2.0.2"
  checksum: 10c0/53a6c54e2019b8c914dc395890153ffdc2322781acf4bd7d1a32d7aedc1710807bdcd866ac133903d5629ec601fbb50abe8c2e5553c7f5a0afdd9b6af6c945af
  languageName: node
  linkType: hard

"estree-walker@npm:^3.0.3":
  version: 3.0.3
  resolution: "estree-walker@npm:3.0.3"
  dependencies:
    "@types/estree": "npm:^1.0.0"
  checksum: 10c0/c12e3c2b2642d2bcae7d5aa495c60fa2f299160946535763969a1c83fc74518ffa9c2cd3a8b69ac56aea547df6a8aac25f729a342992ef0bbac5f1c73e78995d
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 10c0/9a2fe69a41bfdade834ba7c42de4723c97ec776e40656919c62cbd13607c45e127a003f05f724a1ea55e5029a4cf2de444b13009f2af71271e42d93a637137c7
  languageName: node
  linkType: hard

"etag@npm:~1.8.1":
  version: 1.8.1
  resolution: "etag@npm:1.8.1"
  checksum: 10c0/12be11ef62fb9817314d790089a0a49fae4e1b50594135dcb8076312b7d7e470884b5100d249b28c18581b7fd52f8b485689ffae22a11ed9ec17377a33a08f84
  languageName: node
  linkType: hard

"execa@npm:^5.0.0":
  version: 5.1.1
  resolution: "execa@npm:5.1.1"
  dependencies:
    cross-spawn: "npm:^7.0.3"
    get-stream: "npm:^6.0.0"
    human-signals: "npm:^2.1.0"
    is-stream: "npm:^2.0.0"
    merge-stream: "npm:^2.0.0"
    npm-run-path: "npm:^4.0.1"
    onetime: "npm:^5.1.2"
    signal-exit: "npm:^3.0.3"
    strip-final-newline: "npm:^2.0.0"
  checksum: 10c0/c8e615235e8de4c5addf2fa4c3da3e3aa59ce975a3e83533b4f6a71750fb816a2e79610dc5f1799b6e28976c9ae86747a36a606655bf8cb414a74d8d507b304f
  languageName: node
  linkType: hard

"exif-parser@npm:^0.1.12":
  version: 0.1.12
  resolution: "exif-parser@npm:0.1.12"
  checksum: 10c0/ef1df84edbba50621fcfe19510c8db3ddd9e7fb374459d3f77c9256c24584767c7fb4cf1b15aef46e87a06528d3c48e44de02cecc314656d22a5cf954a0e7192
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.2
  resolution: "exponential-backoff@npm:3.1.2"
  checksum: 10c0/d9d3e1eafa21b78464297df91f1776f7fbaa3d5e3f7f0995648ca5b89c069d17055033817348d9f4a43d1c20b0eab84f75af6991751e839df53e4dfd6f22e844
  languageName: node
  linkType: hard

"express@npm:^4.17.1":
  version: 4.21.2
  resolution: "express@npm:4.21.2"
  dependencies:
    accepts: "npm:~1.3.8"
    array-flatten: "npm:1.1.1"
    body-parser: "npm:1.20.3"
    content-disposition: "npm:0.5.4"
    content-type: "npm:~1.0.4"
    cookie: "npm:0.7.1"
    cookie-signature: "npm:1.0.6"
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    finalhandler: "npm:1.3.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    merge-descriptors: "npm:1.0.3"
    methods: "npm:~1.1.2"
    on-finished: "npm:2.4.1"
    parseurl: "npm:~1.3.3"
    path-to-regexp: "npm:0.1.12"
    proxy-addr: "npm:~2.0.7"
    qs: "npm:6.13.0"
    range-parser: "npm:~1.2.1"
    safe-buffer: "npm:5.2.1"
    send: "npm:0.19.0"
    serve-static: "npm:1.16.2"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    type-is: "npm:~1.6.18"
    utils-merge: "npm:1.0.1"
    vary: "npm:~1.1.2"
  checksum: 10c0/38168fd0a32756600b56e6214afecf4fc79ec28eca7f7a91c2ab8d50df4f47562ca3f9dee412da7f5cea6b1a1544b33b40f9f8586dbacfbdada0fe90dbb10a1f
  languageName: node
  linkType: hard

"exsolve@npm:^1.0.1":
  version: 1.0.5
  resolution: "exsolve@npm:1.0.5"
  checksum: 10c0/0e845843951e8e7f190d26648259b3d584990933ea68a3c8ec984e826d4fb3731681f7f2569252b4fe619db1d67b0859abe0ef694cb2edb454343bd44bcdce59
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: 10c0/40dedc862eb8992c54579c66d914635afbec43350afbbe991235fdcb4e3a8d5af1b23ae7e79bef7d4882d0ecee06c3197488026998fb19f72dc95acff1d1b1d0
  languageName: node
  linkType: hard

"fast-glob@npm:^3.2.11, fast-glob@npm:^3.3.2, fast-glob@npm:^3.3.3":
  version: 3.3.3
  resolution: "fast-glob@npm:3.3.3"
  dependencies:
    "@nodelib/fs.stat": "npm:^2.0.2"
    "@nodelib/fs.walk": "npm:^1.2.3"
    glob-parent: "npm:^5.1.2"
    merge2: "npm:^1.3.0"
    micromatch: "npm:^4.0.8"
  checksum: 10c0/f6aaa141d0d3384cf73cbcdfc52f475ed293f6d5b65bfc5def368b09163a9f7e5ec2b3014d80f733c405f58e470ee0cc451c2937685045cddcdeaa24199c43fe
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: 10c0/7f081eb0b8a64e0057b3bb03f974b3ef00135fbf36c1c710895cd9300f13c94ba809bb3a81cf4e1b03f6e5285610a61abbd7602d0652de423144dfee5a389c9b
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.19.1
  resolution: "fastq@npm:1.19.1"
  dependencies:
    reusify: "npm:^1.0.4"
  checksum: 10c0/ebc6e50ac7048daaeb8e64522a1ea7a26e92b3cee5cd1c7f2316cdca81ba543aa40a136b53891446ea5c3a67ec215fbaca87ad405f102dd97012f62916905630
  languageName: node
  linkType: hard

"fdir@npm:^6.4.4":
  version: 6.4.6
  resolution: "fdir@npm:6.4.6"
  peerDependencies:
    picomatch: ^3 || ^4
  peerDependenciesMeta:
    picomatch:
      optional: true
  checksum: 10c0/45b559cff889934ebb8bc498351e5acba40750ada7e7d6bde197768d2fa67c149be8ae7f8ff34d03f4e1eb20f2764116e56440aaa2f6689e9a4aa7ef06acafe9
  languageName: node
  linkType: hard

"file-type@npm:^9.0.0":
  version: 9.0.0
  resolution: "file-type@npm:9.0.0"
  checksum: 10c0/68d77137b2abddd379b53893217cde6015719313c9db1c922ca9ea18d7fd13d60b40f43f26a2f69fe96469f1b51c992ec727aafac573bdefe207f6bd5152f3a4
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: "npm:^5.0.1"
  checksum: 10c0/b75b691bbe065472f38824f694c2f7449d7f5004aa950426a2c28f0306c60db9b880c0b0e4ed819997ffb882d1da02cfcfc819bddc94d71627f5269682edf018
  languageName: node
  linkType: hard

"finalhandler@npm:1.3.1":
  version: 1.3.1
  resolution: "finalhandler@npm:1.3.1"
  dependencies:
    debug: "npm:2.6.9"
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    on-finished: "npm:2.4.1"
    parseurl: "npm:~1.3.3"
    statuses: "npm:2.0.1"
    unpipe: "npm:~1.0.0"
  checksum: 10c0/d38035831865a49b5610206a3a9a9aae4e8523cbbcd01175d0480ffbf1278c47f11d89be3ca7f617ae6d94f29cf797546a4619cd84dd109009ef33f12f69019f
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.15.6":
  version: 1.15.9
  resolution: "follow-redirects@npm:1.15.9"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: 10c0/5829165bd112c3c0e82be6c15b1a58fa9dcfaede3b3c54697a82fe4a62dd5ae5e8222956b448d2f98e331525f05d00404aba7d696de9e761ef6e42fdc780244f
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.1
  resolution: "foreground-child@npm:3.3.1"
  dependencies:
    cross-spawn: "npm:^7.0.6"
    signal-exit: "npm:^4.0.1"
  checksum: 10c0/8986e4af2430896e65bc2788d6679067294d6aee9545daefc84923a0a4b399ad9c7a3ea7bd8c0b2b80fdf4a92de4c69df3f628233ff3224260e9c1541a9e9ed3
  languageName: node
  linkType: hard

"forwarded@npm:0.2.0":
  version: 0.2.0
  resolution: "forwarded@npm:0.2.0"
  checksum: 10c0/9b67c3fac86acdbc9ae47ba1ddd5f2f81526fa4c8226863ede5600a3f7c7416ef451f6f1e240a3cc32d0fd79fcfe6beb08fd0da454f360032bde70bf80afbb33
  languageName: node
  linkType: hard

"fraction.js@npm:^4.3.7":
  version: 4.3.7
  resolution: "fraction.js@npm:4.3.7"
  checksum: 10c0/df291391beea9ab4c263487ffd9d17fed162dbb736982dee1379b2a8cc94e4e24e46ed508c6d278aded9080ba51872f1bc5f3a5fd8d7c74e5f105b508ac28711
  languageName: node
  linkType: hard

"fresh@npm:0.5.2":
  version: 0.5.2
  resolution: "fresh@npm:0.5.2"
  checksum: 10c0/c6d27f3ed86cc5b601404822f31c900dd165ba63fff8152a3ef714e2012e7535027063bc67ded4cb5b3a49fa596495d46cacd9f47d6328459cf570f08b7d9e5a
  languageName: node
  linkType: hard

"fs-extra@npm:^10.0.0":
  version: 10.1.0
  resolution: "fs-extra@npm:10.1.0"
  dependencies:
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^6.0.1"
    universalify: "npm:^2.0.0"
  checksum: 10c0/5f579466e7109719d162a9249abbeffe7f426eb133ea486e020b89bc6d67a741134076bf439983f2eb79276ceaf6bd7b7c1e43c3fd67fe889863e69072fb0a5e
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/63e80da2ff9b621e2cb1596abcb9207f1cf82b968b116ccd7b959e3323144cce7fb141462200971c38bbf2ecca51695069db45265705bed09a7cd93ae5b89f94
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2, fsevents@npm:~2.3.3":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10c0/a1f0c44595123ed717febbc478aa952e47adfc28e2092be66b8ab1635147254ca6cfe1df792a8997f22716d4cbafc73309899ff7bfac2ac3ad8cf2e4ecc3ec60
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A~2.3.2#optional!builtin<compat/fsevents>, fsevents@patch:fsevents@npm%3A~2.3.3#optional!builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 10c0/d8680ee1e5fcd4c197e4ac33b2b4dce03c71f4d91717292785703db200f5c21f977c568d28061226f9b5900cbcd2c84463646134fd5337e7925e0942bc3f46d5
  languageName: node
  linkType: hard

"generic-names@npm:^4.0.0":
  version: 4.0.0
  resolution: "generic-names@npm:4.0.0"
  dependencies:
    loader-utils: "npm:^3.2.0"
  checksum: 10c0/4e2be864535fadceed4e803fefc1df7f85447d9479d51e611a8a43a2c96533422b62c8fae84d9eb10cc21ee3de569a8c29d5ba68978ae930cccc9cb43b9a36d1
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: 10c0/782aba6cba65b1bb5af3b095d96249d20edbe8df32dbf4696fd49be2583faf676173bf4809386588828e4dd76a3354fcbeb577bab1c833ccd9fc4577f26103f8
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.5, get-intrinsic@npm:^1.3.0":
  version: 1.3.0
  resolution: "get-intrinsic@npm:1.3.0"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    function-bind: "npm:^1.1.2"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    math-intrinsics: "npm:^1.1.0"
  checksum: 10c0/52c81808af9a8130f581e6a6a83e1ba4a9f703359e7a438d1369a5267a25412322f03dcbd7c549edaef0b6214a0630a28511d7df0130c93cfd380f4fa0b5b66a
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/9224acb44603c5526955e83510b9da41baf6ae73f7398875fba50edc5e944223a89c4a72b070fcd78beb5f7bdda58ecb6294adc28f7acfc0da05f76a2399643c
  languageName: node
  linkType: hard

"get-stream@npm:^6.0.0":
  version: 6.0.1
  resolution: "get-stream@npm:6.0.1"
  checksum: 10c0/49825d57d3fd6964228e6200a58169464b8e8970489b3acdc24906c782fb7f01f9f56f8e6653c4a50713771d6658f7cfe051e5eb8c12e334138c9c918b296341
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: "npm:^4.0.1"
  checksum: 10c0/cab87638e2112bee3f839ef5f6e0765057163d39c66be8ec1602f3823da4692297ad4e972de876ea17c44d652978638d2fd583c6713d0eb6591706825020c9ee
  languageName: node
  linkType: hard

"glob@npm:^10.2.2":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^3.1.2"
    minimatch: "npm:^9.0.4"
    minipass: "npm:^7.1.2"
    package-json-from-dist: "npm:^1.0.0"
    path-scurry: "npm:^1.11.1"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 10c0/19a9759ea77b8e3ca0a43c2f07ecddc2ad46216b786bb8f993c445aee80d345925a21e5280c7b7c6c59e860a0154b84e4b2b60321fea92cd3c56b4a7489f160e
  languageName: node
  linkType: hard

"global@npm:~4.4.0":
  version: 4.4.0
  resolution: "global@npm:4.4.0"
  dependencies:
    min-document: "npm:^2.19.0"
    process: "npm:^0.11.10"
  checksum: 10c0/4a467aec6602c00a7c5685f310574ab04e289ad7f894f0f01c9c5763562b82f4b92d1e381ce6c5bbb12173e2a9f759c1b63dda6370cfb199970267e14d90aa91
  languageName: node
  linkType: hard

"globals@npm:^11.1.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 10c0/758f9f258e7b19226bd8d4af5d3b0dcf7038780fb23d82e6f98932c44e239f884847f1766e8fa9cc5635ccb3204f7fa7314d4408dd4002a5e8ea827b4018f0a1
  languageName: node
  linkType: hard

"gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: 10c0/50fff1e04ba2b7737c097358534eacadad1e68d24cccee3272e04e007bed008e68d2614f3987788428fd192a5ae3889d08fb2331417e4fc4a9ab366b2043cead
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 10c0/386d011a553e02bc594ac2ca0bd6d9e4c22d7fa8cfbfc448a6d148c59ea881b092db9dbe3547ae4b88e55f1b01f7c4a2ecc53b310c042793e63aa44cf6c257f2
  languageName: node
  linkType: hard

"has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: 10c0/dde0a734b17ae51e84b10986e651c664379018d10b91b6b0e9b293eddb32f0f069688c841fb40f19e9611546130153e0a2a48fd7f512891fb000ddfa36f5a20e
  languageName: node
  linkType: hard

"hash-sum@npm:^2.0.0":
  version: 2.0.0
  resolution: "hash-sum@npm:2.0.0"
  checksum: 10c0/45dee9cf318d7a9b0ba5f766d35bfa14eb9483f9b878b1f980f097a87c2a490219774d42962c0c5c9bf53b1cca51724307bc35a0781218236da3d33715b4962d
  languageName: node
  linkType: hard

"hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: "npm:^1.1.2"
  checksum: 10c0/3769d434703b8ac66b209a4cca0737519925bbdb61dd887f93a16372b14694c63ff4e797686d87c90f08168e81082248b9b028bad60d4da9e0d1148766f56eb9
  languageName: node
  linkType: hard

"he@npm:^1.2.0":
  version: 1.2.0
  resolution: "he@npm:1.2.0"
  bin:
    he: bin/he
  checksum: 10c0/a27d478befe3c8192f006cdd0639a66798979dfa6e2125c6ac582a19a5ebfec62ad83e8382e6036170d873f46e4536a7e795bf8b95bf7c247f4cc0825ccc8c17
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.2.0
  resolution: "http-cache-semantics@npm:4.2.0"
  checksum: 10c0/45b66a945cf13ec2d1f29432277201313babf4a01d9e52f44b31ca923434083afeca03f18417f599c9ab3d0e7b618ceb21257542338b57c54b710463b4a53e37
  languageName: node
  linkType: hard

"http-errors@npm:2.0.0":
  version: 2.0.0
  resolution: "http-errors@npm:2.0.0"
  dependencies:
    depd: "npm:2.0.0"
    inherits: "npm:2.0.4"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    toidentifier: "npm:1.0.1"
  checksum: 10c0/fc6f2715fe188d091274b5ffc8b3657bd85c63e969daa68ccb77afb05b071a4b62841acb7a21e417b5539014dff2ebf9550f0b14a9ff126f2734a7c1387f8e19
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: "npm:^7.1.0"
    debug: "npm:^4.3.4"
  checksum: 10c0/4207b06a4580fb85dd6dff521f0abf6db517489e70863dca1a0291daa7f2d3d2d6015a57bd702af068ea5cf9f1f6ff72314f5f5b4228d299c0904135d2aef921
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:4"
  checksum: 10c0/f729219bc735edb621fa30e6e84e60ee5d00802b8247aac0d7b79b0bd6d4b3294737a337b93b86a0bd9e68099d031858a39260c976dc14cdbba238ba1f8779ac
  languageName: node
  linkType: hard

"human-signals@npm:^2.1.0":
  version: 2.1.0
  resolution: "human-signals@npm:2.1.0"
  checksum: 10c0/695edb3edfcfe9c8b52a76926cd31b36978782062c0ed9b1192b36bebc75c4c87c82e178dfcb0ed0fc27ca59d434198aac0bd0be18f5781ded775604db22304a
  languageName: node
  linkType: hard

"iconv-lite@npm:0.4.24":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3"
  checksum: 10c0/c6886a24cc00f2a059767440ec1bc00d334a89f250db8e0f7feb4961c8727118457e27c495ba94d082e51d3baca378726cd110aaf7ded8b9bbfd6a44760cf1d4
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 10c0/98102bc66b33fcf5ac044099d1257ba0b7ad5e3ccd3221f34dd508ab4070edff183276221684e1e0555b145fce0850c9f7d2b60a9fcac50fbb4ea0d6e845a3b1
  languageName: node
  linkType: hard

"icss-replace-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "icss-replace-symbols@npm:1.1.0"
  checksum: 10c0/aaa5b67f82781fccc77bf6df14eaa9177ce3944462ef82b2b9e3b9f17d8fcd90f8851ffd5e6e249ebc5c464bfda07c2eccce2d122274c51c9d5b359b087f7049
  languageName: node
  linkType: hard

"icss-utils@npm:^5.0.0":
  version: 5.1.0
  resolution: "icss-utils@npm:5.1.0"
  peerDependencies:
    postcss: ^8.1.0
  checksum: 10c0/39c92936fabd23169c8611d2b5cc39e39d10b19b0d223352f20a7579f75b39d5f786114a6b8fc62bee8c5fed59ba9e0d38f7219a4db383e324fb3061664b043d
  languageName: node
  linkType: hard

"ieee754@npm:^1.1.13":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 10c0/b0782ef5e0935b9f12883a2e2aa37baa75da6e66ce6515c168697b42160807d9330de9a32ec1ed73149aea02e0d822e572bca6f1e22bdcbd2149e13b050b17bb
  languageName: node
  linkType: hard

"immutable@npm:^5.0.2":
  version: 5.1.3
  resolution: "immutable@npm:5.1.3"
  checksum: 10c0/f094891dcefb9488a84598376c9218ebff3a130c8b807bda3f6b703c45fe7ef238b8bf9a1eb9961db0523c8d7eb116ab6f47166702e4bbb1927ff5884157cd97
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 10c0/8b51313850dd33605c6c9d3fd9638b714f4c4c40250cff658209f30d40da60f78992fb2df5dabee4acf589a6a82bbc79ad5486550754bd9ec4e3fc0d4a57d6a6
  languageName: node
  linkType: hard

"inherits@npm:2.0.4":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 10c0/4e531f648b29039fb7426fb94075e6545faa1eb9fe83c29f0b6d9e7263aceb4289d2d4557db0d428188eeb449cc7c5e77b0a0b2c4e248ff2a65933a0dee49ef2
  languageName: node
  linkType: hard

"invert-kv@npm:^3.0.0":
  version: 3.0.1
  resolution: "invert-kv@npm:3.0.1"
  checksum: 10c0/a3d90951a635e35dea9c9a5fd749e981e9c54e8a362ad80b2253dad03b9257314b7c4e4d250d61bcd79698ccd5f4c6b0c750cd991bb5ce16352bf830e77ea64b
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: "npm:1.1.0"
    sprintf-js: "npm:^1.1.3"
  checksum: 10c0/331cd07fafcb3b24100613e4b53e1a2b4feab11e671e655d46dc09ee233da5011284d09ca40c4ecbdfe1d0004f462958675c224a804259f2f78d2465a87824bc
  languageName: node
  linkType: hard

"ipaddr.js@npm:1.9.1":
  version: 1.9.1
  resolution: "ipaddr.js@npm:1.9.1"
  checksum: 10c0/0486e775047971d3fdb5fb4f063829bac45af299ae0b82dcf3afa2145338e08290563a2a70f34b732d795ecc8311902e541a8530eeb30d75860a78ff4e94ce2a
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: "npm:^2.0.0"
  checksum: 10c0/a16eaee59ae2b315ba36fad5c5dcaf8e49c3e27318f8ab8fa3cdb8772bf559c8d1ba750a589c2ccb096113bb64497084361a25960899cb6172a6925ab6123d38
  languageName: node
  linkType: hard

"is-core-module@npm:^2.16.0":
  version: 2.16.1
  resolution: "is-core-module@npm:2.16.1"
  dependencies:
    hasown: "npm:^2.0.2"
  checksum: 10c0/898443c14780a577e807618aaae2b6f745c8538eca5c7bc11388a3f2dc6de82b9902bcc7eb74f07be672b11bbe82dd6a6edded44a00cb3d8f933d0459905eedd
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: 10c0/5487da35691fbc339700bbb2730430b07777a3c21b9ebaecb3072512dfd7b4ba78ac2381a87e8d78d20ea08affb3f1971b4af629173a6bf435ff8a4c47747912
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 10c0/bb11d825e049f38e04c06373a8d72782eee0205bda9d908cc550ccb3c59b99d750ff9537982e01733c1c94a58e35400661f57042158ff5e8f3e90cf936daf0fc
  languageName: node
  linkType: hard

"is-function@npm:^1.0.1":
  version: 1.0.2
  resolution: "is-function@npm:1.0.2"
  checksum: 10c0/c55289042a0e828a773f1245e2652e0c029efacc78ebe03e61787746fda74e2c41006cd908f20b53c36e45f9e75464475a4b2d68b17f4c7b9f8018bcaec42f9e
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.1, is-glob@npm:^4.0.3, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: "npm:^2.1.1"
  checksum: 10c0/17fb4014e22be3bbecea9b2e3a76e9e34ff645466be702f1693e8f1ee1adac84710d0be0bd9f967d6354036fd51ab7c2741d954d6e91dae6bb69714de92c197a
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 10c0/b4686d0d3053146095ccd45346461bc8e53b80aeb7671cc52a4de02dbbf7dc0d1d2a986e2fe4ae206984b4d34ef37e8b795ebc4f4295c978373e6575e295d811
  languageName: node
  linkType: hard

"is-stream@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-stream@npm:2.0.1"
  checksum: 10c0/7c284241313fc6efc329b8d7f08e16c0efeb6baab1b4cd0ba579eb78e5af1aa5da11e68559896a2067cd6c526bd29241dda4eb1225e627d5aa1a89a76d4635a5
  languageName: node
  linkType: hard

"isbinaryfile@npm:^5.0.2":
  version: 5.0.4
  resolution: "isbinaryfile@npm:5.0.4"
  checksum: 10c0/fea255bfae67ff4827e8dd2238d6700d4803d02b4d892b72eeac4541487284e901251a3427966af5018d4eb29fa155b036dcb75dd217634146a072991afbc2c2
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 10c0/228cfa503fadc2c31596ab06ed6aa82c9976eec2bfd83397e7eaf06d0ccf42cd1dfd6743bf9aeb01aebd4156d009994c5f76ea898d2832c1fe342da923ca457d
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 10c0/9ec257654093443eb0a528a9c8cbba9c0ca7616ccb40abd6dde7202734d96bb86e4ac0d764f0f8cd965856aacbff2f4ce23e730dc19dfb41e3b0d865ca6fdcc7
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 10c0/6acc10d139eaefdbe04d2f679e6191b3abf073f111edf10b1de5302c97ec93fffeb2fdd8681ed17f16268aa9dd4f8c588ed9d1d3bffbbfa6e8bf897cbb3149b9
  languageName: node
  linkType: hard

"jimp@npm:^0.10.1":
  version: 0.10.3
  resolution: "jimp@npm:0.10.3"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    "@jimp/custom": "npm:^0.10.3"
    "@jimp/plugins": "npm:^0.10.3"
    "@jimp/types": "npm:^0.10.3"
    core-js: "npm:^3.4.1"
    regenerator-runtime: "npm:^0.13.3"
  checksum: 10c0/c84f329d1e74fabaf2ccbddd98802dc9dc619e9eb0f702bc46a78b30659e3b570e6a4d7e135850b1be087f02a6b156f18a0bcc93d5926f58647ed6ad42a17e3d
  languageName: node
  linkType: hard

"jpeg-js@npm:^0.3.4":
  version: 0.3.7
  resolution: "jpeg-js@npm:0.3.7"
  checksum: 10c0/7c7d051096fe9bb60301b4fd29b8941aad492770cc5968719bb120993146a0f280edf35f64803ad504095d187e9a45109cbe9424229f432fdb835b51b889fb49
  languageName: node
  linkType: hard

"js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 10c0/e248708d377aa058eacf2037b07ded847790e6de892bbad3dac0abba2e759cb9f121b00099a65195616badcb6eca8d14d975cb3e89eb1cfda644756402c8aeed
  languageName: node
  linkType: hard

"js-tokens@npm:^9.0.1":
  version: 9.0.1
  resolution: "js-tokens@npm:9.0.1"
  checksum: 10c0/68dcab8f233dde211a6b5fd98079783cbcd04b53617c1250e3553ee16ab3e6134f5e65478e41d82f6d351a052a63d71024553933808570f04dbf828d7921e80e
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 10c0/4f907fb78d7b712e11dea8c165fe0921f81a657d3443dde75359ed52eb2b5d33ce6773d97985a089f09a65edd80b11cb75c767b57ba47391fee4c969f7215c96
  languageName: node
  linkType: hard

"jsesc@npm:^3.0.2":
  version: 3.1.0
  resolution: "jsesc@npm:3.1.0"
  bin:
    jsesc: bin/jsesc
  checksum: 10c0/531779df5ec94f47e462da26b4cbf05eb88a83d9f08aac2ba04206508fc598527a153d08bd462bae82fc78b3eaa1a908e1a4a79f886e9238641c4cdefaf118b1
  languageName: node
  linkType: hard

"jsesc@npm:~3.0.2":
  version: 3.0.2
  resolution: "jsesc@npm:3.0.2"
  bin:
    jsesc: bin/jsesc
  checksum: 10c0/ef22148f9e793180b14d8a145ee6f9f60f301abf443288117b4b6c53d0ecd58354898dc506ccbb553a5f7827965cd38bc5fb726575aae93c5e8915e2de8290e1
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 10c0/108fa90d4cc6f08243aedc6da16c408daf81793bf903e9fd5ab21983cda433d5d2da49e40711da016289465ec2e62e0324dcdfbc06275a607fe3233fde4942ce
  languageName: node
  linkType: hard

"json5@npm:^2.1.2, json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 10c0/5a04eed94810fa55c5ea138b2f7a5c12b97c3750bc63d11e511dcecbfef758003861522a070c2272764ee0f4e3e323862f386945aeb5b85b87ee43f084ba586c
  languageName: node
  linkType: hard

"jsonc-parser@npm:^3.2.0":
  version: 3.3.1
  resolution: "jsonc-parser@npm:3.3.1"
  checksum: 10c0/269c3ae0a0e4f907a914bf334306c384aabb9929bd8c99f909275ebd5c2d3bc70b9bcd119ad794f339dec9f24b6a4ee9cd5a8ab2e6435e730ad4075388fc2ab6
  languageName: node
  linkType: hard

"jsonfile@npm:^6.0.1":
  version: 6.1.0
  resolution: "jsonfile@npm:6.1.0"
  dependencies:
    graceful-fs: "npm:^4.1.6"
    universalify: "npm:^2.0.0"
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 10c0/4f95b5e8a5622b1e9e8f33c96b7ef3158122f595998114d1e7f03985649ea99cb3cd99ce1ed1831ae94c8c8543ab45ebd044207612f31a56fd08462140e46865
  languageName: node
  linkType: hard

"klona@npm:^2.0.4":
  version: 2.0.6
  resolution: "klona@npm:2.0.6"
  checksum: 10c0/94eed2c6c2ce99f409df9186a96340558897b3e62a85afdc1ee39103954d2ebe1c1c4e9fe2b0952771771fa96d70055ede8b27962a7021406374fdb695fd4d01
  languageName: node
  linkType: hard

"lcid@npm:^3.0.0":
  version: 3.1.1
  resolution: "lcid@npm:3.1.1"
  dependencies:
    invert-kv: "npm:^3.0.0"
  checksum: 10c0/43a39c39d92d756b9671691bb36ac2667c44c4a7e30f55403dc9c98ca4e7bba8c2b35599e8d7967163d65c1697e0d136596e9a9b9bccbd2292caf915c77416a4
  languageName: node
  linkType: hard

"licia@npm:^1.29.0":
  version: 1.48.0
  resolution: "licia@npm:1.48.0"
  checksum: 10c0/a18ba810963d7240d80019125c1f9758d45e021e420aa08ad497b4746337e305ea646e4a08f20c452fa36db21a50852bdbe488650cd912a1b0ea8d8d4705742a
  languageName: node
  linkType: hard

"lilconfig@npm:^2.0.5":
  version: 2.1.0
  resolution: "lilconfig@npm:2.1.0"
  checksum: 10c0/64645641aa8d274c99338e130554abd6a0190533c0d9eb2ce7ebfaf2e05c7d9961f3ffe2bfa39efd3b60c521ba3dd24fa236fe2775fc38501bf82bf49d4678b8
  languageName: node
  linkType: hard

"lines-and-columns@npm:^2.0.4":
  version: 2.0.4
  resolution: "lines-and-columns@npm:2.0.4"
  checksum: 10c0/4db28bf065cd7ad897c0700f22d3d0d7c5ed6777e138861c601c496d545340df3fc19e18bd04ff8d95a246a245eb55685b82ca2f8c2ca53a008e9c5316250379
  languageName: node
  linkType: hard

"load-bmfont@npm:^1.3.1, load-bmfont@npm:^1.4.0":
  version: 1.4.2
  resolution: "load-bmfont@npm:1.4.2"
  dependencies:
    buffer-equal: "npm:0.0.1"
    mime: "npm:^1.3.4"
    parse-bmfont-ascii: "npm:^1.0.3"
    parse-bmfont-binary: "npm:^1.0.5"
    parse-bmfont-xml: "npm:^1.1.4"
    phin: "npm:^3.7.1"
    xhr: "npm:^2.0.1"
    xtend: "npm:^4.0.0"
  checksum: 10c0/bc85401964c5b5640ee98581895ddcda666a5e6420773e65a13ae948e2dd9a3e55206e3deb4c98e9bd4d111719d4eb71fc1ce326b1dd060f20afba942b056373
  languageName: node
  linkType: hard

"loader-utils@npm:^2.0.0":
  version: 2.0.4
  resolution: "loader-utils@npm:2.0.4"
  dependencies:
    big.js: "npm:^5.2.2"
    emojis-list: "npm:^3.0.0"
    json5: "npm:^2.1.2"
  checksum: 10c0/d5654a77f9d339ec2a03d88221a5a695f337bf71eb8dea031b3223420bb818964ba8ed0069145c19b095f6c8b8fd386e602a3fc7ca987042bd8bb1dcc90d7100
  languageName: node
  linkType: hard

"loader-utils@npm:^3.2.0":
  version: 3.3.1
  resolution: "loader-utils@npm:3.3.1"
  checksum: 10c0/f2af4eb185ac5bf7e56e1337b666f90744e9f443861ac521b48f093fb9e8347f191c8960b4388a3365147d218913bc23421234e7788db69f385bacfefa0b4758
  languageName: node
  linkType: hard

"local-pkg@npm:^0.5.1":
  version: 0.5.1
  resolution: "local-pkg@npm:0.5.1"
  dependencies:
    mlly: "npm:^1.7.3"
    pkg-types: "npm:^1.2.1"
  checksum: 10c0/ade8346f1dc04875921461adee3c40774b00d4b74095261222ebd4d5fd0a444676e36e325f76760f21af6a60bc82480e154909b54d2d9f7173671e36dacf1808
  languageName: node
  linkType: hard

"local-pkg@npm:^1.0.0":
  version: 1.1.1
  resolution: "local-pkg@npm:1.1.1"
  dependencies:
    mlly: "npm:^1.7.4"
    pkg-types: "npm:^2.0.1"
    quansync: "npm:^0.2.8"
  checksum: 10c0/fe8f9d0443fb066c3f28a4c89d587dd7cba3ab02645cd16598f8d5f30968acf60af1b0ec2d6ad768475ec9f52baad124f31a93d2fbc034f645bcc02bf3a84882
  languageName: node
  linkType: hard

"localstorage-polyfill@npm:^1.0.1":
  version: 1.0.1
  resolution: "localstorage-polyfill@npm:1.0.1"
  checksum: 10c0/63332c5a029da4d5ebe06b819e04428b6ce201bf04474b01220e50ccb7d79507536552f9de71ca382dc2bb5e4339a946ab7580434580b10cf0663bb14b55e4be
  languageName: node
  linkType: hard

"lodash.camelcase@npm:^4.3.0":
  version: 4.3.0
  resolution: "lodash.camelcase@npm:4.3.0"
  checksum: 10c0/fcba15d21a458076dd309fce6b1b4bf611d84a0ec252cb92447c948c533ac250b95d2e00955801ebc367e5af5ed288b996d75d37d2035260a937008e14eaf432
  languageName: node
  linkType: hard

"lodash.debounce@npm:^4.0.8":
  version: 4.0.8
  resolution: "lodash.debounce@npm:4.0.8"
  checksum: 10c0/762998a63e095412b6099b8290903e0a8ddcb353ac6e2e0f2d7e7d03abd4275fe3c689d88960eb90b0dde4f177554d51a690f22a343932ecbc50a5d111849987
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 10c0/ebd04fbca961e6c1d6c0af3799adcc966a1babe798f685bb84e6599266599cd95d94630b10262f5424539bc4640107e8a33aa28585374abf561d30d16f4b39fb
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: "npm:^3.0.2"
  checksum: 10c0/89b2ef2ef45f543011e38737b8a8622a2f8998cddf0e5437174ef8f1f70a8b9d14a918ab3e232cb3ba343b7abddffa667f0b59075b2b80e6b4d63c3de6127482
  languageName: node
  linkType: hard

"magic-string@npm:^0.30.14, magic-string@npm:^0.30.17, magic-string@npm:^0.30.7":
  version: 0.30.17
  resolution: "magic-string@npm:0.30.17"
  dependencies:
    "@jridgewell/sourcemap-codec": "npm:^1.5.0"
  checksum: 10c0/16826e415d04b88378f200fe022b53e638e3838b9e496edda6c0e086d7753a44a6ed187adc72d19f3623810589bf139af1a315541cd6a26ae0771a0193eaf7b8
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": "npm:^3.0.0"
    cacache: "npm:^19.0.1"
    http-cache-semantics: "npm:^4.1.1"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^4.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^1.0.0"
    proc-log: "npm:^5.0.0"
    promise-retry: "npm:^2.0.1"
    ssri: "npm:^12.0.0"
  checksum: 10c0/c40efb5e5296e7feb8e37155bde8eb70bc57d731b1f7d90e35a092fde403d7697c56fb49334d92d330d6f1ca29a98142036d6480a12681133a0a1453164cb2f0
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 10c0/7579ff94e899e2f76ab64491d76cf606274c874d8f2af4a442c016bd85688927fcfca157ba6bf74b08e9439dc010b248ce05b96cc7c126a354c3bae7fcb48b7f
  languageName: node
  linkType: hard

"media-typer@npm:0.3.0":
  version: 0.3.0
  resolution: "media-typer@npm:0.3.0"
  checksum: 10c0/d160f31246907e79fed398470285f21bafb45a62869dc469b1c8877f3f064f5eabc4bcc122f9479b8b605bc5c76187d7871cf84c4ee3ecd3e487da1993279928
  languageName: node
  linkType: hard

"meow@npm:^13.0.0":
  version: 13.2.0
  resolution: "meow@npm:13.2.0"
  checksum: 10c0/d5b339ae314715bcd0b619dd2f8a266891928e21526b4800d49b4fba1cc3fff7e2c1ff5edd3344149fac841bc2306157f858e8c4d5eaee4d52ce52ad925664ce
  languageName: node
  linkType: hard

"merge-descriptors@npm:1.0.3":
  version: 1.0.3
  resolution: "merge-descriptors@npm:1.0.3"
  checksum: 10c0/866b7094afd9293b5ea5dcd82d71f80e51514bed33b4c4e9f516795dc366612a4cbb4dc94356e943a8a6914889a914530badff27f397191b9b75cda20b6bae93
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 10c0/867fdbb30a6d58b011449b8885601ec1690c3e41c759ecd5a9d609094f7aed0096c37823ff4a7190ef0b8f22cc86beb7049196ff68c016e3b3c671d0dac91ce5
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 10c0/254a8a4605b58f450308fc474c82ac9a094848081bf4c06778200207820e5193726dc563a0d2c16468810516a5c97d9d3ea0ca6585d23c58ccfff2403e8dbbeb
  languageName: node
  linkType: hard

"merge@npm:^2.1.1":
  version: 2.1.1
  resolution: "merge@npm:2.1.1"
  checksum: 10c0/9e722a88f661fb4d32bfbab37dcc10c2057d3e3ec7bda5325a13cbfb82a59916963ec99374cca7f5bd3ff8c65a6ffbd9e1061bc0c45c6e3bf211c78af659cb44
  languageName: node
  linkType: hard

"methods@npm:~1.1.2":
  version: 1.1.2
  resolution: "methods@npm:1.1.2"
  checksum: 10c0/bdf7cc72ff0a33e3eede03708c08983c4d7a173f91348b4b1e4f47d4cdbf734433ad971e7d1e8c77247d9e5cd8adb81ea4c67b0a2db526b758b2233d7814b8b2
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.5, micromatch@npm:^4.0.8":
  version: 4.0.8
  resolution: "micromatch@npm:4.0.8"
  dependencies:
    braces: "npm:^3.0.3"
    picomatch: "npm:^2.3.1"
  checksum: 10c0/166fa6eb926b9553f32ef81f5f531d27b4ce7da60e5baf8c021d043b27a388fb95e46a8038d5045877881e673f8134122b59624d5cecbd16eb50a42e7a6b5ca8
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 10c0/0557a01deebf45ac5f5777fe7740b2a5c309c6d62d40ceab4e23da9f821899ce7a900b7ac8157d4548ddbb7beffe9abc621250e6d182b0397ec7f10c7b91a5aa
  languageName: node
  linkType: hard

"mime-types@npm:~2.1.24, mime-types@npm:~2.1.34":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: "npm:1.52.0"
  checksum: 10c0/82fb07ec56d8ff1fc999a84f2f217aa46cb6ed1033fefaabd5785b9a974ed225c90dc72fff460259e66b95b73648596dbcc50d51ed69cdf464af2d237d3149b2
  languageName: node
  linkType: hard

"mime@npm:1.6.0, mime@npm:^1.3.4":
  version: 1.6.0
  resolution: "mime@npm:1.6.0"
  bin:
    mime: cli.js
  checksum: 10c0/b92cd0adc44888c7135a185bfd0dddc42c32606401c72896a842ae15da71eb88858f17669af41e498b463cd7eb998f7b48939a25b08374c7924a9c8a6f8a81b0
  languageName: node
  linkType: hard

"mime@npm:^3.0.0":
  version: 3.0.0
  resolution: "mime@npm:3.0.0"
  bin:
    mime: cli.js
  checksum: 10c0/402e792a8df1b2cc41cb77f0dcc46472b7944b7ec29cb5bbcd398624b6b97096728f1239766d3fdeb20551dd8d94738344c195a6ea10c4f906eb0356323b0531
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: 10c0/b26f5479d7ec6cc2bce275a08f146cf78f5e7b661b18114e2506dd91ec7ec47e7a25bf4360e5438094db0560bcc868079fb3b1fb3892b833c1ecbf63f80c95a4
  languageName: node
  linkType: hard

"min-document@npm:^2.19.0":
  version: 2.19.0
  resolution: "min-document@npm:2.19.0"
  dependencies:
    dom-walk: "npm:^0.1.0"
  checksum: 10c0/783724da716fc73a51c171865d7b29bf2b855518573f82ef61c40d214f6898d7b91b5c5419e4d22693cdb78d4615873ebc3b37d7639d3dd00ca283e5a07c7af9
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.3, minimatch@npm:^9.0.4, minimatch@npm:^9.0.5":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/de96cf5e35bdf0eab3e2c853522f98ffbe9a36c37797778d2665231ec1f20a9447a7e567cb640901f89e4daaa95ae5d70c65a9e8aa2bb0019b6facbc3c0575ed
  languageName: node
  linkType: hard

"minimist@npm:^1.2.6":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 10c0/19d3fcdca050087b84c2029841a093691a91259a47def2f18222f41e7645a0b7c44ef4b40e88a1e58a40c84d2ef0ee6047c55594d298146d0eb3f6b737c20ce6
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/5167e73f62bb74cc5019594709c77e6a742051a647fe9499abf03c71dca75515b7959d67a764bdc4f8b361cf897fbf25e2d9869ee039203ed45240f48b9aa06e
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.1
  resolution: "minipass-fetch@npm:4.0.1"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^7.0.3"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^3.0.1"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/a3147b2efe8e078c9bf9d024a0059339c5a09c5b1dded6900a219c218cc8b1b78510b62dae556b507304af226b18c3f1aeb1d48660283602d5b6586c399eed5c
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/2a51b63feb799d2bb34669205eee7c0eaf9dce01883261a5b77410c9408aa447e478efd191b4de6fc1101e796ff5892f8443ef20d9544385819093dbb32d36bd
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/cbda57cea20b140b797505dc2cac71581a70b3247b84480c1fed5ca5ba46c25ecc25f68bfc9e6dcb1a6e9017dab5c7ada5eab73ad4f0a49d84e35093e0c643f2
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/298f124753efdc745cfe0f2bdfdd81ba25b9f4e753ca4a2066eb17c821f25d48acea607dfc997633ee5bf7b6dfffb4eee4f2051eb168663f0b99fad2fa4829cb
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10c0/a114746943afa1dbbca8249e706d1d38b85ed1298b530f5808ce51f8e9e941962e2a5ad2e00eae7dd21d8a4aae6586a66d4216d1a259385e9d0358f0c1eba16c
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 10c0/b0fd20bb9fb56e5fa9a8bfac539e8915ae07430a619e4b86ff71f5fc757ef3924b23b2c4230393af1eda647ed3d75739e4e0acb250a6b1eb277cf7f8fe449557
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.2
  resolution: "minizlib@npm:3.0.2"
  dependencies:
    minipass: "npm:^7.1.2"
  checksum: 10c0/9f3bd35e41d40d02469cb30470c55ccc21cae0db40e08d1d0b1dff01cc8cc89a6f78e9c5d2b7c844e485ec0a8abc2238111213fdc5b2038e6d1012eacf316f78
  languageName: node
  linkType: hard

"mkdirp@npm:^0.5.1":
  version: 0.5.6
  resolution: "mkdirp@npm:0.5.6"
  dependencies:
    minimist: "npm:^1.2.6"
  bin:
    mkdirp: bin/cmd.js
  checksum: 10c0/e2e2be789218807b58abced04e7b49851d9e46e88a2f9539242cc8a92c9b5c3a0b9bab360bd3014e02a140fc4fbc58e31176c408b493f8a2a6f4986bd7527b01
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 10c0/9f2b975e9246351f5e3a40dcfac99fcd0baa31fbfab615fe059fb11e51f10e4803c63de1f384c54d656e4db31d000e4767e9ef076a22e12a641357602e31d57d
  languageName: node
  linkType: hard

"mlly@npm:^1.7.3, mlly@npm:^1.7.4":
  version: 1.7.4
  resolution: "mlly@npm:1.7.4"
  dependencies:
    acorn: "npm:^8.14.0"
    pathe: "npm:^2.0.1"
    pkg-types: "npm:^1.3.0"
    ufo: "npm:^1.5.4"
  checksum: 10c0/69e738218a13d6365caf930e0ab4e2b848b84eec261597df9788cefb9930f3e40667be9cb58a4718834ba5f97a6efeef31d3b5a95f4388143fd4e0d0deff72ff
  languageName: node
  linkType: hard

"module-alias@npm:^2.2.2":
  version: 2.2.3
  resolution: "module-alias@npm:2.2.3"
  checksum: 10c0/47dc5b6d04f6e7df0ff330ca9b2a37c688a682ed661e9432b0b327e1e6c43eedad052151b8d50d6beea8b924828d2a92fa4625c18d651bf2d93d8f03aa0172fa
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 10c0/f8fda810b39fd7255bbdc451c46286e549794fcc700dc9cd1d25658bbc4dc2563a5de6fe7c60f798a16a60c6ceb53f033cb353f493f0cf63e5199b702943159d
  languageName: node
  linkType: hard

"ms@npm:2.1.3, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: 10c0/d924b57e7312b3b63ad21fc5b3dc0af5e78d61a1fc7cfb5457edaf26326bf62be5307cc87ffb6862ef1c2b33b0233cdb5d4f01c4c958cc0d660948b65a287a48
  languageName: node
  linkType: hard

"muggle-string@npm:^0.3.1":
  version: 0.3.1
  resolution: "muggle-string@npm:0.3.1"
  checksum: 10c0/489b0575fa76e30914393915a36638590052409fca2206a6bef0fb0ad7b181c1cbf99761191bfd16fe402c6f5a3164897965422fa32ef20ada1b44024ba46ab6
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.11":
  version: 3.3.11
  resolution: "nanoid@npm:3.3.11"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 10c0/40e7f70b3d15f725ca072dfc4f74e81fcf1fbb02e491cf58ac0c79093adc9b0a73b152bcde57df4b79cd097e13023d7504acb38404a4da7bc1cd8e887b82fe0b
  languageName: node
  linkType: hard

"negotiator@npm:0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: 10c0/3ec9fd413e7bf071c937ae60d572bc67155262068ed522cf4b3be5edbe6ddf67d095ec03a3a14ebf8fc8e95f8e1d61be4869db0dbb0de696f6b837358bd43fc2
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 10c0/4c559dd52669ea48e1914f9d634227c561221dd54734070791f999c52ed0ff36e437b2e07d5c1f6e32909fc625fe46491c16e4a8f0572567d4dd15c3a4fda04b
  languageName: node
  linkType: hard

"neo-async@npm:^2.6.2":
  version: 2.6.2
  resolution: "neo-async@npm:2.6.2"
  checksum: 10c0/c2f5a604a54a8ec5438a342e1f356dff4bc33ccccdb6dc668d94fe8e5eccfc9d2c2eea6064b0967a767ba63b33763f51ccf2cd2441b461a7322656c1f06b3f5d
  languageName: node
  linkType: hard

"node-addon-api@npm:^7.0.0":
  version: 7.1.1
  resolution: "node-addon-api@npm:7.1.1"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10c0/fb32a206276d608037fa1bcd7e9921e177fe992fc610d098aa3128baca3c0050fc1e014fa007e9b3874cf865ddb4f5bd9f43ccb7cbbbe4efaff6a83e920b17e9
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.2.0
  resolution: "node-gyp@npm:11.2.0"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^14.0.3"
    nopt: "npm:^8.0.0"
    proc-log: "npm:^5.0.0"
    semver: "npm:^7.3.5"
    tar: "npm:^7.4.3"
    tinyglobby: "npm:^0.2.12"
    which: "npm:^5.0.0"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 10c0/bd8d8c76b06be761239b0c8680f655f6a6e90b48e44d43415b11c16f7e8c15be346fba0cbf71588c7cdfb52c419d928a7d3db353afc1d952d19756237d8f10b9
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.19":
  version: 2.0.19
  resolution: "node-releases@npm:2.0.19"
  checksum: 10c0/52a0dbd25ccf545892670d1551690fe0facb6a471e15f2cfa1b20142a5b255b3aa254af5f59d6ecb69c2bec7390bc643c43aa63b13bf5e64b6075952e716b1aa
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.1.0
  resolution: "nopt@npm:8.1.0"
  dependencies:
    abbrev: "npm:^3.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 10c0/62e9ea70c7a3eb91d162d2c706b6606c041e4e7b547cbbb48f8b3695af457dd6479904d7ace600856bf923dd8d1ed0696f06195c8c20f02ac87c1da0e1d315ef
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 10c0/e008c8142bcc335b5e38cf0d63cfd39d6cf2d97480af9abdbe9a439221fd4d749763bab492a8ee708ce7a194bb00c9da6d0a115018672310850489137b3da046
  languageName: node
  linkType: hard

"normalize-range@npm:^0.1.2":
  version: 0.1.2
  resolution: "normalize-range@npm:0.1.2"
  checksum: 10c0/bf39b73a63e0a42ad1a48c2bd1bda5a07ede64a7e2567307a407674e595bcff0fa0d57e8e5f1e7fa5e91000797c7615e13613227aaaa4d6d6e87f5bd5cc95de6
  languageName: node
  linkType: hard

"npm-run-path@npm:^4.0.1":
  version: 4.0.1
  resolution: "npm-run-path@npm:4.0.1"
  dependencies:
    path-key: "npm:^3.0.0"
  checksum: 10c0/6f9353a95288f8455cf64cbeb707b28826a7f29690244c1e4bb61ec573256e021b6ad6651b394eb1ccfd00d6ec50147253aba2c5fe58a57ceb111fad62c519ac
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.3":
  version: 1.13.4
  resolution: "object-inspect@npm:1.13.4"
  checksum: 10c0/d7f8711e803b96ea3191c745d6f8056ce1f2496e530e6a19a0e92d89b0fa3c76d910c31f0aa270432db6bd3b2f85500a376a83aaba849a8d518c8845b3211692
  languageName: node
  linkType: hard

"omggif@npm:^1.0.9":
  version: 1.0.10
  resolution: "omggif@npm:1.0.10"
  checksum: 10c0/5ddb6959555bf16ac93ee8724a6f600b0e97e77855515af9df0f657c69ebe0eb7d769763fdc4765f888827e4e64ca71ebeaf7255c7f51058e4bba5cc7950fe8e
  languageName: node
  linkType: hard

"on-finished@npm:2.4.1":
  version: 2.4.1
  resolution: "on-finished@npm:2.4.1"
  dependencies:
    ee-first: "npm:1.1.1"
  checksum: 10c0/46fb11b9063782f2d9968863d9cbba33d77aa13c17f895f56129c274318b86500b22af3a160fe9995aa41317efcd22941b6eba747f718ced08d9a73afdb087b4
  languageName: node
  linkType: hard

"onetime@npm:^5.1.2":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: "npm:^2.1.0"
  checksum: 10c0/ffcef6fbb2692c3c40749f31ea2e22677a876daea92959b8a80b521d95cca7a668c884d8b2045d1d8ee7d56796aa405c405462af112a1477594cc63531baeb8f
  languageName: node
  linkType: hard

"os-locale-s-fix@npm:^1.0.8-fix-1":
  version: 1.0.8-fix-1
  resolution: "os-locale-s-fix@npm:1.0.8-fix-1"
  dependencies:
    lcid: "npm:^3.0.0"
  checksum: 10c0/1e6b295239bb84929aa2911a61813d32eb9ec705e7d932f1a34f7fe6aa05b81fbf343110ab8e6c026041d83657c1823608dbdb6545f9c3657b03d20eed2fd7e2
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 10c0/46091610da2b38ce47bcd1d8b4835a6fa4e832848a6682cf1652bc93915770f4617afc844c10a77d1b3e56d2472bb2d5622353fa3ead01a7f42b04fc8e744a5c
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 10c0/62ba2785eb655fec084a257af34dbe24292ab74516d6aecef97ef72d4897310bc6898f6c85b5cd22770eaa1ce60d55a0230e150fb6a966e3ecd6c511e23d164b
  languageName: node
  linkType: hard

"pako@npm:^1.0.5":
  version: 1.0.11
  resolution: "pako@npm:1.0.11"
  checksum: 10c0/86dd99d8b34c3930345b8bbeb5e1cd8a05f608eeb40967b293f72fe469d0e9c88b783a8777e4cc7dc7c91ce54c5e93d88ff4b4f060e6ff18408fd21030d9ffbe
  languageName: node
  linkType: hard

"parse-bmfont-ascii@npm:^1.0.3":
  version: 1.0.6
  resolution: "parse-bmfont-ascii@npm:1.0.6"
  checksum: 10c0/f76c57be4678fbb05221e263b21671fa3dbe03d0bae7be133b7f102dbe666958811759b615bfcfc81d76a34efeae1fb76c3305a5a4f28e14eb3baa9ec72c8c4f
  languageName: node
  linkType: hard

"parse-bmfont-binary@npm:^1.0.5":
  version: 1.0.6
  resolution: "parse-bmfont-binary@npm:1.0.6"
  checksum: 10c0/2bcc4f041871ce9cec767105a9438704f114ef43c5827754c4dbcd821f792ec440f8120944d3a5396503e4387e68269ba68d933668a92a3322ad32a079911ea4
  languageName: node
  linkType: hard

"parse-bmfont-xml@npm:^1.1.4":
  version: 1.1.6
  resolution: "parse-bmfont-xml@npm:1.1.6"
  dependencies:
    xml-parse-from-string: "npm:^1.0.0"
    xml2js: "npm:^0.5.0"
  checksum: 10c0/e18e816a2553d3d34795e5a60b584f64a327d4a92f83b48dcb01b9ec30fc75b5338488d9f9d25cd8b463665c13f59264464f39e73c0e8d8d3665ce7f4f128328
  languageName: node
  linkType: hard

"parse-css-font@npm:^4.0.0":
  version: 4.0.0
  resolution: "parse-css-font@npm:4.0.0"
  dependencies:
    css-font-size-keywords: "npm:^1.0.0"
    css-font-stretch-keywords: "npm:^1.0.1"
    css-font-style-keywords: "npm:^1.0.1"
    css-font-weight-keywords: "npm:^1.0.0"
    css-list-helpers: "npm:^2.0.0"
    css-system-font-keywords: "npm:^1.0.0"
    unquote: "npm:^1.1.1"
  checksum: 10c0/4fb6ee7658dc20891c26464c71cc3e5caf495ec88e22d948a35445cfce1f6b2c1a7a5f0537007869e23f8fa7d1d468e1f4bde0859231866f56445506a8c8c0f0
  languageName: node
  linkType: hard

"parse-headers@npm:^2.0.0":
  version: 2.0.6
  resolution: "parse-headers@npm:2.0.6"
  checksum: 10c0/3040ca567f7ceb9b80ffb353401c91c35761365052e30b6795328e78ab549a5fab22be24cbdbb60243500175918fe40812c43b32f689ad775e1c67ba7ba303e9
  languageName: node
  linkType: hard

"parseurl@npm:~1.3.3":
  version: 1.3.3
  resolution: "parseurl@npm:1.3.3"
  checksum: 10c0/90dd4760d6f6174adb9f20cf0965ae12e23879b5f5464f38e92fce8073354341e4b3b76fa3d878351efe7d01e617121955284cfd002ab087fba1a0726ec0b4f5
  languageName: node
  linkType: hard

"path-browserify@npm:^1.0.1":
  version: 1.0.1
  resolution: "path-browserify@npm:1.0.1"
  checksum: 10c0/8b8c3fd5c66bd340272180590ae4ff139769e9ab79522e2eb82e3d571a89b8117c04147f65ad066dccfb42fcad902e5b7d794b3d35e0fd840491a8ddbedf8c66
  languageName: node
  linkType: hard

"path-key@npm:^3.0.0, path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 10c0/748c43efd5a569c039d7a00a03b58eecd1d75f3999f5a28303d75f521288df4823bc057d8784eb72358b2895a05f29a070bc9f1f17d28226cc4e62494cc58c4c
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 10c0/11ce261f9d294cc7a58d6a574b7f1b935842355ec66fba3c3fd79e0f036462eaf07d0aa95bb74ff432f9afef97ce1926c720988c6a7451d8a584930ae7de86e1
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: "npm:^10.2.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: 10c0/32a13711a2a505616ae1cc1b5076801e453e7aae6ac40ab55b388bb91b9d0547a52f5aaceff710ea400205f18691120d4431e520afbe4266b836fadede15872d
  languageName: node
  linkType: hard

"path-to-regexp@npm:0.1.12":
  version: 0.1.12
  resolution: "path-to-regexp@npm:0.1.12"
  checksum: 10c0/1c6ff10ca169b773f3bba943bbc6a07182e332464704572962d277b900aeee81ac6aa5d060ff9e01149636c30b1f63af6e69dd7786ba6e0ddb39d4dee1f0645b
  languageName: node
  linkType: hard

"pathe@npm:^2.0.1, pathe@npm:^2.0.3":
  version: 2.0.3
  resolution: "pathe@npm:2.0.3"
  checksum: 10c0/c118dc5a8b5c4166011b2b70608762e260085180bb9e33e80a50dcdb1e78c010b1624f4280c492c92b05fc276715a4c357d1f9edc570f8f1b3d90b6839ebaca1
  languageName: node
  linkType: hard

"phin@npm:^2.9.1":
  version: 2.9.3
  resolution: "phin@npm:2.9.3"
  checksum: 10c0/5bb94493f1bf1f0b66ca08aee6aaa488c46f4a8af2722d94ef9c8e4aaa3a5952e19aeff37f832ed64a71c98124c73936a30caef42973be21350b36af5fc33ab8
  languageName: node
  linkType: hard

"phin@npm:^3.7.1":
  version: 3.7.1
  resolution: "phin@npm:3.7.1"
  dependencies:
    centra: "npm:^2.7.0"
  checksum: 10c0/114cb1eff5ec817ddaf6748de354b01f02375c755a8da5f2505e0014d2e4eecdb820158e94235cdf9a25fea22c485b113b15ddf053b7699b2e59945bbe3aeaf0
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0, picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: 10c0/e2e3e8170ab9d7c7421969adaa7e1b31434f789afb9b3f115f6b96d91945041ac3ceb02e9ec6fe6510ff036bcc0bf91e69a1772edc0b707e12b19c0f2d6bcf58
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 10c0/26c02b8d06f03206fc2ab8d16f19960f2ff9e81a658f831ecb656d8f17d9edc799e8364b1f4a7873e89d9702dff96204be0fa26fe4181f6843f040f819dac4be
  languageName: node
  linkType: hard

"picomatch@npm:^4.0.2":
  version: 4.0.2
  resolution: "picomatch@npm:4.0.2"
  checksum: 10c0/7c51f3ad2bb42c776f49ebf964c644958158be30d0a510efd5a395e8d49cb5acfed5b82c0c5b365523ce18e6ab85013c9ebe574f60305892ec3fa8eee8304ccc
  languageName: node
  linkType: hard

"pify@npm:^2.3.0":
  version: 2.3.0
  resolution: "pify@npm:2.3.0"
  checksum: 10c0/551ff8ab830b1052633f59cb8adc9ae8407a436e06b4a9718bcb27dc5844b83d535c3a8512b388b6062af65a98c49bdc0dd523d8b2617b188f7c8fee457158dc
  languageName: node
  linkType: hard

"pixelmatch@npm:^4.0.2":
  version: 4.0.2
  resolution: "pixelmatch@npm:4.0.2"
  dependencies:
    pngjs: "npm:^3.0.0"
  bin:
    pixelmatch: bin/pixelmatch
  checksum: 10c0/81fee4a8f3f8d462ada5fc7809399384f99c137fa44974e9eddd52dcd0f0d5838387e2c4cd2397f6adbbc2a6728688ff6124faf0091a1c776d57277f9d42e9fa
  languageName: node
  linkType: hard

"pkg-types@npm:^1.2.1, pkg-types@npm:^1.3.0":
  version: 1.3.1
  resolution: "pkg-types@npm:1.3.1"
  dependencies:
    confbox: "npm:^0.1.8"
    mlly: "npm:^1.7.4"
    pathe: "npm:^2.0.1"
  checksum: 10c0/19e6cb8b66dcc66c89f2344aecfa47f2431c988cfa3366bdfdcfb1dd6695f87dcce37fbd90fe9d1605e2f4440b77f391e83c23255347c35cf84e7fd774d7fcea
  languageName: node
  linkType: hard

"pkg-types@npm:^2.0.1":
  version: 2.1.0
  resolution: "pkg-types@npm:2.1.0"
  dependencies:
    confbox: "npm:^0.2.1"
    exsolve: "npm:^1.0.1"
    pathe: "npm:^2.0.3"
  checksum: 10c0/7729d0a2367ba0aa2caf0f84a6ff0b73b13f4e9a3d62c229ddfa6d45d1f3898f590acdbaa64d779d56737d4ebea2d085961efd59094b8adf8baa34d829599b75
  languageName: node
  linkType: hard

"pngjs@npm:^3.0.0, pngjs@npm:^3.3.3":
  version: 3.4.0
  resolution: "pngjs@npm:3.4.0"
  checksum: 10c0/88ee73e2ad3f736e0b2573722309eb80bd2aa28916f0862379b4fd0f904751b4f61bb6bd1ecd7d4242d331f2b5c28c13309dd4b7d89a9b78306e35122fdc5011
  languageName: node
  linkType: hard

"postcss-import@npm:^14.0.2":
  version: 14.1.0
  resolution: "postcss-import@npm:14.1.0"
  dependencies:
    postcss-value-parser: "npm:^4.0.0"
    read-cache: "npm:^1.0.0"
    resolve: "npm:^1.1.7"
  peerDependencies:
    postcss: ^8.0.0
  checksum: 10c0/0552f48b6849d48b25213e8bfb4b2ae10fcf061224ba17b5c008d8b8de69b9b85442bff6c7ac2a313aec32f14fd000f57720b06f82dc6e9f104405b221a741db
  languageName: node
  linkType: hard

"postcss-load-config@npm:^3.1.1":
  version: 3.1.4
  resolution: "postcss-load-config@npm:3.1.4"
  dependencies:
    lilconfig: "npm:^2.0.5"
    yaml: "npm:^1.10.2"
  peerDependencies:
    postcss: ">=8.0.9"
    ts-node: ">=9.0.0"
  peerDependenciesMeta:
    postcss:
      optional: true
    ts-node:
      optional: true
  checksum: 10c0/7d2cc6695c2fc063e4538316d651a687fdb55e48db453ff699de916a6ee55ab68eac2b120c28a6b8ca7aa746a588888351b810a215b5cd090eabea62c5762ede
  languageName: node
  linkType: hard

"postcss-modules-extract-imports@npm:^3.0.0":
  version: 3.1.0
  resolution: "postcss-modules-extract-imports@npm:3.1.0"
  peerDependencies:
    postcss: ^8.1.0
  checksum: 10c0/402084bcab376083c4b1b5111b48ec92974ef86066f366f0b2d5b2ac2b647d561066705ade4db89875a13cb175b33dd6af40d16d32b2ea5eaf8bac63bd2bf219
  languageName: node
  linkType: hard

"postcss-modules-local-by-default@npm:^4.0.0":
  version: 4.2.0
  resolution: "postcss-modules-local-by-default@npm:4.2.0"
  dependencies:
    icss-utils: "npm:^5.0.0"
    postcss-selector-parser: "npm:^7.0.0"
    postcss-value-parser: "npm:^4.1.0"
  peerDependencies:
    postcss: ^8.1.0
  checksum: 10c0/b0b83feb2a4b61f5383979d37f23116c99bc146eba1741ca3cf1acca0e4d0dbf293ac1810a6ab4eccbe1ee76440dd0a9eb2db5b3bba4f99fc1b3ded16baa6358
  languageName: node
  linkType: hard

"postcss-modules-scope@npm:^3.0.0":
  version: 3.2.1
  resolution: "postcss-modules-scope@npm:3.2.1"
  dependencies:
    postcss-selector-parser: "npm:^7.0.0"
  peerDependencies:
    postcss: ^8.1.0
  checksum: 10c0/bd2d81f79e3da0ef6365b8e2c78cc91469d05b58046b4601592cdeef6c4050ed8fe1478ae000a1608042fc7e692cb51fecbd2d9bce3f4eace4d32e883ffca10b
  languageName: node
  linkType: hard

"postcss-modules-values@npm:^4.0.0":
  version: 4.0.0
  resolution: "postcss-modules-values@npm:4.0.0"
  dependencies:
    icss-utils: "npm:^5.0.0"
  peerDependencies:
    postcss: ^8.1.0
  checksum: 10c0/dd18d7631b5619fb9921b198c86847a2a075f32e0c162e0428d2647685e318c487a2566cc8cc669fc2077ef38115cde7a068e321f46fb38be3ad49646b639dbc
  languageName: node
  linkType: hard

"postcss-modules@npm:^4.3.0":
  version: 4.3.1
  resolution: "postcss-modules@npm:4.3.1"
  dependencies:
    generic-names: "npm:^4.0.0"
    icss-replace-symbols: "npm:^1.1.0"
    lodash.camelcase: "npm:^4.3.0"
    postcss-modules-extract-imports: "npm:^3.0.0"
    postcss-modules-local-by-default: "npm:^4.0.0"
    postcss-modules-scope: "npm:^3.0.0"
    postcss-modules-values: "npm:^4.0.0"
    string-hash: "npm:^1.1.1"
  peerDependencies:
    postcss: ^8.0.0
  checksum: 10c0/944e52c67900869c4f5bbdec7c91b31564ce80aa6addb2eea61e11d336d9f84873de17f10782fa0bab9afae491ce24590a83dac6d825fc4eff625cc85bbbca02
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:^6.0.6":
  version: 6.1.2
  resolution: "postcss-selector-parser@npm:6.1.2"
  dependencies:
    cssesc: "npm:^3.0.0"
    util-deprecate: "npm:^1.0.2"
  checksum: 10c0/523196a6bd8cf660bdf537ad95abd79e546d54180f9afb165a4ab3e651ac705d0f8b8ce6b3164fb9e3279ce482c5f751a69eb2d3a1e8eb0fd5e82294fb3ef13e
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:^7.0.0":
  version: 7.1.0
  resolution: "postcss-selector-parser@npm:7.1.0"
  dependencies:
    cssesc: "npm:^3.0.0"
    util-deprecate: "npm:^1.0.2"
  checksum: 10c0/0fef257cfd1c0fe93c18a3f8a6e739b4438b527054fd77e9a62730a89b2d0ded1b59314a7e4aaa55bc256204f40830fecd2eb50f20f8cb7ab3a10b52aa06c8aa
  languageName: node
  linkType: hard

"postcss-value-parser@npm:^4.0.0, postcss-value-parser@npm:^4.1.0, postcss-value-parser@npm:^4.2.0":
  version: 4.2.0
  resolution: "postcss-value-parser@npm:4.2.0"
  checksum: 10c0/f4142a4f56565f77c1831168e04e3effd9ffcc5aebaf0f538eee4b2d465adfd4b85a44257bb48418202a63806a7da7fe9f56c330aebb3cac898e46b4cbf49161
  languageName: node
  linkType: hard

"postcss@npm:^8.4.35, postcss@npm:^8.4.38, postcss@npm:^8.5.3":
  version: 8.5.5
  resolution: "postcss@npm:8.5.5"
  dependencies:
    nanoid: "npm:^3.3.11"
    picocolors: "npm:^1.1.1"
    source-map-js: "npm:^1.2.1"
  checksum: 10c0/6415873fab84de05c2d8fd18f72ea6654bca437bb4b9f02ca819c438501e4b3a450023e575e17587c6eaa5bedddaaa4dad3af210f5cf166e30cec09cac58baf8
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: 10c0/bbe5edb944b0ad63387a1d5b1911ae93e05ce8d0f60de1035b218cdcceedfe39dbd2c697853355b70f1a090f8f58fe90da487c85216bf9671f9499d1a897e9e3
  languageName: node
  linkType: hard

"process@npm:^0.11.10":
  version: 0.11.10
  resolution: "process@npm:0.11.10"
  checksum: 10c0/40c3ce4b7e6d4b8c3355479df77aeed46f81b279818ccdc500124e6a5ab882c0cc81ff7ea16384873a95a74c4570b01b120f287abbdd4c877931460eca6084b3
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 10c0/9c7045a1a2928094b5b9b15336dcd2a7b1c052f674550df63cc3f36cd44028e5080448175b6f6ca32b642de81150f5e7b1a98b728f15cb069f2dd60ac2616b96
  languageName: node
  linkType: hard

"proxy-addr@npm:~2.0.7":
  version: 2.0.7
  resolution: "proxy-addr@npm:2.0.7"
  dependencies:
    forwarded: "npm:0.2.0"
    ipaddr.js: "npm:1.9.1"
  checksum: 10c0/c3eed999781a35f7fd935f398b6d8920b6fb00bbc14287bc6de78128ccc1a02c89b95b56742bf7cf0362cc333c61d138532049c7dedc7a328ef13343eff81210
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: 10c0/14f76a8206bc3464f794fb2e3d3cc665ae416c01893ad7a02b23766eb07159144ee612ad67af5e84fa4479ccfe67678c4feb126b0485651b302babf66f04f9e9
  languageName: node
  linkType: hard

"qrcode-reader@npm:^1.0.4":
  version: 1.0.4
  resolution: "qrcode-reader@npm:1.0.4"
  checksum: 10c0/2e51f59b3a2362b716585b2a1adb1153b633367f6d0289098be5344018523bf125cf959a6a7959732ba3e631d923918535a6daf57987da0557e87b0c93d6fbd2
  languageName: node
  linkType: hard

"qrcode-terminal@npm:^0.12.0":
  version: 0.12.0
  resolution: "qrcode-terminal@npm:0.12.0"
  bin:
    qrcode-terminal: ./bin/qrcode-terminal.js
  checksum: 10c0/1d8996a743d6c95e22056bd45fe958c306213adc97d7ef8cf1e03bc1aeeb6f27180a747ec3d761141921351eb1e3ca688f7b673ab54cdae9fa358dffaa49563c
  languageName: node
  linkType: hard

"qs@npm:6.13.0":
  version: 6.13.0
  resolution: "qs@npm:6.13.0"
  dependencies:
    side-channel: "npm:^1.0.6"
  checksum: 10c0/62372cdeec24dc83a9fb240b7533c0fdcf0c5f7e0b83343edd7310f0ab4c8205a5e7c56406531f2e47e1b4878a3821d652be4192c841de5b032ca83619d8f860
  languageName: node
  linkType: hard

"quansync@npm:^0.2.8":
  version: 0.2.10
  resolution: "quansync@npm:0.2.10"
  checksum: 10c0/f86f1d644f812a3a7c42de79eb401c47a5a67af82a9adff8a8afb159325e03e00f77cebbf42af6340a0bd47bd0c1fbe999e7caf7e1bbb30d7acb00c8729b7530
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: 10c0/900a93d3cdae3acd7d16f642c29a642aea32c2026446151f0778c62ac089d4b8e6c986811076e1ae180a694cedf077d453a11b58ff0a865629a4f82ab558e102
  languageName: node
  linkType: hard

"range-parser@npm:~1.2.1":
  version: 1.2.1
  resolution: "range-parser@npm:1.2.1"
  checksum: 10c0/96c032ac2475c8027b7a4e9fe22dc0dfe0f6d90b85e496e0f016fbdb99d6d066de0112e680805075bd989905e2123b3b3d002765149294dce0c1f7f01fcc2ea0
  languageName: node
  linkType: hard

"raw-body@npm:2.5.2":
  version: 2.5.2
  resolution: "raw-body@npm:2.5.2"
  dependencies:
    bytes: "npm:3.1.2"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    unpipe: "npm:1.0.0"
  checksum: 10c0/b201c4b66049369a60e766318caff5cb3cc5a900efd89bdac431463822d976ad0670912c931fdbdcf5543207daf6f6833bca57aa116e1661d2ea91e12ca692c4
  languageName: node
  linkType: hard

"read-cache@npm:^1.0.0":
  version: 1.0.0
  resolution: "read-cache@npm:1.0.0"
  dependencies:
    pify: "npm:^2.3.0"
  checksum: 10c0/90cb2750213c7dd7c80cb420654344a311fdec12944e81eb912cd82f1bc92aea21885fa6ce442e3336d9fccd663b8a7a19c46d9698e6ca55620848ab932da814
  languageName: node
  linkType: hard

"readdirp@npm:^4.0.1":
  version: 4.1.2
  resolution: "readdirp@npm:4.1.2"
  checksum: 10c0/60a14f7619dec48c9c850255cd523e2717001b0e179dc7037cfa0895da7b9e9ab07532d324bfb118d73a710887d1e35f79c495fa91582784493e085d18c72c62
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: "npm:^2.2.1"
  checksum: 10c0/6fa848cf63d1b82ab4e985f4cf72bd55b7dcfd8e0a376905804e48c3634b7e749170940ba77b32804d5fe93b3cc521aa95a8d7e7d725f830da6d93f3669ce66b
  languageName: node
  linkType: hard

"regenerate-unicode-properties@npm:^10.2.0":
  version: 10.2.0
  resolution: "regenerate-unicode-properties@npm:10.2.0"
  dependencies:
    regenerate: "npm:^1.4.2"
  checksum: 10c0/5510785eeaf56bbfdf4e663d6753f125c08d2a372d4107bc1b756b7bf142e2ed80c2733a8b54e68fb309ba37690e66a0362699b0e21d5c1f0255dea1b00e6460
  languageName: node
  linkType: hard

"regenerate@npm:^1.4.2":
  version: 1.4.2
  resolution: "regenerate@npm:1.4.2"
  checksum: 10c0/f73c9eba5d398c818edc71d1c6979eaa05af7a808682749dd079f8df2a6d91a9b913db216c2c9b03e0a8ba2bba8701244a93f45211afbff691c32c7b275db1b8
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.13.3":
  version: 0.13.11
  resolution: "regenerator-runtime@npm:0.13.11"
  checksum: 10c0/12b069dc774001fbb0014f6a28f11c09ebfe3c0d984d88c9bced77fdb6fedbacbca434d24da9ae9371bfbf23f754869307fb51a4c98a8b8b18e5ef748677ca24
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.14.1":
  version: 0.14.1
  resolution: "regenerator-runtime@npm:0.14.1"
  checksum: 10c0/1b16eb2c4bceb1665c89de70dcb64126a22bc8eb958feef3cd68fe11ac6d2a4899b5cd1b80b0774c7c03591dc57d16631a7f69d2daa2ec98100e2f29f7ec4cc4
  languageName: node
  linkType: hard

"regexpu-core@npm:^6.2.0":
  version: 6.2.0
  resolution: "regexpu-core@npm:6.2.0"
  dependencies:
    regenerate: "npm:^1.4.2"
    regenerate-unicode-properties: "npm:^10.2.0"
    regjsgen: "npm:^0.8.0"
    regjsparser: "npm:^0.12.0"
    unicode-match-property-ecmascript: "npm:^2.0.0"
    unicode-match-property-value-ecmascript: "npm:^2.1.0"
  checksum: 10c0/bbcb83a854bf96ce4005ee4e4618b71c889cda72674ce6092432f0039b47890c2d0dfeb9057d08d440999d9ea03879ebbb7f26ca005ccf94390e55c348859b98
  languageName: node
  linkType: hard

"regjsgen@npm:^0.8.0":
  version: 0.8.0
  resolution: "regjsgen@npm:0.8.0"
  checksum: 10c0/44f526c4fdbf0b29286101a282189e4dbb303f4013cf3fea058668d96d113b9180d3d03d1e13f6d4cbde38b7728bf951aecd9dc199938c080093a9a6f0d7a6bd
  languageName: node
  linkType: hard

"regjsparser@npm:^0.12.0":
  version: 0.12.0
  resolution: "regjsparser@npm:0.12.0"
  dependencies:
    jsesc: "npm:~3.0.2"
  bin:
    regjsparser: bin/parser
  checksum: 10c0/99d3e4e10c8c7732eb7aa843b8da2fd8b647fe144d3711b480e4647dc3bff4b1e96691ccf17f3ace24aa866a50b064236177cb25e6e4fbbb18285d99edaed83b
  languageName: node
  linkType: hard

"resolve@npm:^1.1.7, resolve@npm:^1.14.2, resolve@npm:^1.22.1":
  version: 1.22.10
  resolution: "resolve@npm:1.22.10"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10c0/8967e1f4e2cc40f79b7e080b4582b9a8c5ee36ffb46041dccb20e6461161adf69f843b43067b4a375de926a2cd669157e29a29578191def399dd5ef89a1b5203
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A^1.1.7#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.14.2#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.22.1#optional!builtin<compat/resolve>":
  version: 1.22.10
  resolution: "resolve@patch:resolve@npm%3A1.22.10#optional!builtin<compat/resolve>::version=1.22.10&hash=c3c19d"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10c0/52a4e505bbfc7925ac8f4cd91fd8c4e096b6a89728b9f46861d3b405ac9a1ccf4dcbf8befb4e89a2e11370dacd0160918163885cbc669369590f2f31f4c58939
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 10c0/59933e8501727ba13ad73ef4a04d5280b3717fd650408460c987392efe9d7be2040778ed8ebe933c5cbd63da3dcc37919c141ef8af0a54a6e4fca5a2af177bfe
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.1.0
  resolution: "reusify@npm:1.1.0"
  checksum: 10c0/4eff0d4a5f9383566c7d7ec437b671cc51b25963bd61bf127c3f3d3f68e44a026d99b8d2f1ad344afff8d278a8fe70a8ea092650a716d22287e8bef7126bb2fa
  languageName: node
  linkType: hard

"rollup@npm:^4.13.0":
  version: 4.43.0
  resolution: "rollup@npm:4.43.0"
  dependencies:
    "@rollup/rollup-android-arm-eabi": "npm:4.43.0"
    "@rollup/rollup-android-arm64": "npm:4.43.0"
    "@rollup/rollup-darwin-arm64": "npm:4.43.0"
    "@rollup/rollup-darwin-x64": "npm:4.43.0"
    "@rollup/rollup-freebsd-arm64": "npm:4.43.0"
    "@rollup/rollup-freebsd-x64": "npm:4.43.0"
    "@rollup/rollup-linux-arm-gnueabihf": "npm:4.43.0"
    "@rollup/rollup-linux-arm-musleabihf": "npm:4.43.0"
    "@rollup/rollup-linux-arm64-gnu": "npm:4.43.0"
    "@rollup/rollup-linux-arm64-musl": "npm:4.43.0"
    "@rollup/rollup-linux-loongarch64-gnu": "npm:4.43.0"
    "@rollup/rollup-linux-powerpc64le-gnu": "npm:4.43.0"
    "@rollup/rollup-linux-riscv64-gnu": "npm:4.43.0"
    "@rollup/rollup-linux-riscv64-musl": "npm:4.43.0"
    "@rollup/rollup-linux-s390x-gnu": "npm:4.43.0"
    "@rollup/rollup-linux-x64-gnu": "npm:4.43.0"
    "@rollup/rollup-linux-x64-musl": "npm:4.43.0"
    "@rollup/rollup-win32-arm64-msvc": "npm:4.43.0"
    "@rollup/rollup-win32-ia32-msvc": "npm:4.43.0"
    "@rollup/rollup-win32-x64-msvc": "npm:4.43.0"
    "@types/estree": "npm:1.0.7"
    fsevents: "npm:~2.3.2"
  dependenciesMeta:
    "@rollup/rollup-android-arm-eabi":
      optional: true
    "@rollup/rollup-android-arm64":
      optional: true
    "@rollup/rollup-darwin-arm64":
      optional: true
    "@rollup/rollup-darwin-x64":
      optional: true
    "@rollup/rollup-freebsd-arm64":
      optional: true
    "@rollup/rollup-freebsd-x64":
      optional: true
    "@rollup/rollup-linux-arm-gnueabihf":
      optional: true
    "@rollup/rollup-linux-arm-musleabihf":
      optional: true
    "@rollup/rollup-linux-arm64-gnu":
      optional: true
    "@rollup/rollup-linux-arm64-musl":
      optional: true
    "@rollup/rollup-linux-loongarch64-gnu":
      optional: true
    "@rollup/rollup-linux-powerpc64le-gnu":
      optional: true
    "@rollup/rollup-linux-riscv64-gnu":
      optional: true
    "@rollup/rollup-linux-riscv64-musl":
      optional: true
    "@rollup/rollup-linux-s390x-gnu":
      optional: true
    "@rollup/rollup-linux-x64-gnu":
      optional: true
    "@rollup/rollup-linux-x64-musl":
      optional: true
    "@rollup/rollup-win32-arm64-msvc":
      optional: true
    "@rollup/rollup-win32-ia32-msvc":
      optional: true
    "@rollup/rollup-win32-x64-msvc":
      optional: true
    fsevents:
      optional: true
  bin:
    rollup: dist/bin/rollup
  checksum: 10c0/a14a16ee5433f9eddfe803ed1a3f4528e3e96f746e55bf88c5482f9a60a4ad61f507b59f46d5d9c8dc98bb7983483e0c94b760ae37c02157eba9da5665c1641b
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: "npm:^1.2.2"
  checksum: 10c0/200b5ab25b5b8b7113f9901bfe3afc347e19bb7475b267d55ad0eb86a62a46d77510cb0f232507c9e5d497ebda569a08a9867d0d14f57a82ad5564d991588b39
  languageName: node
  linkType: hard

"safe-area-insets@npm:^1.4.1":
  version: 1.4.1
  resolution: "safe-area-insets@npm:1.4.1"
  checksum: 10c0/e299c9d7f1e78a7be4ad364fcda0c535fca509ed4cc0912c352201476160f6bfe339efb26f6cbb9e937105921929d05a2bb294b6c0b86f1168b8cd9736de738e
  languageName: node
  linkType: hard

"safe-buffer@npm:5.2.1":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: 10c0/6501914237c0a86e9675d4e51d89ca3c21ffd6a31642efeba25ad65720bce6921c9e7e974e5be91a786b25aa058b5303285d3c15dbabf983a919f5f630d349f3
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 10c0/7e3c8b2e88a1841c9671094bbaeebd94448111dd90a81a1f606f3f67708a6ec57763b3b47f06da09fc6054193e0e6709e77325415dc8422b04497a8070fa02d4
  languageName: node
  linkType: hard

"sass-loader@npm:10.1.1":
  version: 10.1.1
  resolution: "sass-loader@npm:10.1.1"
  dependencies:
    klona: "npm:^2.0.4"
    loader-utils: "npm:^2.0.0"
    neo-async: "npm:^2.6.2"
    schema-utils: "npm:^3.0.0"
    semver: "npm:^7.3.2"
  peerDependencies:
    fibers: ">= 3.1.0"
    node-sass: ^4.0.0 || ^5.0.0
    sass: ^1.3.0
    webpack: ^4.36.0 || ^5.0.0
  peerDependenciesMeta:
    fibers:
      optional: true
    node-sass:
      optional: true
    sass:
      optional: true
  checksum: 10c0/b1d9df6f988e85f07792f6caa5195722391058e5286afd4e7608bd3a7123e36db2c9366f3fe9f6e6ffd50a3c590cb93cb6e415e9880fc96bd2e8146322d2b3de
  languageName: node
  linkType: hard

"sass@npm:^1.89.2":
  version: 1.89.2
  resolution: "sass@npm:1.89.2"
  dependencies:
    "@parcel/watcher": "npm:^2.4.1"
    chokidar: "npm:^4.0.0"
    immutable: "npm:^5.0.2"
    source-map-js: "npm:>=0.6.2 <2.0.0"
  dependenciesMeta:
    "@parcel/watcher":
      optional: true
  bin:
    sass: sass.js
  checksum: 10c0/752ccc7581b0c6395f63918116c20924e99943a86d79e94f5c4a0d41b1e981fe1f0ecd1ee82fff21496f81dbc91f68fb35a498166562ec8ec53e7aad7c3dbd9d
  languageName: node
  linkType: hard

"sax@npm:>=0.6.0":
  version: 1.4.1
  resolution: "sax@npm:1.4.1"
  checksum: 10c0/6bf86318a254c5d898ede6bd3ded15daf68ae08a5495a2739564eb265cd13bcc64a07ab466fb204f67ce472bb534eb8612dac587435515169593f4fffa11de7c
  languageName: node
  linkType: hard

"schema-utils@npm:^3.0.0":
  version: 3.3.0
  resolution: "schema-utils@npm:3.3.0"
  dependencies:
    "@types/json-schema": "npm:^7.0.8"
    ajv: "npm:^6.12.5"
    ajv-keywords: "npm:^3.5.2"
  checksum: 10c0/fafdbde91ad8aa1316bc543d4b61e65ea86970aebbfb750bfb6d8a6c287a23e415e0e926c2498696b242f63af1aab8e585252637fabe811fd37b604351da6500
  languageName: node
  linkType: hard

"scule@npm:^1.3.0":
  version: 1.3.0
  resolution: "scule@npm:1.3.0"
  checksum: 10c0/5d1736daa10622c420f2aa74e60d3c722e756bfb139fa784ae5c66669fdfe92932d30ed5072e4ce3107f9c3053e35ad73b2461cb18de45b867e1d4dea63f8823
  languageName: node
  linkType: hard

"semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: 10c0/e3d79b609071caa78bcb6ce2ad81c7966a46a7431d9d58b8800cfa9cb6a63699b3899a0e4bcce36167a284578212d9ae6942b6929ba4aa5015c079a67751d42d
  languageName: node
  linkType: hard

"semver@npm:^7.3.2, semver@npm:^7.3.5, semver@npm:^7.5.4":
  version: 7.7.2
  resolution: "semver@npm:7.7.2"
  bin:
    semver: bin/semver.js
  checksum: 10c0/aca305edfbf2383c22571cb7714f48cadc7ac95371b4b52362fb8eeffdfbc0de0669368b82b2b15978f8848f01d7114da65697e56cd8c37b0dab8c58e543f9ea
  languageName: node
  linkType: hard

"send@npm:0.19.0":
  version: 0.19.0
  resolution: "send@npm:0.19.0"
  dependencies:
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    mime: "npm:1.6.0"
    ms: "npm:2.1.3"
    on-finished: "npm:2.4.1"
    range-parser: "npm:~1.2.1"
    statuses: "npm:2.0.1"
  checksum: 10c0/ea3f8a67a8f0be3d6bf9080f0baed6d2c51d11d4f7b4470de96a5029c598a7011c497511ccc28968b70ef05508675cebff27da9151dd2ceadd60be4e6cf845e3
  languageName: node
  linkType: hard

"serve-static@npm:1.16.2":
  version: 1.16.2
  resolution: "serve-static@npm:1.16.2"
  dependencies:
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    parseurl: "npm:~1.3.3"
    send: "npm:0.19.0"
  checksum: 10c0/528fff6f5e12d0c5a391229ad893910709bc51b5705962b09404a1d813857578149b8815f35d3ee5752f44cd378d0f31669d4b1d7e2d11f41e08283d5134bd1f
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: 10c0/68733173026766fa0d9ecaeb07f0483f4c2dc70ca376b3b7c40b7cda909f94b0918f6c5ad5ce27a9160bdfb475efaa9d5e705a11d8eaae18f9835d20976028bc
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: 10c0/a41692e7d89a553ef21d324a5cceb5f686d1f3c040759c50aab69688634688c5c327f26f3ecf7001ebfd78c01f3c7c0a11a7c8bfd0a8bc9f6240d4f40b224e4e
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 10c0/1dbed0726dd0e1152a92696c76c7f06084eb32a90f0528d11acd764043aacf76994b2fb30aa1291a21bd019d6699164d048286309a278855ee7bec06cf6fb690
  languageName: node
  linkType: hard

"side-channel-list@npm:^1.0.0":
  version: 1.0.0
  resolution: "side-channel-list@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
  checksum: 10c0/644f4ac893456c9490ff388bf78aea9d333d5e5bfc64cfb84be8f04bf31ddc111a8d4b83b85d7e7e8a7b845bc185a9ad02c052d20e086983cf59f0be517d9b3d
  languageName: node
  linkType: hard

"side-channel-map@npm:^1.0.1":
  version: 1.0.1
  resolution: "side-channel-map@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
  checksum: 10c0/010584e6444dd8a20b85bc926d934424bd809e1a3af941cace229f7fdcb751aada0fb7164f60c2e22292b7fa3c0ff0bce237081fd4cdbc80de1dc68e95430672
  languageName: node
  linkType: hard

"side-channel-weakmap@npm:^1.0.2":
  version: 1.0.2
  resolution: "side-channel-weakmap@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
    side-channel-map: "npm:^1.0.1"
  checksum: 10c0/71362709ac233e08807ccd980101c3e2d7efe849edc51455030327b059f6c4d292c237f94dc0685031dd11c07dd17a68afde235d6cf2102d949567f98ab58185
  languageName: node
  linkType: hard

"side-channel@npm:^1.0.6":
  version: 1.1.0
  resolution: "side-channel@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
    side-channel-list: "npm:^1.0.0"
    side-channel-map: "npm:^1.0.1"
    side-channel-weakmap: "npm:^1.0.2"
  checksum: 10c0/cb20dad41eb032e6c24c0982e1e5a24963a28aa6122b4f05b3f3d6bf8ae7fd5474ef382c8f54a6a3ab86e0cac4d41a23bd64ede3970e5bfb50326ba02a7996e6
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.3":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: 10c0/25d272fa73e146048565e08f3309d5b942c1979a6f4a58a8c59d5fa299728e9c2fcd1a759ec870863b1fd38653670240cd420dad2ad9330c71f36608a6a1c912
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 10c0/41602dce540e46d599edba9d9860193398d135f7ff72cab629db5171516cfae628d21e7bfccde1bbfdf11c48726bc2a6d1a8fb8701125852fbfda7cf19c6aa83
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: 10c0/a16775323e1404dd43fabafe7460be13a471e021637bc7889468eb45ce6a6b207261f454e4e530a19500cc962c4cc5348583520843b363f4193cee5c00e1e539
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:^4.3.4"
    socks: "npm:^2.8.3"
  checksum: 10c0/5d2c6cecba6821389aabf18728325730504bf9bb1d9e342e7987a5d13badd7a98838cc9a55b8ed3cb866ad37cc23e1086f09c4d72d93105ce9dfe76330e9d2a6
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.5
  resolution: "socks@npm:2.8.5"
  dependencies:
    ip-address: "npm:^9.0.5"
    smart-buffer: "npm:^4.2.0"
  checksum: 10c0/e427d0eb0451cfd04e20b9156ea8c0e9b5e38a8d70f21e55c30fbe4214eda37cfc25d782c63f9adc5fbdad6d062a0f127ef2cefc9a44b6fee2b9ea5d1ed10827
  languageName: node
  linkType: hard

"source-map-js@npm:>=0.6.2 <2.0.0, source-map-js@npm:^1.0.2, source-map-js@npm:^1.2.1":
  version: 1.2.1
  resolution: "source-map-js@npm:1.2.1"
  checksum: 10c0/7bda1fc4c197e3c6ff17de1b8b2c20e60af81b63a52cb32ec5a5d67a20a7d42651e2cb34ebe93833c5a2a084377e17455854fee3e21e7925c64a51b6a52b0faf
  languageName: node
  linkType: hard

"source-map-support@npm:~0.5.20":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: "npm:^1.0.0"
    source-map: "npm:^0.6.0"
  checksum: 10c0/9ee09942f415e0f721d6daad3917ec1516af746a8120bba7bb56278707a37f1eb8642bde456e98454b8a885023af81a16e646869975f06afc1a711fb90484e7d
  languageName: node
  linkType: hard

"source-map@npm:0.6.1, source-map@npm:^0.6.0":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 10c0/ab55398007c5e5532957cb0beee2368529618ac0ab372d789806f5718123cc4367d57de3904b4e6a4170eb5a0b0f41373066d02ca0735a0c4d75c7d328d3e011
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: 10c0/09270dc4f30d479e666aee820eacd9e464215cdff53848b443964202bf4051490538e5dd1b42e1a65cf7296916ca17640aebf63dae9812749c7542ee5f288dec
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/caddd5f544b2006e88fa6b0124d8d7b28208b83c72d7672d5ade44d794525d23b540f3396108c4eb9280dcb7c01f0bef50682f5b4b2c34291f7c5e211fd1417d
  languageName: node
  linkType: hard

"statuses@npm:2.0.1":
  version: 2.0.1
  resolution: "statuses@npm:2.0.1"
  checksum: 10c0/34378b207a1620a24804ce8b5d230fea0c279f00b18a7209646d5d47e419d1cc23e7cbf33a25a1e51ac38973dc2ac2e1e9c647a8e481ef365f77668d72becfd0
  languageName: node
  linkType: hard

"string-hash@npm:^1.1.1":
  version: 1.1.3
  resolution: "string-hash@npm:1.1.3"
  checksum: 10c0/179725d7706b49fbbc0a4901703a2d8abec244140879afd5a17908497e586a6b07d738f6775450aefd9f8dd729e4a0abd073fbc6fa3bd020b7a1d2369614af88
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: 10c0/1e525e92e5eae0afd7454086eed9c818ee84374bb80328fc41217ae72ff5f065ef1c9d7f72da41de40c75fa8bb3dee63d92373fd492c84260a552c636392a47b
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/ab9c4264443d35b8b923cbdd513a089a60de339216d3b0ed3be3ba57d6880e1a192b70ae17225f764d7adbf5994e9bb8df253a944736c15a0240eff553c678ca
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: 10c0/1ae5f212a126fe5b167707f716942490e3933085a5ff6c008ab97ab2f272c8025d3aa218b7bd6ab25729ca20cc81cddb252102f8751e13482a5199e873680952
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: 10c0/a198c3762e8832505328cbf9e8c8381de14a4fa50a4f9b2160138158ea88c0f5549fb50cb13c651c3088f47e63a108b34622ec18c0499b6c8c3a5ddf6b305ac4
  languageName: node
  linkType: hard

"strip-final-newline@npm:^2.0.0":
  version: 2.0.0
  resolution: "strip-final-newline@npm:2.0.0"
  checksum: 10c0/bddf8ccd47acd85c0e09ad7375409d81653f645fda13227a9d459642277c253d877b68f2e5e4d819fe75733b0e626bac7e954c04f3236f6d196f79c94fa4a96f
  languageName: node
  linkType: hard

"strip-literal@npm:^2.1.1":
  version: 2.1.1
  resolution: "strip-literal@npm:2.1.1"
  dependencies:
    js-tokens: "npm:^9.0.1"
  checksum: 10c0/66a7353f5ba1ae6a4fb2805b4aba228171847200640083117c41512692e6b2c020e18580402984f55c0ae69c30f857f9a55abd672863e4ca8fdb463fdf93ba19
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 10c0/6c4032340701a9950865f7ae8ef38578d8d7053f5e10518076e6554a9381fa91bd9c6850193695c141f32b21f979c985db07265a758867bac95de05f7d8aeb39
  languageName: node
  linkType: hard

"systemjs@npm:^6.14.3":
  version: 6.15.1
  resolution: "systemjs@npm:6.15.1"
  checksum: 10c0/106e5751a49dbe4acb17fa1474a43b27fd26efbee1b322c00c04c08f3e95de756adfba828d743af89bef7fa10888da8a5c5ceb55dae5c42e4909b151168ad192
  languageName: node
  linkType: hard

"tapable@npm:^2.2.0":
  version: 2.2.2
  resolution: "tapable@npm:2.2.2"
  checksum: 10c0/8ad130aa705cab6486ad89e42233569a1fb1ff21af115f59cebe9f2b45e9e7995efceaa9cc5062510cdb4ec673b527924b2ab812e3579c55ad659ae92117011e
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": "npm:^4.0.0"
    chownr: "npm:^3.0.0"
    minipass: "npm:^7.1.2"
    minizlib: "npm:^3.0.1"
    mkdirp: "npm:^3.0.1"
    yallist: "npm:^5.0.0"
  checksum: 10c0/d4679609bb2a9b48eeaf84632b6d844128d2412b95b6de07d53d8ee8baf4ca0857c9331dfa510390a0727b550fd543d4d1a10995ad86cdf078423fbb8d99831d
  languageName: node
  linkType: hard

"terser@npm:^5.4.0":
  version: 5.42.0
  resolution: "terser@npm:5.42.0"
  dependencies:
    "@jridgewell/source-map": "npm:^0.3.3"
    acorn: "npm:^8.14.0"
    commander: "npm:^2.20.0"
    source-map-support: "npm:~0.5.20"
  bin:
    terser: bin/terser
  checksum: 10c0/f89d5f8c9ccfcd4f6e9a0ecd9569677e2784a876b5cd916e4bc3d19e057ddae3416391df8e40746b29285bdafd48bb3a4230df1453ad8ec8caa7dd67f48f6dc0
  languageName: node
  linkType: hard

"timm@npm:^1.6.1":
  version: 1.7.1
  resolution: "timm@npm:1.7.1"
  checksum: 10c0/28759984dac61dce80a250eb6fed42803f834658b77ef0ebec869cc663110a05e8d453cc317f63fe8c8b3a3401a277dc3483324a652b05a4621a4563eaacabad
  languageName: node
  linkType: hard

"tinycolor2@npm:^1.4.1":
  version: 1.6.0
  resolution: "tinycolor2@npm:1.6.0"
  checksum: 10c0/9aa79a36ba2c2a87cb221453465cabacd04b9e35f9694373e846fdc78b1c768110f81e581ea41440106c0f24d9a023891d0887e8075885e790ac40eb0e74a5c1
  languageName: node
  linkType: hard

"tinyglobby@npm:^0.2.12":
  version: 0.2.14
  resolution: "tinyglobby@npm:0.2.14"
  dependencies:
    fdir: "npm:^6.4.4"
    picomatch: "npm:^4.0.2"
  checksum: 10c0/f789ed6c924287a9b7d3612056ed0cda67306cd2c80c249fd280cf1504742b12583a2089b61f4abbd24605f390809017240e250241f09938054c9b363e51c0a6
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: "npm:^7.0.0"
  checksum: 10c0/487988b0a19c654ff3e1961b87f471702e708fa8a8dd02a298ef16da7206692e8552a0250e8b3e8759270f62e9d8314616f6da274734d3b558b1fc7b7724e892
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 10c0/93937279934bd66cc3270016dd8d0afec14fb7c94a05c72dc57321f8bd1fa97e5bea6d1f7c89e728d077ca31ea125b78320a616a6c6cd0e6b9cb94cb864381c1
  languageName: node
  linkType: hard

"type-is@npm:~1.6.18":
  version: 1.6.18
  resolution: "type-is@npm:1.6.18"
  dependencies:
    media-typer: "npm:0.3.0"
    mime-types: "npm:~2.1.24"
  checksum: 10c0/a23daeb538591b7efbd61ecf06b6feb2501b683ffdc9a19c74ef5baba362b4347e42f1b4ed81f5882a8c96a3bfff7f93ce3ffaf0cbbc879b532b04c97a55db9d
  languageName: node
  linkType: hard

"typescript@npm:^4.9.4":
  version: 4.9.5
  resolution: "typescript@npm:4.9.5"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/5f6cad2e728a8a063521328e612d7876e12f0d8a8390d3b3aaa452a6a65e24e9ac8ea22beb72a924fd96ea0a49ea63bb4e251fb922b12eedfb7f7a26475e5c56
  languageName: node
  linkType: hard

"typescript@patch:typescript@npm%3A^4.9.4#optional!builtin<compat/typescript>":
  version: 4.9.5
  resolution: "typescript@patch:typescript@npm%3A4.9.5#optional!builtin<compat/typescript>::version=4.9.5&hash=289587"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/e3333f887c6829dfe0ab6c1dbe0dd1e3e2aeb56c66460cb85c5440c566f900c833d370ca34eb47558c0c69e78ced4bfe09b8f4f98b6de7afed9b84b8d1dd06a1
  languageName: node
  linkType: hard

"ufo@npm:^1.5.4":
  version: 1.6.1
  resolution: "ufo@npm:1.6.1"
  checksum: 10c0/5a9f041e5945fba7c189d5410508cbcbefef80b253ed29aa2e1f8a2b86f4bd51af44ee18d4485e6d3468c92be9bf4a42e3a2b72dcaf27ce39ce947ec994f1e6b
  languageName: node
  linkType: hard

"uni-preset-vue@workspace:.":
  version: 0.0.0-use.local
  resolution: "uni-preset-vue@workspace:."
  dependencies:
    "@dcloudio/types": "npm:^3.4.8"
    "@dcloudio/uni-app": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-app-harmony": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-app-plus": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-automator": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-cli-shared": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-components": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-h5": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-alipay": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-baidu": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-jd": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-kuaishou": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-lark": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-qq": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-toutiao": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-weixin": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-mp-xhs": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-quickapp-webview": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-stacktracey": "npm:3.0.0-4030620241128001"
    "@dcloudio/uni-ui": "npm:^1.5.7"
    "@dcloudio/vite-plugin-uni": "npm:3.0.0-4030620241128001"
    "@vue/runtime-core": "npm:^3.4.21"
    "@vue/tsconfig": "npm:^0.1.3"
    sass: "npm:^1.89.2"
    sass-loader: "npm:10.1.1"
    typescript: "npm:^4.9.4"
    vite: "npm:5.2.8"
    vue: "npm:^3.4.21"
    vue-i18n: "npm:^9.1.9"
    vue-tsc: "npm:^1.0.24"
  languageName: unknown
  linkType: soft

"unicode-canonical-property-names-ecmascript@npm:^2.0.0":
  version: 2.0.1
  resolution: "unicode-canonical-property-names-ecmascript@npm:2.0.1"
  checksum: 10c0/f83bc492fdbe662860795ef37a85910944df7310cac91bd778f1c19ebc911e8b9cde84e703de631e5a2fcca3905e39896f8fc5fc6a44ddaf7f4aff1cda24f381
  languageName: node
  linkType: hard

"unicode-match-property-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-match-property-ecmascript@npm:2.0.0"
  dependencies:
    unicode-canonical-property-names-ecmascript: "npm:^2.0.0"
    unicode-property-aliases-ecmascript: "npm:^2.0.0"
  checksum: 10c0/4d05252cecaf5c8e36d78dc5332e03b334c6242faf7cf16b3658525441386c0a03b5f603d42cbec0f09bb63b9fd25c9b3b09667aee75463cac3efadae2cd17ec
  languageName: node
  linkType: hard

"unicode-match-property-value-ecmascript@npm:^2.1.0":
  version: 2.2.0
  resolution: "unicode-match-property-value-ecmascript@npm:2.2.0"
  checksum: 10c0/1d0a2deefd97974ddff5b7cb84f9884177f4489928dfcebb4b2b091d6124f2739df51fc6ea15958e1b5637ac2a24cff9bf21ea81e45335086ac52c0b4c717d6d
  languageName: node
  linkType: hard

"unicode-property-aliases-ecmascript@npm:^2.0.0":
  version: 2.1.0
  resolution: "unicode-property-aliases-ecmascript@npm:2.1.0"
  checksum: 10c0/50ded3f8c963c7785e48c510a3b7c6bc4e08a579551489aa0349680a35b1ceceec122e33b2b6c1b579d0be2250f34bb163ac35f5f8695fe10bbc67fb757f0af8
  languageName: node
  linkType: hard

"unimport@npm:^3.11.1, unimport@npm:^3.13.4":
  version: 3.14.6
  resolution: "unimport@npm:3.14.6"
  dependencies:
    "@rollup/pluginutils": "npm:^5.1.4"
    acorn: "npm:^8.14.0"
    escape-string-regexp: "npm:^5.0.0"
    estree-walker: "npm:^3.0.3"
    fast-glob: "npm:^3.3.3"
    local-pkg: "npm:^1.0.0"
    magic-string: "npm:^0.30.17"
    mlly: "npm:^1.7.4"
    pathe: "npm:^2.0.1"
    picomatch: "npm:^4.0.2"
    pkg-types: "npm:^1.3.0"
    scule: "npm:^1.3.0"
    strip-literal: "npm:^2.1.1"
    unplugin: "npm:^1.16.1"
  checksum: 10c0/041cd6d2c85483e68e900c3ae55ddfd60f20b1a43016f6f810e970aba552db2ea5e03817f7c79c16d8648e5757d289cffc6b01f141aa579dbbb4fab6f7a3a4b3
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: "npm:^5.0.0"
  checksum: 10c0/38ae681cceb1408ea0587b6b01e29b00eee3c84baee1e41fd5c16b9ed443b80fba90c40e0ba69627e30855570a34ba8b06702d4a35035d4b5e198bf5a64c9ddc
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 10c0/d324c5a44887bd7e105ce800fcf7533d43f29c48757ac410afd42975de82cc38ea2035c0483f4de82d186691bf3208ef35c644f73aa2b1b20b8e651be5afd293
  languageName: node
  linkType: hard

"universalify@npm:^2.0.0":
  version: 2.0.1
  resolution: "universalify@npm:2.0.1"
  checksum: 10c0/73e8ee3809041ca8b818efb141801a1004e3fc0002727f1531f4de613ea281b494a40909596dae4a042a4fb6cd385af5d4db2e137b1362e0e91384b828effd3a
  languageName: node
  linkType: hard

"unpipe@npm:1.0.0, unpipe@npm:~1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 10c0/193400255bd48968e5c5383730344fbb4fa114cdedfab26e329e50dd2d81b134244bb8a72c6ac1b10ab0281a58b363d06405632c9d49ca9dfd5e90cbd7d0f32c
  languageName: node
  linkType: hard

"unplugin-auto-import@npm:^0.18.2":
  version: 0.18.6
  resolution: "unplugin-auto-import@npm:0.18.6"
  dependencies:
    "@antfu/utils": "npm:^0.7.10"
    "@rollup/pluginutils": "npm:^5.1.3"
    fast-glob: "npm:^3.3.2"
    local-pkg: "npm:^0.5.1"
    magic-string: "npm:^0.30.14"
    minimatch: "npm:^9.0.5"
    unimport: "npm:^3.13.4"
    unplugin: "npm:^1.16.0"
  peerDependencies:
    "@nuxt/kit": ^3.2.2
    "@vueuse/core": "*"
  peerDependenciesMeta:
    "@nuxt/kit":
      optional: true
    "@vueuse/core":
      optional: true
  checksum: 10c0/0d4adf397e9745e5a43bb3675e9b6499b90b71ff3b4a8ec1319914ad18cd58e8a7865d0b64be955a0bb3540329c6837b9d43fa32f4a2e2a0e4d65db5f58a6391
  languageName: node
  linkType: hard

"unplugin@npm:^1.16.0, unplugin@npm:^1.16.1":
  version: 1.16.1
  resolution: "unplugin@npm:1.16.1"
  dependencies:
    acorn: "npm:^8.14.0"
    webpack-virtual-modules: "npm:^0.6.2"
  checksum: 10c0/dd5f8c5727d0135847da73cf03fb199107f1acf458167034886fda3405737dab871ad3926431b4f70e1e82cdac482ac1383cea4019d782a68515c8e3e611b6cc
  languageName: node
  linkType: hard

"unquote@npm:^1.1.1":
  version: 1.1.1
  resolution: "unquote@npm:1.1.1"
  checksum: 10c0/de59fb48cbaadc636002c6563dcb6b1bce95c91ebecb92addbc9bb47982cb03e7d8a8371c9617267b9e5746bbcb4403394139bc1310106b9ac4c26790ed57859
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.3":
  version: 1.1.3
  resolution: "update-browserslist-db@npm:1.1.3"
  dependencies:
    escalade: "npm:^3.2.0"
    picocolors: "npm:^1.1.1"
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 10c0/682e8ecbf9de474a626f6462aa85927936cdd256fe584c6df2508b0df9f7362c44c957e9970df55dfe44d3623807d26316ea2c7d26b80bb76a16c56c37233c32
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: "npm:^2.1.0"
  checksum: 10c0/4ef57b45aa820d7ac6496e9208559986c665e49447cb072744c13b66925a362d96dd5a46c4530a6b8e203e5db5fe849369444440cb22ecfc26c679359e5dfa3c
  languageName: node
  linkType: hard

"utif@npm:^2.0.1":
  version: 2.0.1
  resolution: "utif@npm:2.0.1"
  dependencies:
    pako: "npm:^1.0.5"
  checksum: 10c0/a6c3616c2bba87173903eba49eefa62ce25d91dbfea336baf605f2a7266b679ae2e4e81d23089acd22dbb9f2e5da3d53165e2d842abb08edf0d5213ca168a57e
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.2":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 10c0/41a5bdd214df2f6c3ecf8622745e4a366c4adced864bc3c833739791aeeeb1838119af7daed4ba36428114b5c67dcda034a79c882e97e43c03e66a4dd7389942
  languageName: node
  linkType: hard

"utils-merge@npm:1.0.1":
  version: 1.0.1
  resolution: "utils-merge@npm:1.0.1"
  checksum: 10c0/02ba649de1b7ca8854bfe20a82f1dfbdda3fb57a22ab4a8972a63a34553cf7aa51bc9081cf7e001b035b88186d23689d69e71b510e610a09a4c66f68aa95b672
  languageName: node
  linkType: hard

"vary@npm:~1.1.2":
  version: 1.1.2
  resolution: "vary@npm:1.1.2"
  checksum: 10c0/f15d588d79f3675135ba783c91a4083dcd290a2a5be9fcb6514220a1634e23df116847b1cc51f66bfb0644cf9353b2abb7815ae499bab06e46dd33c1a6bf1f4f
  languageName: node
  linkType: hard

"vite@npm:5.2.8":
  version: 5.2.8
  resolution: "vite@npm:5.2.8"
  dependencies:
    esbuild: "npm:^0.20.1"
    fsevents: "npm:~2.3.3"
    postcss: "npm:^8.4.38"
    rollup: "npm:^4.13.0"
  peerDependencies:
    "@types/node": ^18.0.0 || >=20.0.0
    less: "*"
    lightningcss: ^1.21.0
    sass: "*"
    stylus: "*"
    sugarss: "*"
    terser: ^5.4.0
  dependenciesMeta:
    fsevents:
      optional: true
  peerDependenciesMeta:
    "@types/node":
      optional: true
    less:
      optional: true
    lightningcss:
      optional: true
    sass:
      optional: true
    stylus:
      optional: true
    sugarss:
      optional: true
    terser:
      optional: true
  bin:
    vite: bin/vite.js
  checksum: 10c0/b5717bb00c2570c08ff6d8ed917655e79184efcafa9dd62d52eea19c5d6dfc5a708ec3de9ebc670a7165fc5d401c2bdf1563bb39e2748d8e51e1593d286a9a13
  languageName: node
  linkType: hard

"vue-i18n@npm:^9.1.9":
  version: 9.14.4
  resolution: "vue-i18n@npm:9.14.4"
  dependencies:
    "@intlify/core-base": "npm:9.14.4"
    "@intlify/shared": "npm:9.14.4"
    "@vue/devtools-api": "npm:^6.5.0"
  peerDependencies:
    vue: ^3.0.0
  checksum: 10c0/eceb9c7d5907e7fa6c8305d539d43c638c73600714fc617f6c55b68dbfc39971922f63693c59e318d12a36224c70e3c8b7a908c17d59be2d3427c4d2d19fbd45
  languageName: node
  linkType: hard

"vue-router@npm:^4.3.0":
  version: 4.5.1
  resolution: "vue-router@npm:4.5.1"
  dependencies:
    "@vue/devtools-api": "npm:^6.6.4"
  peerDependencies:
    vue: ^3.2.0
  checksum: 10c0/89fbc11e46c19a4c4d62b807596a0210726dc09bd9e6a319ded1ac0951e6933e581c56acd1b846d3891673b9bad7348564d28ecd8424126d63578b3b5d291d96
  languageName: node
  linkType: hard

"vue-template-compiler@npm:^2.7.14":
  version: 2.7.16
  resolution: "vue-template-compiler@npm:2.7.16"
  dependencies:
    de-indent: "npm:^1.0.2"
    he: "npm:^1.2.0"
  checksum: 10c0/66667ffd5095b707f169c902c4f1a011e9d5ab99fc228e4dac14eb5ca7f107ed99bff261b21578a4b391d2f3d320a8050e754404443472acad13ddaa4bd7bae2
  languageName: node
  linkType: hard

"vue-tsc@npm:^1.0.24":
  version: 1.8.27
  resolution: "vue-tsc@npm:1.8.27"
  dependencies:
    "@volar/typescript": "npm:~1.11.1"
    "@vue/language-core": "npm:1.8.27"
    semver: "npm:^7.5.4"
  peerDependencies:
    typescript: "*"
  bin:
    vue-tsc: bin/vue-tsc.js
  checksum: 10c0/6e6ba37eb7a0c8b9cc613225729c74edf8bd0632d265e62aca28b1969b5615b9dbe2de03aefb8aed2e26fdbd4b93f134785c8ab0095f92c2469192e2db5d09fd
  languageName: node
  linkType: hard

"vue@npm:^3.4.21":
  version: 3.5.16
  resolution: "vue@npm:3.5.16"
  dependencies:
    "@vue/compiler-dom": "npm:3.5.16"
    "@vue/compiler-sfc": "npm:3.5.16"
    "@vue/runtime-dom": "npm:3.5.16"
    "@vue/server-renderer": "npm:3.5.16"
    "@vue/shared": "npm:3.5.16"
  peerDependencies:
    typescript: "*"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/dbc2edacd5fd1eaa6683579e9561960cdc735a0210238e2ce3a2570efbfe24b8a242592fe4c1d2e8e167650077592f36b5056711e70584d9339ff5cb6ef724f6
  languageName: node
  linkType: hard

"webpack-virtual-modules@npm:^0.6.2":
  version: 0.6.2
  resolution: "webpack-virtual-modules@npm:0.6.2"
  checksum: 10c0/5ffbddf0e84bf1562ff86cf6fcf039c74edf09d78358a6904a09bbd4484e8bb6812dc385fe14330b715031892dcd8423f7a88278b57c9f5002c84c2860179add
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 10c0/66522872a768b60c2a65a57e8ad184e5372f5b6a9ca6d5f033d4b0dc98aff63995655a7503b9c0a2598936f532120e81dd8cc155e2e92ed662a2b9377cc4374f
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: "npm:^3.1.1"
  bin:
    node-which: bin/which.js
  checksum: 10c0/e556e4cd8b7dbf5df52408c9a9dd5ac6518c8c5267c8953f5b0564073c66ed5bf9503b14d876d0e9c7844d4db9725fb0dcf45d6e911e17e26ab363dc3965ae7b
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10c0/d15fc12c11e4cbc4044a552129ebc75ee3f57aa9c1958373a4db0292d72282f54373b536103987a4a7594db1ef6a4f10acf92978f79b98c49306a4b58c77d4da
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/138ff58a41d2f877eae87e3282c0630fc2789012fc1af4d6bd626eeb9a2f9a65ca92005e6e69a75c7b85a68479fe7443c7dbe1eb8fbaa681a4491364b7c55c60
  languageName: node
  linkType: hard

"ws@npm:^8.4.2":
  version: 8.18.2
  resolution: "ws@npm:8.18.2"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10c0/4b50f67931b8c6943c893f59c524f0e4905bbd183016cfb0f2b8653aa7f28dad4e456b9d99d285bbb67cca4fedd9ce90dfdfaa82b898a11414ebd66ee99141e4
  languageName: node
  linkType: hard

"xhr@npm:^2.0.1":
  version: 2.6.0
  resolution: "xhr@npm:2.6.0"
  dependencies:
    global: "npm:~4.4.0"
    is-function: "npm:^1.0.1"
    parse-headers: "npm:^2.0.0"
    xtend: "npm:^4.0.0"
  checksum: 10c0/b73b6413b678846c422559cbc0afb2acb34c3a75b4c3bbee1f258e984255a8b8d65c1749b51691278bbdc28781782950d77a759ef5a9adf7774bed2f5dabc954
  languageName: node
  linkType: hard

"xml-parse-from-string@npm:^1.0.0":
  version: 1.0.1
  resolution: "xml-parse-from-string@npm:1.0.1"
  checksum: 10c0/485fd096818c360dce4a115444f2157a4ab61b0ec99753b0381ce40a26978dc2eceb8079bcbbbd5cd570901cd7aa7310cad3d5c47084602a5dbf7e530a59edda
  languageName: node
  linkType: hard

"xml2js@npm:^0.5.0":
  version: 0.5.0
  resolution: "xml2js@npm:0.5.0"
  dependencies:
    sax: "npm:>=0.6.0"
    xmlbuilder: "npm:~11.0.0"
  checksum: 10c0/c9cd07cd19c5e41c740913bbbf16999a37a204488e11f86eddc2999707d43967197e257014d7ed72c8fc4348c192fa47eb352d1d9d05637cefd0d2e24e9aa4c8
  languageName: node
  linkType: hard

"xmlbuilder@npm:~11.0.0":
  version: 11.0.1
  resolution: "xmlbuilder@npm:11.0.1"
  checksum: 10c0/74b979f89a0a129926bc786b913459bdbcefa809afaa551c5ab83f89b1915bdaea14c11c759284bb9b931e3b53004dbc2181e21d3ca9553eeb0b2a7b4e40c35b
  languageName: node
  linkType: hard

"xmlhttprequest@npm:^1.8.0":
  version: 1.8.0
  resolution: "xmlhttprequest@npm:1.8.0"
  checksum: 10c0/c890661562e4cb6c36a126071e956047164296f58b0058ab28a9c9f1c3b46a65bf421a242d3449363a2aadc3d9769146160b10a501710d476a17d77d41a5c99e
  languageName: node
  linkType: hard

"xregexp@npm:3.1.0":
  version: 3.1.0
  resolution: "xregexp@npm:3.1.0"
  checksum: 10c0/bfd0b119a85972eb1fac6ca590ce02a61720bcc6e376f60dfb0a7b7110be511acd2a55987aecaa001d838fe004e2e4a2df4960634c47a5c79aba04176c66496a
  languageName: node
  linkType: hard

"xtend@npm:^4.0.0":
  version: 4.0.2
  resolution: "xtend@npm:4.0.2"
  checksum: 10c0/366ae4783eec6100f8a02dff02ac907bf29f9a00b82ac0264b4d8b832ead18306797e283cf19de776538babfdcb2101375ec5646b59f08c52128ac4ab812ed0e
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 10c0/c66a5c46bc89af1625476f7f0f2ec3653c1a1791d2f9407cfb4c2ba812a1e1c9941416d71ba9719876530e3340a99925f697142989371b72d93b9ee628afd8c1
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 10c0/2286b5e8dbfe22204ab66e2ef5cc9bbb1e55dfc873bbe0d568aa943eb255d131890dfd5bf243637273d31119b870f49c18fcde2c6ffbb7a7a092b870dc90625a
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: 10c0/a499c81ce6d4a1d260d4ea0f6d49ab4da09681e32c3f0472dee16667ed69d01dae63a3b81745a24bd78476ec4fcf856114cb4896ace738e01da34b2c42235416
  languageName: node
  linkType: hard

"yaml@npm:^1.10.2":
  version: 1.10.2
  resolution: "yaml@npm:1.10.2"
  checksum: 10c0/5c28b9eb7adc46544f28d9a8d20c5b3cb1215a886609a2fd41f51628d8aaa5878ccd628b755dbcd29f6bb4921bd04ffbc6dcc370689bb96e594e2f9813d2605f
  languageName: node
  linkType: hard
