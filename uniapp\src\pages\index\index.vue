<template>
  <view class="home-container">
    <!-- 标题 -->
    <view class="section-title">
      <text class="title-text">英语单词游戏</text>
      <text class="title-subtitle">挑战你的词汇量，提升英语水平！</text>
    </view>

    <!-- 用户信息显示 -->
    <view v-if="userInfo" class="user-info-card">
      <view class="user-header">
        <text class="user-name">{{ userInfo.nickname || userInfo.maskedPhone || '游戏玩家' }}</text>
        <view class="help-btn" @click="showHelp">
          <uni-icons
            type="help"
            color="#666"
            size="20"
          />
        </view>
      </view>
      <text class="user-progress">已解锁 {{ userInfo.unlockedLevels || 0 }} 关 | 已完成 {{ userInfo.completedLevelIds?.length || 0 }} 关</text>

      <!-- 每日状态显示 -->
      <view v-if="dailyStatus && !dailyStatus.isVip" class="daily-status">
        <text class="daily-text">
          今日解锁: {{ dailyStatus.dailyUnlockCount }}/{{ dailyStatus.dailyUnlockLimit }}
          <text v-if="dailyStatus.remainingUnlocks > 0" class="remaining">
            (剩余 {{ dailyStatus.remainingUnlocks }} 次)
          </text>
          <text v-else class="no-remaining">
            (已用完)
          </text>
        </text>
        <text v-if="!dailyStatus.dailyShared && dailyStatus.remainingUnlocks <= 3" class="share-tip">
          💡 分享可获得5次额外机会
        </text>
      </view>
      <view v-if="dailyStatus && dailyStatus.isVip" class="vip-status">
        <text class="vip-text">👑 VIP用户 - 无限解锁</text>
      </view>

      <!-- VIP会员按钮 -->
      <view v-if="!dailyStatus?.isVip" class="vip-upgrade-section">
        <button class="vip-upgrade-btn" @click="showVipPackages">
          <text class="vip-btn-text">🎯 升级VIP会员</text>
          <text class="vip-btn-desc">无限解锁 · 畅玩所有关卡</text>
        </button>
      </view>

    </view>

    <!-- 游戏功能区 -->
    <view class="game-section">
      <!-- 全部关卡按钮 -->
      <view class="all-levels-card">
        <view class="card-header">
          <text class="card-title">🎮 开始游戏</text>
          <text class="card-subtitle">选择关卡开始你的英语学习之旅</text>
        </view>
        <view class="card-content">
          <view class="levels-info">
            <view class="info-item">
              <text class="info-label">已解锁关卡</text>
              <text class="info-value">{{ userInfo?.unlockedLevels || 0 }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">已完成关卡</text>
              <text class="info-value">{{ userInfo?.completedLevelIds?.length || 0 }}</text>
            </view>
          </view>
          <button class="all-levels-btn" @click="goToLevelSelection">
            <text class="btn-text">全部关卡</text>
            <text class="btn-icon">→</text>
          </button>
        </view>
      </view>

      <!-- 快速开始按钮 -->
      <view class="quick-start-card">
        <view class="card-header">
          <text class="card-title">⚡ 快速开始</text>
          <text class="card-subtitle">继续上次的游戏进度</text>
        </view>
        <button class="quick-start-btn" @click="quickStart">
          <text class="btn-text">继续游戏</text>
        </button>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="bottom-decoration">
      <text class="decoration-text">挑战更多关卡，提升你的技能！</text>
    </view>

    <!-- 右上角设置按钮 -->
    <view class="floating-settings-btn" @click="showSettings">
      <!-- <text class="settings-icon">⚙️</text> -->
      <text class="settings-text">设置</text>
    </view>

    <!-- 设置弹窗 -->
    <SettingsModal
      :visible="showSettingsModal"
      @close="closeSettings"
      @settingsChange="handleSettingsChange"
    />

    <!-- 悬浮调试按钮（仅开发环境显示） -->
    <!-- #ifdef MP-WEIXIN -->
    <view v-if="isDevelopment" class="floating-debug-btn" @click="goToDebug">
      <text class="debug-icon">🔧</text>
    </view>
    <!-- #endif -->

    <!-- #ifdef H5 -->
    <view v-if="isDevelopment" class="floating-debug-btn" @click="goToDebug">
      <text class="debug-icon">🔧</text>
    </view>
    <!-- #endif -->
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted,   reactive, computed } from 'vue'
import { onLoad, onShareAppMessage, onShow,onHide } from '@dcloudio/uni-app'
import weixinApi from '../../api/weixin'
import { createLoadingState, withLoading, showError, showSuccess } from '../../api/utils'
import { shareUtils } from '../../utils/share'
import { audioManager } from '../../utils/audio'
import { shouldShowDebugFeatures } from '../../utils/development'
import SettingsModal from '../../components/SettingsModal.vue'
import { useGlobalConfig } from '../../composables/useGlobalConfig'
import type { LevelInfo, UserInfo, levelState, DailyStatusResponse, VipPackage, GameSettings } from '../../api/types'

// 关卡状态管理
const levelState = reactive<levelState>({
  level: [],
  currentLevel: null,
  ...createLoadingState()
})

// 用户信息
const userInfo = ref<UserInfo | null>(null)

// 每日状态信息
const dailyStatus = ref<DailyStatusResponse | null>(null)

// VIP套餐信息
const vipPackages = ref<VipPackage[]>([])
const isLoadingVipPackages = ref(false)

// 设置相关
const showSettingsModal = ref(false)
const gameSettings = ref<GameSettings>({
  backgroundMusic: true,
  soundEffects: true,
  vibration: true
})

// 全局配置
const {
  globalConfig,
  isLoading: configLoading,
  getBackgroundMusicUrl,
  showHelpModal,
  initializeGlobalConfig
} = useGlobalConfig()

// 开发环境检测
const isDevelopment = ref(false)

// 页面刷新防抖
let refreshTimer: number | null = null
const isRefreshing = ref(false)

// 页面可见性检查
let visibilityCheckTimer: number | null = null
let lastUserInfoString = ''

// 分享奖励执行状态
let isHandlingShareReward = false

// 计算属性：格式化的关卡列表（兼容原有UI）
const level = computed(() => {
  return levelState.level.map((level, index) => ({
    id: level.id,
    levelNumber: String(index + 1).padStart(2, '0'),
    name: level.name,
    description: level.description,
    library: getLibraryForLevel(level), // 根据关卡获取对应词库
    locked: !level.isUnlocked,
    completed: level.isCompleted
  }))
})

// 词库数据（保留作为备用，实际应该从服务端获取）
const libraries = [
  {
    id: 1,
    name: '小学基础词汇',
    words: [
      { english: 'apple', chinese: '苹果', phonetic: '/ˈæpl/' },
      { english: 'book', chinese: '书', phonetic: '/bʊk/' },
      { english: 'cat', chinese: '猫', phonetic: '/kæt/' },
      { english: 'dog', chinese: '狗', phonetic: '/dɔːɡ/' },
      { english: 'egg', chinese: '鸡蛋', phonetic: '/eɡ/' },
      { english: 'fish', chinese: '鱼', phonetic: '/fɪʃ/' },
      { english: 'green', chinese: '绿色', phonetic: '/ɡriːn/' },
      { english: 'house', chinese: '房子', phonetic: '/haʊs/' },
      { english: 'ice', chinese: '冰', phonetic: '/aɪs/' },
      { english: 'jump', chinese: '跳', phonetic: '/dʒʌmp/' }
    ]
  },
  {
    id: 2,
    name: '动物世界',
    words: [
      { english: 'lion', chinese: '狮子', phonetic: '/ˈlaɪən/' },
      { english: 'tiger', chinese: '老虎', phonetic: '/ˈtaɪɡər/' },
      { english: 'elephant', chinese: '大象', phonetic: '/ˈelɪfənt/' },
      { english: 'monkey', chinese: '猴子', phonetic: '/ˈmʌŋki/' },
      { english: 'rabbit', chinese: '兔子', phonetic: '/ˈræbɪt/' },
      { english: 'bird', chinese: '鸟', phonetic: '/bɜːrd/' },
      { english: 'horse', chinese: '马', phonetic: '/hɔːrs/' },
      { english: 'cow', chinese: '牛', phonetic: '/kaʊ/' }
    ]
  },
  {
    id: 3,
    name: '颜色彩虹',
    words: [
      { english: 'red', chinese: '红色', phonetic: '/red/' },
      { english: 'blue', chinese: '蓝色', phonetic: '/bluː/' },
      { english: 'yellow', chinese: '黄色', phonetic: '/ˈjeloʊ/' },
      { english: 'green', chinese: '绿色', phonetic: '/ɡriːn/' },
      { english: 'purple', chinese: '紫色', phonetic: '/ˈpɜːrpl/' },
      { english: 'orange', chinese: '橙色', phonetic: '/ˈɔːrɪndʒ/' },
      { english: 'pink', chinese: '粉色', phonetic: '/pɪŋk/' },
      { english: 'black', chinese: '黑色', phonetic: '/blæk/' },
      { english: 'white', chinese: '白色', phonetic: '/waɪt/' }
    ]
  },
  {
    id: 4,
    name: '数字王国',
    words: [
      { english: 'one', chinese: '一', phonetic: '/wʌn/' },
      { english: 'two', chinese: '二', phonetic: '/tuː/' },
      { english: 'three', chinese: '三', phonetic: '/θriː/' },
      { english: 'four', chinese: '四', phonetic: '/fɔːr/' },
      { english: 'five', chinese: '五', phonetic: '/faɪv/' },
      { english: 'six', chinese: '六', phonetic: '/sɪks/' },
      { english: 'seven', chinese: '七', phonetic: '/ˈsevn/' },
      { english: 'eight', chinese: '八', phonetic: '/eɪt/' },
      { english: 'nine', chinese: '九', phonetic: '/naɪn/' },
      { english: 'ten', chinese: '十', phonetic: '/ten/' }
    ]
  }
]

/**
 * 根据关卡获取对应的词库
 */
const getLibraryForLevel = (level: LevelInfo) => {
  // 根据关卡难度或名称匹配词库
  // 这里简化处理，实际项目中应该从服务端获取关卡对应的词库
  const difficultyIndex = (level.difficulty - 1) % libraries.length
  return libraries[difficultyIndex]
}

// 页面加载时初始化
onMounted(async () => {
  // 初始化全局配置
  await initializeGlobalConfig()

  await initializePage()

  // 启动页面可见性检查定时器（解决返回首页不刷新的问题）
  startVisibilityCheck()

  // 初始化音频设置
  initAudioSettings()

  // 检测开发环境
  checkDevelopmentEnvironment()
})

// 页面显示时
onShow(() => {
  console.log('首页显示')
  audioManager.onPageShow()

  // 如果背景音乐设置开启，播放主背景音乐
  const settings = audioManager.getSettings()
  if (settings.backgroundMusic) {
    audioManager.playBackgroundMusic('main')
  }
})

// 页面隐藏时
onHide(() => {
  console.log('首页隐藏')
  audioManager.onPageHide()
})

/**
 * 启动页面可见性检查
 */
const startVisibilityCheck = () => {
  // 记录初始用户信息
  const currentUserInfo = uni.getStorageSync('userInfo')
  lastUserInfoString = currentUserInfo || ''

  // 每2秒检查一次用户信息是否有更新
  visibilityCheckTimer = setInterval(async () => {
    const newUserInfo = uni.getStorageSync('userInfo')

    // 如果用户信息有更新，刷新页面数据
    if (newUserInfo !== lastUserInfoString) {
      console.log('检测到用户信息更新，刷新首页数据')
      lastUserInfoString = newUserInfo
      await refreshPageData()
    }
  }, 2000)

  console.log('页面可见性检查已启动')
}

/**
 * 停止页面可见性检查
 */
const stopVisibilityCheck = () => {
  if (visibilityCheckTimer) {
    clearInterval(visibilityCheckTimer)
    visibilityCheckTimer = null
    console.log('页面可见性检查已停止')
  }
}

/**
 * 页面初始化
 */
const initializePage = async () => {
  await withLoading(levelState, async () => {
    // 1. 获取用户信息
    await loadUserInfo()

    // 2. 加载关卡列表
    await loadlevel()
  }, {
    errorMessage: '页面加载失败，请重试'
  })
}

/**
 * 页面数据刷新（用于onShow时调用）
 */
const refreshPageData = async () => {
  if (isRefreshing.value) {
    return
  }

  try {
    isRefreshing.value = true
    console.log('开始刷新首页数据...')

    // 1. 刷新用户信息（可能在游戏中有更新）
    await loadUserInfo()

    // 2. 刷新关卡列表（可能有新的完成状态）
    await refreshlevel()

    console.log('首页数据刷新完成')
  } catch (error) {
    console.error('刷新页面数据失败:', error)
    // 不显示错误提示，避免影响用户体验
  } finally {
    isRefreshing.value = false
  }
}

/**
 * 加载用户信息
 */
const loadUserInfo = async () => {
  try {
    // 优先从服务器获取最新用户信息
    try {
      const freshUserInfo = await weixinApi.refreshUserInfo()
      if (freshUserInfo) {
        userInfo.value = freshUserInfo
        console.log('从服务器获取到最新用户信息:', freshUserInfo)

        // 同时加载每日状态
        await loadDailyStatus()
        return
      }
    } catch (apiError) {
      console.warn('从服务器获取用户信息失败，使用本地缓存:', apiError)
    }

    // 备用：从本地存储获取用户信息
    const localUserInfo = weixinApi.getLocalUserInfo()
    if (localUserInfo) {
      userInfo.value = localUserInfo
      console.log('使用本地缓存用户信息:', localUserInfo)

      // 尝试加载每日状态
      try {
        await loadDailyStatus()
      } catch (error) {
        console.warn('加载每日状态失败:', error)
      }
    } else {
      console.warn('未找到用户信息，可能需要重新登录')
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
  }
}

/**
 * 加载每日状态
 */
const loadDailyStatus = async () => {
  try {
    const status = await weixinApi.getDailyStatus()
    dailyStatus.value = status
    console.log('每日状态加载成功:', status)

    // 显示每日状态信息
    showDailyStatusInfo(status)
  } catch (error) {
    console.error('加载每日状态失败:', error)
  }
}

/**
 * 显示每日状态信息
 */
const showDailyStatusInfo = (status: DailyStatusResponse) => {
  if (status.isVip) {
    console.log('VIP用户，无解锁限制')
    return
  }

  const remainingText = status.remainingUnlocks > 0
    ? `今日还可解锁 ${status.remainingUnlocks} 次`
    : '今日解锁次数已用完'

  console.log(`每日状态: ${remainingText}`)

  // 如果解锁次数不足，提示用户分享获得额外机会
  if (status.remainingUnlocks <= 3 && !status.dailyShared) {
    setTimeout(() => {
      uni.showModal({
        title: '解锁提醒',
        content: `${remainingText}，分享游戏可获得5次额外解锁机会！`,
        showCancel: true,
        cancelText: '稍后再说',
        confirmText: '立即分享',
        success: (res) => {
          if (res.confirm) {
            triggerShare()
          }
        }
      })
    }, 1000)
  }
}

/**
 * 触发分享
 */
const triggerShare = () => {
  // 触发微信分享
  uni.showShareMenu({
    withShareTicket: true
  })
}

/**
 * 初始化音频设置
 */
const initAudioSettings = () => {
  try {
    const settings = audioManager.getSettings()
    gameSettings.value = { ...settings }
    console.log('音频设置初始化完成:', settings)

    // 如果背景音乐开启，播放首页背景音乐
    if (settings.backgroundMusic) {
      const musicUrl = getBackgroundMusicUrl('main')
      console.log('播放首页背景音乐:', musicUrl)
      audioManager.playBackgroundMusic('main', musicUrl)
    }
  } catch (error) {
    console.error('初始化音频设置失败:', error)
  }
}

/**
 * 检测开发环境
 */
const checkDevelopmentEnvironment = () => {
  try {
    isDevelopment.value = shouldShowDebugFeatures()
    console.log('开发环境检测结果:', isDevelopment.value)
  } catch (error) {
    console.error('检测开发环境失败:', error)
    // 默认不显示调试按钮
    isDevelopment.value = false
  }
}

/**
 * 显示设置弹窗
 */
const showSettings = () => {
  // 播放点击音效
  audioManager.playSoundEffect('click')

  showSettingsModal.value = true
  console.log('显示设置弹窗')
}

/**
 * 关闭设置弹窗
 */
const closeSettings = () => {
  showSettingsModal.value = false
  console.log('关闭设置弹窗')
}

/**
 * 处理设置变更
 */
const handleSettingsChange = (newSettings: GameSettings) => {
  gameSettings.value = { ...newSettings }
  console.log('设置已更新:', newSettings)

  // 如果背景音乐设置发生变化
  if (newSettings.backgroundMusic !== gameSettings.value.backgroundMusic) {
    if (newSettings.backgroundMusic) {
      // 开启背景音乐
      audioManager.playBackgroundMusic('main')
    } else {
      // 关闭背景音乐
      audioManager.stopBackgroundMusic()
    }
  }
}

/**
 * 显示帮助信息
 */
const showHelp = () => {
  // 播放点击音效
  audioManager.playSoundEffect('click')

  // 使用全局配置的帮助功能
  showHelpModal()
}

/**
 * 跳转到关卡选择页面
 */
const goToLevelSelection = () => {
  // 播放点击音效
  audioManager.playSoundEffect('click')

  uni.navigateTo({
    url: '/pages/level-selection/index'
  })
}

/**
 * 快速开始游戏
 */
const quickStart = () => {
  // 播放点击音效
  audioManager.playSoundEffect('click')

  // 获取上次游戏的关卡信息
  const lastLevel = uni.getStorageSync('selectedLevel')

  if (lastLevel) {
    try {
      const levelData = JSON.parse(lastLevel)
      uni.showModal({
        title: '继续游戏',
        content: `继续挑战：${levelData.name}`,
        confirmText: '开始',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            uni.navigateTo({
              url: '/pages/game/index'
            })
          }
        }
      })
    } catch (error) {
      console.error('解析关卡数据失败:', error)
      // 如果解析失败，跳转到关卡选择页面
      goToLevelSelection()
    }
  } else {
    // 没有上次游戏记录，跳转到关卡选择页面
    uni.showToast({
      title: '请先选择关卡',
      icon: 'none'
    })
    setTimeout(() => {
      goToLevelSelection()
    }, 1500)
  }
}

/**
 * 显示VIP套餐选择
 */
const showVipPackages = async () => {
  try {
    // 播放点击音效
    audioManager.playSoundEffect('click')

    // 加载VIP套餐列表
    isLoadingVipPackages.value = true
    const packages = await weixinApi.getVipPackages()
    vipPackages.value = packages

    console.log('VIP套餐列表:', packages)

    // 显示套餐选择弹窗
    const packageOptions = packages.map(pkg =>
      `${pkg.name} - ¥${(pkg.price / 100).toFixed(2)}`
    )

    uni.showActionSheet({
      itemList: packageOptions,
      success: (res) => {
        const selectedPackage = packages[res.tapIndex]
        if (selectedPackage) {
          showPaymentConfirm(selectedPackage)
        }
      },
      fail: (err) => {
        console.log('用户取消选择套餐:', err)
      }
    })
  } catch (error) {
    console.error('加载VIP套餐失败:', error)
    uni.showToast({
      title: '加载套餐失败',
      icon: 'none',
      duration: 2000
    })
  } finally {
    isLoadingVipPackages.value = false
  }
}

/**
 * 显示支付确认
 */
const showPaymentConfirm = (vipPackage: VipPackage) => {
  const price = (vipPackage.price / 100).toFixed(2)

  uni.showModal({
    title: '确认购买',
    content: `${vipPackage.name}\n价格：¥${price}\n时长：${vipPackage.duration}天\n\n${vipPackage.description}`,
    showCancel: true,
    cancelText: '取消',
    confirmText: '立即支付',
    success: (res) => {
      if (res.confirm) {
        handleVipPayment(vipPackage)
      }
    }
  })
}

/**
 * 处理VIP支付
 */
const handleVipPayment = async (vipPackage: VipPackage) => {
  try {
    uni.showLoading({
      title: '正在创建订单...'
    })

    // 发起微信支付
    const paymentSuccess = await weixinApi.requestPayment(vipPackage.id)

    if (paymentSuccess) {
      // 支付成功，刷新用户信息和每日状态
      await loadUserInfo()
      await loadDailyStatus()

      uni.showModal({
        title: '支付成功',
        content: `恭喜您成为VIP会员！\n已获得${vipPackage.duration}天VIP特权\n现在可以无限制解锁所有关卡！`,
        showCancel: false,
        confirmText: '开始游戏'
      })
    }
  } catch (error) {
    console.error('VIP支付失败:', error)

    // 根据错误类型显示不同提示
    if (error && typeof error === 'object' && 'errMsg' in error) {
      const errMsg = (error as any).errMsg
      if (errMsg.includes('cancel')) {
        // 用户取消支付，不显示错误提示
        return
      }
    }

    uni.showModal({
      title: '支付失败',
      content: '支付过程中出现问题，请稍后重试。如有疑问请联系客服。',
      showCancel: true,
      cancelText: '稍后重试',
      confirmText: '联系客服'
    })
  } finally {
    uni.hideLoading()
  }
}

/**
 * 加载关卡列表
 */
const loadlevel = async () => {
  try {
    // 从API获取关卡列表（使用通用 openid）
    const apiLevels = await weixinApi.getLevels()
    levelState.level = apiLevels

    console.log('关卡列表加载成功:', apiLevels)
  } catch (error) {
    console.error('加载关卡列表失败:', error)

    // 如果API加载失败，使用本地备用数据
    console.log('使用本地备用关卡数据')
    levelState.level = createFallbacklevel()
  }
}

/**
 * 创建备用关卡数据（当API不可用时）
 */
const createFallbacklevel = (): LevelInfo[] => {
  return [
    {
      id: 'fallback-level-1',
      name: '第1关 - 基础词汇',
      difficulty: 1,
      description: '小学基础词汇练习',
      isUnlocked: true,
      isCompleted: false,
      createdAt: new Date().toISOString()
    },
    {
      id: 'fallback-level-2',
      name: '第2关 - 动物世界',
      difficulty: 2,
      description: '认识可爱的动物',
      isUnlocked: true,
      isCompleted: false,
      createdAt: new Date().toISOString()
    },
    {
      id: 'fallback-level-3',
      name: '第3关 - 颜色彩虹',
      difficulty: 3,
      description: '学习各种颜色',
      isUnlocked: false,
      isCompleted: false,
      createdAt: new Date().toISOString()
    },
    {
      id: 'fallback-level-4',
      name: '第4关 - 数字王国',
      difficulty: 4,
      description: '掌握数字表达',
      isUnlocked: false,
      isCompleted: false,
      createdAt: new Date().toISOString()
    }
  ]
}

/**
 * 刷新关卡数据
 */
const refreshlevel = async () => {
  await loadlevel()
}

/**
 * 跳转到调试页面
 */
const goToDebug = () => {
  uni.navigateTo({
    url: '/pages/debug/index'
  })
}

// 保存关卡完成状态（保留兼容性）
const saveLevelProgress = (levelId: string | number, completed: boolean): void => {
  const completedKey = `level_${levelId}_completed`
  uni.setStorageSync(completedKey, completed.toString())

  // 同时更新关卡状态
  const levelIndex = levelState.level.findIndex(level => level.id === levelId)
  if (levelIndex !== -1) {
    levelState.level[levelIndex].isCompleted = completed
  }
}

// 选择关卡
const selectLevel = async (level: any): Promise<void> => {
  if (level.locked) {
    // 播放失败音效
    audioManager.playSoundEffect('fail')

    // 检查是否可以解锁
    await checkCanUnlock(level)
    return
  }

  // 播放点击音效
  audioManager.playSoundEffect('click')

  console.log('Selected level:', level)

  // 将选择的关卡信息存储到本地
  uni.setStorageSync('selectedLibrary', JSON.stringify(level.library))
  uni.setStorageSync('selectedLevel', JSON.stringify(level))

  // 显示选择提示
  const actionText = level.completed ? '重新挑战' : '进入关卡'
  // showSuccess(`${actionText}: ${level.name}`, 1500)

  // 延迟跳转，让用户看到提示
  setTimeout(() => {
    uni.navigateTo({
      url: '/pages/game/index'
    })
  }, 1500)
}

/**
 * 检查是否可以解锁关卡
 */
const checkCanUnlock = async (level: any): Promise<void> => {
  try {
    // 刷新每日状态
    await loadDailyStatus()

    if (!dailyStatus.value) {
      uni.showToast({
        title: '无法获取解锁状态',
        icon: 'none',
        duration: 1500
      })
      return
    }

    const status = dailyStatus.value

    // VIP用户无限制
    if (status.isVip) {
      uni.showToast({
        title: '该关卡尚未解锁',
        icon: 'none',
        duration: 1500
      })
      return
    }

    // 检查每日解锁次数
    if (status.remainingUnlocks <= 0) {
      // 解锁次数不足
      const message = status.dailyShared
        ? '今日解锁次数已用完，请明天再来！'
        : '今日解锁次数已用完，分享游戏可获得5次额外机会！'

      uni.showModal({
        title: '解锁次数不足',
        content: message,
        showCancel: !status.dailyShared,
        cancelText: '明天再来',
        confirmText: status.dailyShared ? '知道了' : '立即分享',
        success: (res) => {
          if (res.confirm && !status.dailyShared) {
            triggerShare()
          }
        }
      })
    } else {
      // 有解锁次数，但关卡被锁定（可能是前置关卡未完成）
      uni.showToast({
        title: '该关卡尚未解锁',
        icon: 'none',
        duration: 1500
      })
    }
  } catch (error) {
    console.error('检查解锁状态失败:', error)
    uni.showToast({
      title: '该关卡尚未解锁',
      icon: 'none',
      duration: 1500
    })
  }
}

// 重玩关卡
const replayLevel = (level: any): void => {
  console.log('Replay level:', level)

  // 将选择的关卡信息存储到本地
  uni.setStorageSync('selectedLibrary', JSON.stringify(level.library))
  uni.setStorageSync('selectedLevel', JSON.stringify(level))

  // 显示重玩提示
  showSuccess(`重玩关卡: ${level.name}`, 1500)

  // 延迟跳转，让用户看到提示
  setTimeout(() => {
    uni.navigateTo({
      url: '/pages/game/index'
    })
  }, 1500)
}

/**
 * 检查今日是否已经获取过分享奖励
 */
const checkDailyShareReward = (userId: string): boolean => {
  try {
    const today = new Date().toDateString() // 获取今日日期字符串，如 "Mon Dec 25 2023"
    const storageKey = `daily_share_reward_${userId}_${today}`
    const hasSharedToday = uni.getStorageSync(storageKey)

    console.log(`检查每日分享奖励状态 - 用户: ${userId}, 日期: ${today}, 已分享: ${!!hasSharedToday}`)
    return !!hasSharedToday
  } catch (error) {
    console.error('检查每日分享奖励状态失败:', error)
    return false
  }
}

/**
 * 标记今日已获取分享奖励
 */
const markDailyShareReward = (userId: string): void => {
  try {
    const today = new Date().toDateString()
    const storageKey = `daily_share_reward_${userId}_${today}`
    uni.setStorageSync(storageKey, true)

    console.log(`标记每日分享奖励完成 - 用户: ${userId}, 日期: ${today}`)
  } catch (error) {
    console.error('标记每日分享奖励失败:', error)
  }
}

/**
 * 处理分享奖励
 */
const handleShareReward = async (options: any) => {
  try {
    if (!userInfo.value?.id) {
      console.warn('用户信息不存在，无法获取分享奖励')
      return
    }

    // 防止重复执行
    if (isHandlingShareReward) {
      console.log('分享奖励正在处理中，跳过重复请求')
      return
    }

    // 检查今日是否已经获取过分享奖励
    const hasSharedToday = checkDailyShareReward(userInfo.value.id)
    if (hasSharedToday) {
      console.log('今日已获取过分享奖励，跳过本次请求')
      uni.showToast({
        title: '今日已获得分享奖励',
        icon: 'none',
        duration: 2000
      })
      return
    }

    // 标记开始处理
    isHandlingShareReward = true
    console.log('开始处理分享奖励:', options)

    // 延迟获取奖励，确保分享完成
    setTimeout(async () => {
      try {
        // 使用新的分享API
        const shareResponse = await weixinApi.shareForReward()

        if (shareResponse.status === 'success') {
          // 标记今日已获取分享奖励
          markDailyShareReward(userInfo.value!.id)

          // 刷新每日状态
          await loadDailyStatus()

          // 显示奖励获得提示
          uni.showModal({
            title: '分享奖励',
            content: `${shareResponse.message}今日分享奖励已领取完毕。`,
            showCancel: false,
            confirmText: '太棒了',
            success: () => {
              console.log('分享奖励提示已显示')
            }
          })

          console.log('分享奖励获取成功:', shareResponse)
        } else if (shareResponse.status === 'already_shared') {
          console.log('今日已分享过:', shareResponse.message)

          // 标记本地状态
          markDailyShareReward(userInfo.value!.id)

          uni.showToast({
            title: shareResponse.message,
            icon: 'none',
            duration: 2000
          })
        } else {
          console.log('分享奖励获取失败:', shareResponse.message)

          uni.showToast({
            title: shareResponse.message || '分享失败，请重试',
            icon: 'none',
            duration: 2000
          })
        }
      } catch (error) {
        console.error('获取分享奖励失败:', error)
        // 不显示错误提示，避免影响用户体验
      } finally {
        // 重置处理状态
        isHandlingShareReward = false
        console.log('分享奖励处理完成，重置状态')
      }
    }, 2000) // 延迟2秒，确保分享完成
  } catch (error) {
    console.error('处理分享奖励失败:', error)
    // 重置处理状态
    isHandlingShareReward = false
  }
}

onShareAppMessage((options) => {
  const promise = shareUtils.handleShareAppMessage(options, {
      page: 'pages/index/index',
      userId: userInfo.value?.id
  })

  if(options.from === 'menu') {
    // 触发分享以后，通过 /api/v1/weixin/share 接口获取额外的通关机会
    handleShareReward(options)
  }

  return {
    title: "趣护消消乐",
    path: "/pages/index/index",
    imageUrl: "",
    promise,
  }
})

</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: #fdf9f9;
  padding: 32rpx 24rpx;
}

/* 标题区域 */
.section-title {
  text-align: center;
  margin-bottom: 48rpx;
  margin-top: 40rpx;
}

.title-text {
  display: block;
  font-size: 52rpx;
  font-weight: bold;
  color: #111;
  margin-bottom: 16rpx;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
  letter-spacing: 2rpx;
}

.title-subtitle {
  display: block;
  font-size: 28rpx;
  color: #111;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

/* 用户信息卡片 */
.user-info-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 24rpx;
  margin: 0 8rpx 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
}

.user-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #2d3748;
  flex: 1;
}

.help-btn {
  width: 48rpx;
  height: 48rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  cursor: pointer;
}

.user-progress {
  display: block;
  font-size: 24rpx;
  color: #718096;
}

/* 每日状态样式 */
.daily-status {
  margin-top: 16rpx;
  padding: 16rpx;
  background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
  border-radius: 16rpx;
  border: 2rpx solid rgba(116, 185, 255, 0.2);
}

.daily-text {
  display: block;
  font-size: 24rpx;
  color: #2d3748;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.remaining {
  color: #00b894;
  font-weight: bold;
}

.no-remaining {
  color: #e17055;
  font-weight: bold;
}

.share-tip {
  display: block;
  font-size: 22rpx;
  color: #fd79a8;
  font-weight: 500;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* VIP状态样式 */
.vip-status {
  margin-top: 16rpx;
  padding: 16rpx;
  background: linear-gradient(135deg, #ffd700, #ffb347);
  border-radius: 16rpx;
  border: 2rpx solid rgba(255, 215, 0, 0.3);
  text-align: center;
}

.vip-text {
  font-size: 26rpx;
  color: #8b4513;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* VIP升级按钮样式 */
.vip-upgrade-section {
  margin-top: 16rpx;
}

.vip-upgrade-btn {
  width: 100%;
  padding: 20rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 20rpx;
  border: none;
  box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.vip-upgrade-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.vip-upgrade-btn:active::before {
  left: 100%;
}

.vip-btn-text {
  display: block;
  font-size: 28rpx;
  color: #ffffff;
  font-weight: bold;
  text-align: center;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.vip-btn-desc {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  font-weight: 500;
}

/* 右上角设置按钮样式 */
.floating-settings-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 998;
  transition: all 0.3s ease;
  position: absolute;
  top: 20rpx;
  right: 20rpx;
}

.floating-settings-btn:active {
  transform: scale(0.9);
}

.settings-icon {
  font-size: 32rpx;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.settings-text {
  color: #111;
  font-size: 28rpx;
}

/* 悬浮调试按钮样式 */
.floating-debug-btn {
  position: fixed;
  bottom: 100rpx;
  right: 40rpx;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(255, 107, 107, 0.4);
  z-index: 999;
  transition: all 0.3s ease;
  animation: debugPulse 2s infinite;
}

.floating-debug-btn:active {
  transform: scale(0.9);
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.6);
}

.debug-icon {
  font-size: 36rpx;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

@keyframes debugPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 8rpx 20rpx rgba(255, 107, 107, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 12rpx 24rpx rgba(255, 107, 107, 0.6);
  }
}

/* 加载状态 */
.loading-container {
  text-align: center;
  padding: 80rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #111;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

/* 错误状态 */
.error-container {
  text-align: center;
  padding: 80rpx 24rpx;
}

.error-text {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  margin-bottom: 32rpx;
}

.retry-btn {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: white;
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  display: inline-block;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(116, 185, 255, 0.3);
}

.retry-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 6rpx 16rpx rgba(116, 185, 255, 0.4);
}

.retry-text {
  font-size: 28rpx;
  color: white;
  font-weight: 500;
}

/* 调试按钮 */
.debug-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background: rgba(255, 255, 255, 0.2);
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.debug-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.debug-text {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

/* 游戏功能区 */
.game-section {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-bottom: 40rpx;
  padding: 0 8rpx;
}

.all-levels-card,
.quick-start-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.all-levels-card:active,
.quick-start-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.12);
}

.card-header {
  margin-bottom: 24rpx;
}

.card-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 8rpx;
  line-height: 1.2;
}

.card-subtitle {
  display: block;
  font-size: 24rpx;
  color: #718096;
  line-height: 1.4;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.levels-info {
  display: flex;
  justify-content: space-between;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 20rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.info-label {
  font-size: 22rpx;
  color: #718096;
}

.info-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #667eea;
}

.all-levels-btn,
.quick-start-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.all-levels-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #ffffff;
  box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
}

.quick-start-btn {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
  color: #ffffff;
  box-shadow: 0 8rpx 20rpx rgba(78, 205, 196, 0.3);
}

.all-levels-btn:active,
.quick-start-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.btn-text {
  color: inherit;
  font-size: inherit;
  font-weight: inherit;
}

.btn-icon {
  color: inherit;
  font-size: 24rpx;
  font-weight: bold;
}



/* 底部装饰 */
.bottom-decoration {
  text-align: center;
  padding: 48rpx 0 32rpx;
}

.decoration-text {
  font-size: 26rpx;
  color: #111;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  font-weight: 500;
}
</style>