{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,YAAY,CAAA;AACZ,IAAI,KAAK,GAAa,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAA;AACxD,IAAI,MAAe,CAAA;AACnB,IAAI,oBAAoB,GAAG,EAAE,CAAA;AAC7B,IAAI,OAAe,CAAA;AAEnB;IACI,IAAG,CAAC,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,OAAO,GAAG,CAAC,QAAQ,IAAI,UAAU,EAAE;QACxD,OAAO,GAAG,EAAE,CAAA;KACf;SAAM,IAAG,GAAG,CAAC,QAAQ,CAAC,+BAA+B,CAAC,EAAE;QACrD,OAAO,GAAG,KAAK,CAAA;KAClB;SAAM,IAAG,GAAG,CAAC,QAAQ,CAAC,oCAAoC,CAAC,EAAE;QAC1D,OAAO,GAAG,UAAU,CAAA;KACvB;SAAM;QACH,OAAO,GAAG,EAAE,CAAA;KACf;IACD,OAAO,OAAO,CAAA;AAClB,CAAC;AAED;IACI,OAAO,GAAG,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,EAAE,CAAA;IAC9D,IAAG,CAAC,OAAO,EAAE;QACT,KAAK,CAAC,OAAO,CAAC,UAAC,IAAY;YACvB,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAClC,CAAC,CAAC,CAAA;QACF,OAAM;KACT;IAED,kBAAkB,EAAe,EAAE,KAAK;QACpC,IAAI,OAAO,GAAwB,EAAE,CAAC,KAAK,CAAA;QAC3C,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,UAAA,GAAG;YAC1B,IAAI,GAAG,GAAW,KAAK,CAAC,GAAG,CAAC,CAAA;YAC5B,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,CAAA;QACtB,CAAC,CAAC,CAAA;IACN,CAAC;IAED,IAAI,GAAG,GAAe,EAAE,CAAA;IACxB,qBAAqB,QAAmB;QACpC,IAAG,QAAQ,EAAE;YACT,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;SACrB;aAAM;YACH,GAAG,CAAC,OAAO,CAAC,UAAA,EAAE;gBACV,EAAE,EAAE,CAAA;YACR,CAAC,CAAC,CAAA;SACL;IACL,CAAC;IAED,IAAI,aAAa,GAAQ,KAAK,CAAA;IAC9B,IAAI;QACA,IAAI,IAAI,GAAG,MAAM,CAAC,cAAc,CAAC,EAAE,EAAE,SAAS,EAAE;YAC5C,GAAG,EAAE;gBACD,aAAa,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,CAAA;YACrC,CAAC;SACJ,CAAC,CAAA;QACF,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;KAC9C;IAAC,OAAM,CAAC,EAAE;KAEV;IAED,kBAAkB,MAAmB,EAAE,IAAY;QAC/C,IAAI,EAAE,GAAgB,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;QACnD,IAAI,EAAE,GAAgB,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;QACnD,IAAI,UAAU,GAAgB,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;QAC3D,IAAI,UAAU,GAAgB,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;QAC3D,IAAI,CAAC,GAAW,GAAG,CAAA;QACnB,IAAI,GAAG,GAAW,KAAK,CAAA;QACvB,IAAI,MAAM,GAAG;YACT,QAAQ,EAAE,UAAU;YACpB,KAAK,EAAE,CAAC,GAAG,IAAI;YACf,MAAM,EAAE,OAAO;YACf,SAAS,EAAE,YAAY;YACvB,QAAQ,EAAE,QAAQ;YAClB,aAAa,EAAK,OAAO,yBAAoB,IAAI,MAAG;SACvD,CAAA;QACD,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,CAAA;QACpB,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,CAAA;QACpB,QAAQ,CAAC,UAAU,EAAE;YACjB,UAAU,EAAE,IAAI;YAChB,SAAS,EAAE,MAAM;YACjB,KAAK,EAAE,OAAO;YACd,MAAM,EAAE,OAAO;SAClB,CAAC,CAAA;QACF,QAAQ,CAAC,UAAU,EAAE;YACjB,UAAU,EAAE,IAAI;YAChB,SAAS,EAAE,MAAM;YACjB,KAAK,EAAE,MAAM;YACb,MAAM,EAAE,MAAM;SACjB,CAAC,CAAA;QACF,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC,CAAA;QAC1B,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC,CAAA;QAC1B,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAA;QACtB,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAA;QAEtB,WAAW,CAAC;YACR,EAAE,CAAC,SAAS,GAAG,EAAE,CAAC,SAAS,GAAG,GAAG,CAAA;YACjC,IAAI,eAAe,GAAW,EAAE,CAAC,SAAS,CAAA;YAC1C,IAAI,eAAe,GAAW,EAAE,CAAC,SAAS,CAAA;YAC1C;gBACI,IAAG,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,KAAK,EAAE,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,EAAE;oBACrE,OAAM;iBACT;gBACD,EAAE,CAAC,SAAS,GAAG,EAAE,CAAC,SAAS,GAAG,GAAG,CAAA;gBACjC,eAAe,GAAG,EAAE,CAAC,SAAS,CAAA;gBAC9B,eAAe,GAAG,EAAE,CAAC,SAAS,CAAA;gBAC9B,UAAU,CAAC,IAAI,CAAC,CAAA;YACpB,CAAC;YACD,EAAE,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAA;YACtD,EAAE,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAA;QAC1D,CAAC,CAAC,CAAA;QAEF,IAAI,aAAa,GAAwB,gBAAgB,CAAC,EAAE,CAAC,CAAA;QAC7D,MAAM,CAAC,cAAc,CAAC,oBAAoB,EAAE,IAAI,EAAE;YAC9C,YAAY,EAAE,IAAI;YAClB,GAAG;gBACC,OAAO,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAA;YAClD,CAAC;SACJ,CAAC,CAAA;IACN,CAAC;IAED,IAAI,SAAS,GAAgB,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;IAC1D,QAAQ,CAAC,SAAS,EAAE;QAChB,QAAQ,EAAE,UAAU;QACpB,IAAI,EAAE,GAAG;QACT,GAAG,EAAE,GAAG;QACR,KAAK,EAAE,GAAG;QACV,MAAM,EAAE,GAAG;QACX,MAAM,EAAE,IAAI;QACZ,QAAQ,EAAE,QAAQ;QAClB,UAAU,EAAE,QAAQ;KACvB,CAAC,CAAA;IACF,KAAK,CAAC,OAAO,CAAC,UAAA,GAAG;QACb,QAAQ,CAAC,SAAS,EAAE,GAAG,CAAC,CAAA;IAC5B,CAAC,CAAC,CAAA;IACF,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAA;IACpC,WAAW,EAAE,CAAA;IACb,MAAM,GAAG,IAAI,CAAA;AACjB,CAAC;AAED,iBAAiB,IAAY;IACzB,IAAG,CAAC,MAAM,EAAE;QACR,IAAI,EAAE,CAAA;KACT;IACD,OAAO,oBAAoB,CAAC,IAAI,CAAC,CAAA;AACrC,CAAC;AAED,IAAI,WAAW,GAAa,EAAE,CAAA;AAC9B,oBAAoB,IAAY;IAC5B,IAAG,CAAC,WAAW,CAAC,MAAM,EAAE;QACpB,UAAU,CAAC;YACP,IAAI,KAAK,GAAG,EAAE,CAAA;YACd,WAAW,CAAC,OAAO,CAAC,UAAA,IAAI;gBACpB,KAAK,CAAC,IAAI,CAAC,GAAG,oBAAoB,CAAC,IAAI,CAAC,CAAA;YAC5C,CAAC,CAAC,CAAA;YACF,WAAW,CAAC,MAAM,GAAG,CAAC,CAAA;YACtB,SAAS,CAAC,OAAO,CAAC,UAAA,QAAQ;gBACtB,QAAQ,CAAC,KAAK,CAAC,CAAA;YACnB,CAAC,CAAC,CAAA;QACN,CAAC,EAAE,CAAC,CAAC,CAAA;KACR;IACD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;AAC1B,CAAC;AAED,IAAI,SAAS,GAAe,EAAE,CAAA;AAC9B,kBAAkB,QAAkB;IAChC,IAAG,CAAC,UAAU,EAAE,EAAE;QACd,OAAM;KACT;IACD,IAAG,CAAC,MAAM,EAAE;QACR,IAAI,EAAE,CAAA;KACT;IACD,IAAG,OAAO,QAAQ,KAAK,UAAU,EAAE;QAC/B,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;KAC3B;AACL,CAAC;AAED,mBAAmB,QAAkB;IACjC,IAAI,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;IACvC,IAAG,KAAK,IAAI,CAAC,EAAC;QACV,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;KAC7B;AACL,CAAC;AAED,IAAI,cAAc,GAAG;IACjB,IAAI,OAAO;QACP,OAAO,CAAC,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,MAAM,IAAI,CAAC,CAAA;IAC7E,CAAC;IACD,IAAI,GAAG;QACH,OAAO,OAAO,CAAC,KAAK,CAAC,CAAA;IACzB,CAAC;IACD,IAAI,IAAI;QACJ,OAAO,OAAO,CAAC,MAAM,CAAC,CAAA;IAC1B,CAAC;IACD,IAAI,KAAK;QACL,OAAO,OAAO,CAAC,OAAO,CAAC,CAAA;IAC3B,CAAC;IACD,IAAI,MAAM;QACN,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAA;IAC5B,CAAC;IACD,QAAQ,UAAA;IACR,SAAS,WAAA;CACZ,CAAA;AAED,iBAAS,cAAc,CAAA"}