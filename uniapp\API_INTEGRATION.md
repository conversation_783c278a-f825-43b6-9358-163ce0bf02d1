# API集成文档

## 🎯 概述

本文档说明了如何在XX消消乐项目中集成微信小程序API接口，实现用户管理、关卡进度同步等功能。

## 📋 已集成的功能

### 1. 用户认证系统
- ✅ 微信登录
- ✅ 用户绑定
- ✅ 用户信息获取
- ✅ 自动登录状态恢复

### 2. 关卡管理系统
- ✅ 从API获取关卡列表
- ✅ 用户进度同步
- ✅ 关卡解锁状态管理
- ✅ 完成状态追踪

### 3. 游戏进度同步
- ✅ 游戏完成后自动同步进度
- ✅ 本地进度缓存
- ✅ 离线模式支持

## 🏗 架构设计

### API模块结构
```
src/api/
├── config.ts      # API配置（服务器地址、超时等）
├── types.ts       # TypeScript类型定义
├── request.ts     # HTTP请求工具类
├── weixin.ts      # 微信API服务类
├── utils.ts       # 工具函数（错误处理、加载状态等）
└── index.ts       # 统一导出
```

### 核心组件

#### 1. HTTP客户端 (`request.ts`)
- 统一的请求拦截器
- 自动重试机制
- 错误处理
- 响应拦截器

#### 2. 微信API服务 (`weixin.ts`)
- 用户绑定：`bindUser()`
- 获取用户信息：`getUserInfo()`
- 获取关卡列表：`getlevel()`
- 微信登录：`weixinLogin()`

#### 3. 工具函数 (`utils.ts`)
- 加载状态管理：`createLoadingState()`
- 异步操作包装：`withLoading()`
- 错误处理：`showError()`, `showSuccess()`

## 🚀 使用方法

### 1. 基础配置

在 `src/api/config.ts` 中配置API服务器地址：

```typescript
export const API_CONFIG = {
  DEV_BASE_URL: 'http://127.0.0.1:3001',  // 开发环境
  PROD_BASE_URL: 'https://your-api.com',  // 生产环境
  // ...其他配置
}
```

### 2. 用户认证流程

应用启动时会自动执行用户认证：

```typescript
// App.vue 中的认证流程
onLaunch(async () => {
  await initializeApp()  // 自动处理登录和用户绑定
})
```

### 3. 获取用户信息

```typescript
import { weixinApi } from '@/api'

// 获取本地用户信息
const userInfo = weixinApi.getLocalUserInfo()

// 从服务器刷新用户信息
const freshUserInfo = await weixinApi.refreshUserInfo()
```

### 4. 关卡数据获取

```typescript
import { weixinApi } from '@/api'

// 获取关卡列表（包含用户进度）
const openid = weixinApi.getLocalOpenid()
const level = await weixinApi.getlevel(openid)
```

### 5. 错误处理和加载状态

```typescript
import { createLoadingState, withLoading } from '@/api/utils'

const loadingState = createLoadingState()

// 自动管理加载状态的异步操作
await withLoading(loadingState, async () => {
  // 你的异步操作
  await someApiCall()
}, {
  errorMessage: '操作失败，请重试'
})
```

## 🔧 API接口说明

### 用户绑定接口
```
POST /api/v1/weixin/user/bind
```

**请求参数：**
```json
{
  "openid": "wx_openid_123456",
  "phone": "13800138000",
  "nickname": "微信用户",
  "avatarUrl": "https://wx.qlogo.cn/..."
}
```

### 获取用户信息接口
```
GET /api/v1/weixin/user/info?openid={openid}
```

### 获取关卡列表接口
```
GET /api/v1/weixin/levels?openid={openid}
```

## 🎮 游戏流程集成

### 1. 应用启动
1. 检查本地用户信息
2. 如果有用户信息，自动登录
3. 如果没有，执行微信登录和绑定流程

### 2. 首页关卡显示
1. 从API获取关卡列表
2. 显示用户解锁进度
3. 支持离线模式（使用本地备用数据）

### 3. 游戏完成
1. 保存本地进度（兼容性）
2. 同步进度到服务器
3. 刷新用户信息

## 🔒 数据安全

### 1. 手机号加密
- 显示格式：`138****8000`
- 保护用户隐私

### 2. 本地存储
- 用户信息：`userInfo`
- 用户openid：`userOpenid`
- 关卡进度：本地缓存

### 3. 错误处理
- 网络错误自动重试
- 优雅降级到本地数据
- 用户友好的错误提示

## 🚨 注意事项

### 1. 开发环境
- 非微信小程序环境会使用模拟数据
- 开发时可以正常测试所有功能

### 2. 生产环境
- 需要配置正确的API服务器地址
- 需要微信小程序的真实openid

### 3. 兼容性
- 保持与原有本地存储的兼容性
- 支持离线模式
- 渐进式升级

## 📊 状态管理

### 全局状态
- 用户登录状态
- 用户信息
- 关卡数据

### 页面状态
- 加载状态
- 错误状态
- 同步状态

## 🔄 数据流

```
用户启动应用
    ↓
检查本地用户信息
    ↓
如果没有 → 微信登录 → 用户绑定
    ↓
获取关卡列表
    ↓
显示首页
    ↓
用户选择关卡
    ↓
进入游戏
    ↓
游戏完成 → 同步进度 → 更新用户信息
```

## 🛠 调试工具

### 1. 控制台日志
- 所有API请求都有详细日志
- 错误信息包含完整堆栈

### 2. 调试模式
- 游戏页面支持调试模式
- 显示卡片位置信息

### 3. 错误提示
- 用户友好的错误消息
- 自动重试机制

## 📈 性能优化

### 1. 请求优化
- 自动重试机制
- 请求超时控制
- 响应缓存

### 2. 状态管理
- 响应式状态更新
- 最小化重新渲染

### 3. 用户体验
- 加载状态显示
- 错误状态处理
- 离线模式支持

---

**集成完成！** 🎉

现在项目已经完全集成了微信小程序API，支持用户管理、关卡进度同步等功能，同时保持了良好的用户体验和错误处理机制。
