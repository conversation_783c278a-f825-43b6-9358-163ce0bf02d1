# 简化版微信小程序登录流程

## 🎯 概述

根据uniapp官方文档，删除了getUserInfo和getUserProfile API，只使用uni.login进行微信登录，并获取WEIXIN_LOGIN_FLOW.md中登录接口需要的值。

## 📋 简化后的登录流程

### 1. 登录流程图
```
用户打开小程序
    ↓
调用 uni.checkSession() 检查登录状态
    ↓
登录状态过期或首次登录
    ↓
调用 uni.login() 获取 code
    ↓
发送 code 和可选参数到服务端 /api/v1/weixin/login
    ↓
服务端返回登录状态
    ↓
status: 'success' → 登录成功，获取用户信息和关卡
    ↓
status: 'need_bind' → 需要绑定手机号
    ↓
调用 /api/v1/weixin/bind-phone 完成绑定
    ↓
获取用户信息和关卡列表
```

### 2. 核心API使用

#### uni.login() - 获取登录凭证
```typescript
async weixinLogin(): Promise<WeixinLoginResult> {
  return new Promise((resolve, reject) => {
    uni.login({
      provider: 'weixin',
      timeout: 10000,
      success: (loginRes) => {
        if (loginRes.code) {
          resolve({ code: loginRes.code })
        } else {
          reject(new Error('未获取到登录凭证'))
        }
      },
      fail: (error) => {
        reject(new Error(`微信登录失败: ${error.errMsg}`))
      }
    })
  })
}
```

#### uni.checkSession() - 检查登录状态
```typescript
async checkSession(): Promise<boolean> {
  return new Promise((resolve) => {
    uni.checkSession({
      success: () => resolve(true),
      fail: () => resolve(false)
    })
  })
}
```

### 3. 登录接口参数

根据WEIXIN_LOGIN_FLOW.md，登录接口需要的参数：

```typescript
interface WeixinLoginRequest {
  code: string        // 必需 - 从uni.login()获取
  phone?: string      // 可选 - 用户手机号
  nickname?: string   // 可选 - 用户昵称
  avatarUrl?: string  // 可选 - 用户头像URL
}
```

### 4. 简化后的实现

#### 完整登录流程
```typescript
async performWeixinLogin(userInfo?: { 
  phone?: string; 
  nickname?: string; 
  avatarUrl?: string 
}): Promise<WeixinLoginResponse> {
  try {
    // 1. 获取微信登录凭证
    const loginResult = await this.weixinLogin()
    console.log('获取微信登录凭证成功:', loginResult.code)
    
    // 2. 构造登录请求参数
    const loginRequest: WeixinLoginRequest = {
      code: loginResult.code,
      phone: userInfo?.phone || '', // 可选
      nickname: userInfo?.nickname || '微信用户', // 可选
      avatarUrl: userInfo?.avatarUrl || '' // 可选
    }

    // 3. 调用服务端登录接口
    const url = getWeixinApiUrl('/login')
    const response = await httpClient.post<WeixinLoginResponse>(url, loginRequest)

    // 4. 处理登录响应
    if (response.status === 'success') {
      // 登录成功，保存用户信息和openid
      uni.setStorageSync(STORAGE_KEYS.USER_INFO, JSON.stringify(response.userInfo))
      this.setOpenid(response.openid)
    } else if (response.status === 'need_bind') {
      // 需要绑定手机号，保存openid
      this.setOpenid(response.openid)
    }

    return response
  } catch (error) {
    console.error('微信登录流程失败:', error)
    throw error
  }
}
```

#### App.vue中的登录流程
```typescript
const performWeixinLogin = async () => {
  try {
    // 1. 检查登录状态是否过期
    const isSessionValid = await weixinApi.checkSession()
    
    // 2. 如果登录状态过期或首次登录，重新登录
    if (!isSessionValid || !userState.userInfo) {
      // 3. 执行完整的微信登录流程（只使用uni.login获取code）
      const loginResponse = await weixinApi.performWeixinLogin({
        phone: '', // 实际项目中需要用户授权获取
        nickname: '微信用户', // 可选参数
        avatarUrl: '' // 可选参数
      })

      // 4. 处理登录结果
      if (loginResponse.status === 'success') {
        // 登录成功
        userState.userInfo = loginResponse.userInfo
        userState.isLoggedIn = true
        
        // 获取关卡信息
        await loadUserlevel()
        
        uni.showToast({ title: '登录成功', icon: 'success' })
      } else if (loginResponse.status === 'need_bind') {
        // 需要绑定手机号
        await handlePhoneBinding(loginResponse.openid)
      }
    } else {
      // 登录状态有效，直接获取关卡信息
      await loadUserlevel()
    }
  } catch (error) {
    console.error('微信登录流程失败:', error)
    throw error
  }
}
```

## 🔧 删除的API

### 已删除的方法
- ❌ `getWeixinUserProfile()` - 获取用户信息
- ❌ `getPhoneNumber()` - 获取手机号
- ❌ 相关的用户信息获取逻辑

### 删除原因
1. **简化流程**：只使用uni.login获取必需的code
2. **减少权限**：不需要用户授权获取个人信息
3. **提高兼容性**：避免getUserProfile在新版本中的限制
4. **专注核心**：专注于登录凭证获取和服务端交互

## 🎮 用户体验

### 1. 简化的登录体验
- 用户只需要授权登录，无需额外的信息授权
- 减少了用户操作步骤
- 提高了登录成功率

### 2. 默认信息处理
- 使用默认昵称"微信用户"
- 使用空的头像URL
- 使用默认手机号（实际项目中需要用户输入）

### 3. 错误处理
- 完善的错误处理和重试机制
- 用户友好的错误提示
- 优雅降级到本地数据

## 🛠 调试功能

### 简化后的调试接口
- **测试获取微信code**：测试uni.login()功能
- **检查登录状态**：测试uni.checkSession()功能
- **测试完整登录流程**：测试整个登录流程
- **测试手机号绑定**：测试绑定流程

### 调试页面使用
```typescript
// 测试获取微信code
const testWeixinLoginCode = async () => {
  const loginResult = await weixinApi.weixinLogin()
  // 显示结果：{ code: "xxx" }
}

// 测试完整登录流程
const testWeixinLogin = async () => {
  const loginResponse = await weixinApi.performWeixinLogin({
    phone: '',
    nickname: '测试用户',
    avatarUrl: ''
  })
  // 显示登录结果
}
```

## 📱 平台兼容性

### 微信小程序环境
- 使用真实的uni.login()获取code
- 使用真实的uni.checkSession()检查状态
- 完整的错误处理

### H5开发环境
- 使用模拟的code数据
- 跳过登录状态检查
- 便于开发和调试

### 生产部署
- 支持真实微信小程序环境
- 完整的错误处理和重试机制
- 生产就绪的代码质量

## 🚀 使用指南

### 1. 开发环境测试
```bash
# 启动H5版本
npm run dev:h5

# 访问调试页面测试登录功能
http://localhost:5173/ → 点击"API调试"
```

### 2. 微信小程序测试
```bash
# 构建微信小程序版本
npm run dev:mp-weixin

# 使用微信开发者工具打开
# dist/dev/mp-weixin
```

### 3. 服务端接口要求
确保服务端实现以下接口：
- `POST /api/v1/weixin/login` - 接收code和可选参数
- `POST /api/v1/weixin/bind-phone` - 绑定手机号
- `GET /api/v1/weixin/config` - 检查配置

## ✅ 优势总结

### 1. 简化流程
- ✅ 只使用uni.login获取必需的code
- ✅ 减少用户授权步骤
- ✅ 提高登录成功率

### 2. 兼容性好
- ✅ 避免getUserProfile的版本限制
- ✅ 支持所有微信小程序版本
- ✅ 跨平台兼容性好

### 3. 维护简单
- ✅ 代码逻辑简化
- ✅ 减少API依赖
- ✅ 错误处理集中

### 4. 用户体验
- ✅ 登录流程简洁
- ✅ 减少用户操作
- ✅ 快速完成登录

现在微信小程序登录流程已经简化完成，只使用uni.login获取code，并将其他信息作为可选参数传递给服务端！🎉
