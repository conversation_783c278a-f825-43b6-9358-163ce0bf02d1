<template>
  <view class="level-selection-container">
    <!-- 头部信息 -->
    <view class="header-section">
      <view class="header-content">
        <text class="page-title">选择关卡</text>
        <text class="page-subtitle">选择你想要挑战的关卡开始游戏吧！</text>
      </view>
      
      <!-- 用户进度信息 -->
      <view v-if="userInfo" class="progress-card">
        <view class="progress-item">
          <text class="progress-label">已解锁</text>
          <text class="progress-value">{{ userInfo.unlockedLevels || 0 }}</text>
        </view>
        <view class="progress-item">
          <text class="progress-label">已完成</text>
          <text class="progress-value">{{ userInfo.completedLevelIds?.length || 0 }}</text>
        </view>
        <view class="progress-item">
          <text class="progress-label">总关卡</text>
          <text class="progress-value">{{ levels.length }}</text>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="levelState.isLoading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载关卡...</text>
    </view>

    <!-- 错误状态 -->
    <view v-else-if="levelState.error" class="error-container">
      <view class="error-icon">⚠️</view>
      <text class="error-title">加载失败</text>
      <text class="error-text">{{ levelState.error }}</text>
      <button class="retry-btn" @click="loadLevels">
        <text class="retry-text">重试</text>
      </button>
    </view>

    <!-- 关卡列表 -->
    <view v-else class="levels-container">
      <view class="levels-grid">
        <view
          v-for="level in levels"
          :key="level.id"
          class="level-card"
          :class="{
            'level-locked': level.locked,
            'level-completed': level.completed
          }"
          @click="selectLevel(level)"
        >
          <!-- 关卡编号 -->
          <view class="level-number">{{ level.levelNumber }}</view>
          
          <!-- 关卡信息 -->
          <view class="level-info">
            <text class="level-name">{{ level.name }}</text>
            <text class="level-desc">{{ level.description }}</text>
          </view>
          
          <!-- 关卡状态 -->
          <view class="level-status">
            <!-- 未解锁状态 -->
            <view v-if="level.locked" class="status-badge locked">
              <text class="status-text">🔒 未解锁</text>
            </view>
            <!-- 已完成状态 -->
            <view v-else-if="level.completed" class="status-badge completed">
              <text class="status-text">✅ 已完成</text>
            </view>
            <!-- 可开始状态 -->
            <view v-else class="status-badge available">
              <text class="status-text">▶️ 开始</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- VIP 升级提示 -->
    <view v-if="!dailyStatus?.isVip && hasLockedLevels" class="vip-promotion">
      <view class="vip-card">
        <view class="vip-header">
          <text class="vip-title">🎯 升级VIP会员</text>
          <text class="vip-subtitle">解锁所有关卡，畅享无限挑战</text>
        </view>
        <button class="vip-btn" @click="showVipPackages">
          <text class="vip-btn-text">立即升级</text>
        </button>
      </view>
    </view>

    <!-- 底部返回按钮 -->
    <view class="bottom-actions">
      <button class="back-btn" @click="goBack">
        <text class="back-text">返回首页</text>
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue'
import weixinApi from '../../api/weixin'
import { audioManager } from '../../utils/audio'
import { withLoading, createLoadingState } from '../../api/utils'
import type { LevelInfo, UserInfo, levelState, DailyStatusResponse } from '../../api/types'

// 状态管理
const levelState = reactive<levelState>({
  level: [],
  currentLevel: null,
  ...createLoadingState()
})

const userInfo = ref<UserInfo | null>(null)
const dailyStatus = ref<DailyStatusResponse | null>(null)

// 计算属性：格式化的关卡列表
const levels = computed(() => {
  return levelState.level.map((level, index) => ({
    id: level.id,
    levelNumber: String(index + 1).padStart(2, '0'),
    name: level.name,
    description: level.description,
    library: getLibraryForLevel(level),
    locked: !level.isUnlocked,
    completed: level.isCompleted,
    difficulty: level.difficulty
  }))
})

// 计算属性：是否有锁定的关卡
const hasLockedLevels = computed(() => {
  return levels.value.some(level => level.locked)
})

// 词库数据（与首页保持一致）
const libraries = [
  {
    name: '基础词汇',
    words: [
      { english: 'apple', chinese: '苹果' },
      { english: 'book', chinese: '书' },
      { english: 'cat', chinese: '猫' },
      { english: 'dog', chinese: '狗' },
      { english: 'egg', chinese: '鸡蛋' },
      { english: 'fish', chinese: '鱼' },
      { english: 'girl', chinese: '女孩' },
      { english: 'hat', chinese: '帽子' }
    ]
  },
  {
    name: '动物世界',
    words: [
      { english: 'elephant', chinese: '大象' },
      { english: 'tiger', chinese: '老虎' },
      { english: 'lion', chinese: '狮子' },
      { english: 'monkey', chinese: '猴子' },
      { english: 'rabbit', chinese: '兔子' },
      { english: 'bird', chinese: '鸟' },
      { english: 'horse', chinese: '马' },
      { english: 'sheep', chinese: '羊' }
    ]
  },
  {
    name: '颜色彩虹',
    words: [
      { english: 'red', chinese: '红色' },
      { english: 'blue', chinese: '蓝色' },
      { english: 'green', chinese: '绿色' },
      { english: 'yellow', chinese: '黄色' },
      { english: 'purple', chinese: '紫色' },
      { english: 'orange', chinese: '橙色' },
      { english: 'black', chinese: '黑色' },
      { english: 'white', chinese: '白色' }
    ]
  },
  {
    name: '数字王国',
    words: [
      { english: 'one', chinese: '一' },
      { english: 'two', chinese: '二' },
      { english: 'three', chinese: '三' },
      { english: 'four', chinese: '四' },
      { english: 'five', chinese: '五' },
      { english: 'six', chinese: '六' },
      { english: 'seven', chinese: '七' },
      { english: 'eight', chinese: '八' }
    ]
  }
]

/**
 * 页面初始化
 */
onMounted(async () => {
  await initializePage()
})

/**
 * 初始化页面数据
 */
const initializePage = async () => {
  await withLoading(levelState, async () => {
    // 1. 加载用户信息
    await loadUserInfo()
    
    // 2. 加载关卡列表
    await loadLevels()
    
    // 3. 加载每日状态
    await loadDailyStatus()
  }, {
    errorMessage: '页面加载失败，请重试'
  })
}

/**
 * 加载用户信息
 */
const loadUserInfo = async () => {
  try {
    const info = await weixinApi.getUserInfo()
    userInfo.value = info
    console.log('用户信息加载成功:', info)
  } catch (error) {
    console.error('加载用户信息失败:', error)
    // 用户信息加载失败不影响关卡显示
  }
}

/**
 * 加载关卡列表
 */
const loadLevels = async () => {
  try {
    const apiLevels = await weixinApi.getLevels()
    levelState.level = apiLevels
    console.log('关卡列表加载成功:', apiLevels)
  } catch (error) {
    console.error('加载关卡列表失败:', error)

    // 如果API加载失败，使用本地备用数据
    console.log('使用本地备用关卡数据')
    levelState.level = createFallbackLevels()
  }
}

/**
 * 加载每日状态
 */
const loadDailyStatus = async () => {
  try {
    const status = await weixinApi.getDailyStatus()
    dailyStatus.value = status
    console.log('每日状态加载成功:', status)
  } catch (error) {
    console.error('加载每日状态失败:', error)
    // 每日状态加载失败不影响关卡显示
  }
}

/**
 * 创建备用关卡数据
 */
const createFallbackLevels = (): LevelInfo[] => {
  return [
    {
      id: 'fallback-level-1',
      name: '第1关 - 基础词汇',
      difficulty: 1,
      description: '小学基础词汇练习',
      isUnlocked: true,
      isCompleted: false,
      createdAt: new Date().toISOString()
    },
    {
      id: 'fallback-level-2',
      name: '第2关 - 动物世界',
      difficulty: 2,
      description: '认识可爱的动物',
      isUnlocked: true,
      isCompleted: false,
      createdAt: new Date().toISOString()
    },
    {
      id: 'fallback-level-3',
      name: '第3关 - 颜色彩虹',
      difficulty: 3,
      description: '学习各种颜色',
      isUnlocked: false,
      isCompleted: false,
      createdAt: new Date().toISOString()
    },
    {
      id: 'fallback-level-4',
      name: '第4关 - 数字王国',
      difficulty: 4,
      description: '掌握数字表达',
      isUnlocked: false,
      isCompleted: false,
      createdAt: new Date().toISOString()
    }
  ]
}

/**
 * 根据关卡获取对应的词库
 */
const getLibraryForLevel = (level: LevelInfo) => {
  const difficultyIndex = (level.difficulty - 1) % libraries.length
  return libraries[difficultyIndex]
}

/**
 * 选择关卡
 */
const selectLevel = async (level: any) => {
  if (level.locked) {
    // 播放失败音效
    audioManager.playSoundEffect('fail')
    
    // 检查是否可以解锁
    await checkCanUnlock(level)
    return
  }

  // 播放点击音效
  audioManager.playSoundEffect('click')

  console.log('Selected level:', level)

  // 将选择的关卡信息存储到本地
  uni.setStorageSync('selectedLibrary', JSON.stringify(level.library))
  uni.setStorageSync('selectedLevel', JSON.stringify(level))

  // 显示选择提示
  const actionText = level.completed ? '重新挑战' : '进入关卡'
  
  uni.showModal({
    title: actionText,
    content: `${level.name}\n${level.description}`,
    confirmText: '开始游戏',
    cancelText: '取消',
    success: (res) => {
      if (res.confirm) {
        // 跳转到游戏页面
        uni.navigateTo({
          url: '/pages/game/index'
        })
      }
    }
  })
}

/**
 * 检查是否可以解锁关卡
 */
const checkCanUnlock = async (level: any) => {
  try {
    // 刷新每日状态
    await loadDailyStatus()
    
    const status = dailyStatus.value
    if (!status) {
      uni.showToast({
        title: '该关卡尚未解锁',
        icon: 'none',
        duration: 1500
      })
      return
    }

    // VIP用户无限制
    if (status.isVip) {
      uni.showToast({
        title: '该关卡尚未解锁',
        icon: 'none',
        duration: 1500
      })
      return
    }

    // 检查解锁次数
    if (status.remainingUnlocks > 0) {
      uni.showModal({
        title: '解锁关卡',
        content: `是否使用1次解锁机会来解锁这个关卡？\n剩余解锁次数：${status.remainingUnlocks}`,
        confirmText: '解锁',
        cancelText: '取消',
        success: async (res) => {
          if (res.confirm) {
            // TODO: 调用解锁关卡的API
            uni.showToast({
              title: '关卡解锁成功！',
              icon: 'success'
            })
            
            // 刷新关卡列表
            await loadLevels()
          }
        }
      })
    } else {
      // 没有解锁次数，提示升级VIP或分享获取
      uni.showModal({
        title: '解锁次数不足',
        content: '今日解锁次数已用完\n• 升级VIP可无限解锁\n• 分享游戏可获得额外解锁机会',
        confirmText: '升级VIP',
        cancelText: '分享获取',
        success: (res) => {
          if (res.confirm) {
            showVipPackages()
          } else {
            // TODO: 触发分享功能
            uni.showToast({
              title: '请分享游戏给好友',
              icon: 'none'
            })
          }
        }
      })
    }
  } catch (error) {
    console.error('检查解锁状态失败:', error)
    uni.showToast({
      title: '该关卡尚未解锁',
      icon: 'none',
      duration: 1500
    })
  }
}

/**
 * 显示VIP套餐
 */
const showVipPackages = () => {
  // TODO: 实现VIP套餐显示逻辑
  uni.showToast({
    title: 'VIP功能开发中',
    icon: 'none'
  })
}

/**
 * 返回首页
 */
const goBack = () => {
  // 播放点击音效
  audioManager.playSoundEffect('click')
  
  uni.navigateBack({
    delta: 1
  })
}
</script>

<style lang="scss" scoped>
.level-selection-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea, #764ba2);
  padding: 40rpx 20rpx;
}

.header-section {
  margin-bottom: 40rpx;
}

.header-content {
  text-align: center;
  margin-bottom: 32rpx;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  line-height: 1.4;
}

.progress-card {
  display: flex;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20rpx;
  padding: 24rpx;
  backdrop-filter: blur(10rpx);
}

.progress-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.progress-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
}

.progress-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 32rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.error-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 16rpx;
}

.error-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.retry-btn {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s;
}

.retry-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.retry-text {
  color: inherit;
}

.levels-container {
  margin-bottom: 40rpx;
}

.levels-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.level-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.level-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.15);
}

.level-card.level-locked {
  opacity: 0.7;
}

.level-number {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  flex-shrink: 0;
}

.level-locked .level-number {
  background: #bbb;
  box-shadow: 0 4rpx 12rpx rgba(187, 187, 187, 0.3);
}

.level-completed .level-number {
  background: linear-gradient(135deg, #00b894, #00a085);
  box-shadow: 0 4rpx 12rpx rgba(0, 184, 148, 0.3);
}

.level-info {
  flex: 1;
  min-width: 0;
}

.level-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 8rpx;
  line-height: 1.2;
}

.level-desc {
  display: block;
  font-size: 24rpx;
  color: #718096;
  line-height: 1.3;
}

.level-locked .level-name,
.level-locked .level-desc {
  color: #999;
}

.level-status {
  flex-shrink: 0;
}

.status-badge {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  text-align: center;
}

.status-badge.locked {
  background: #f0f0f0;
}

.status-badge.completed {
  background: #d1f2eb;
}

.status-badge.available {
  background: #e3f2fd;
}

.status-text {
  font-size: 22rpx;
  font-weight: 500;
}

.locked .status-text {
  color: #999;
}

.completed .status-text {
  color: #00b894;
}

.available .status-text {
  color: #667eea;
}

.vip-promotion {
  margin-bottom: 40rpx;
}

.vip-card {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20rpx;
  padding: 32rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
}

.vip-header {
  text-align: center;
  margin-bottom: 24rpx;
}

.vip-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 8rpx;
}

.vip-subtitle {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

.vip-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #ffd700, #ffb347);
  border-radius: 40rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  box-shadow: 0 8rpx 20rpx rgba(255, 215, 0, 0.3);
  transition: all 0.2s;
}

.vip-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.vip-btn-text {
  color: inherit;
}

.bottom-actions {
  display: flex;
  justify-content: center;
  padding-top: 20rpx;
}

.back-btn {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s;
}

.back-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.back-text {
  color: inherit;
}
</style>
