# 音频管理器升级文档

## 🎯 升级概述

根据 uniapp 官方文档 https://uniapp.dcloud.net.cn/api/media/audio-context.html，已将音频管理功能从旧的 `uni.createAudioContext()` 升级到新的 `uni.createInnerAudioContext()` API。

## 📋 主要改进

### 1. **API 升级**

#### 旧版本 API
```typescript
// 旧版本使用 uni.createAudioContext()
const audioContext = uni.createAudioContext('backgroundAudio')
audioContext.setSrc(src)
audioContext.play()
```

#### 新版本 API
```typescript
// 新版本使用 uni.createInnerAudioContext()
const innerAudioContext = uni.createInnerAudioContext()
innerAudioContext.src = src
innerAudioContext.loop = true
innerAudioContext.autoplay = false
innerAudioContext.volume = 0.5
innerAudioContext.obeyMuteSwitch = true
innerAudioContext.play()
```

### 2. **音频属性配置**

#### 完整的音频属性设置
```typescript
// 设置音频属性
innerAudioContext.src = src                    // 音频源
innerAudioContext.loop = true                  // 循环播放
innerAudioContext.autoplay = false             // 不自动播放
innerAudioContext.volume = 0.5                 // 音量 50%
innerAudioContext.obeyMuteSwitch = true        // 遵循系统静音开关
```

#### 支持的音频格式
- **iOS**: mp3, aac, m4a, wav, aiff, caf
- **Android**: mp3, aac, mp4, wav, ogg, flac, ape, amr, wma

### 3. **事件监听系统**

#### 完整的事件监听
```typescript
private setupAudioEventListeners(audioContext: UniApp.InnerAudioContext): void {
  // 音频可以播放时
  audioContext.onCanplay(() => {
    console.log('音频可以播放:', currentAudioSrc)
  })

  // 音频开始播放
  audioContext.onPlay(() => {
    isPlaying = true
    isPaused = false
    console.log('背景音乐开始播放:', currentAudioSrc)
  })

  // 音频暂停
  audioContext.onPause(() => {
    isPaused = true
    console.log('背景音乐暂停:', currentAudioSrc)
  })

  // 音频停止
  audioContext.onStop(() => {
    isPlaying = false
    isPaused = false
    console.log('背景音乐停止:', currentAudioSrc)
  })

  // 音频播放结束
  audioContext.onEnded(() => {
    isPlaying = false
    isPaused = false
    console.log('背景音乐播放结束:', currentAudioSrc)
  })

  // 音频播放错误
  audioContext.onError((error) => {
    const errorCode = error.errCode || error.code || 'unknown'
    const errorMsg = AUDIO_ERROR_CODES[errorCode] || '未知错误'
    console.error('背景音乐播放错误:', {
      code: errorCode,
      message: errorMsg,
      src: currentAudioSrc,
      error
    })
    
    isPlaying = false
    isPaused = false

    // 处理特定错误
    if (errorCode === -99) {
      console.warn('检测到音频实例冲突，尝试重新创建')
      setTimeout(() => {
        this.destroyInnerAudioContext()
      }, 100)
    }
  })

  // 音频加载中
  audioContext.onWaiting(() => {
    console.log('音频加载中:', currentAudioSrc)
  })

  // 音频进度更新
  audioContext.onTimeUpdate(() => {
    // 可以在这里处理播放进度
  })
}
```

### 4. **错误处理机制**

#### 错误码映射
```typescript
const AUDIO_ERROR_CODES: Record<string, string> = {
  '10001': '系统错误',
  '10002': '网络错误',
  '10003': '文件错误',
  '10004': '格式错误',
  '-1': '未知错误',
  '-99': '音频实例冲突错误'
}
```

#### 特殊错误处理
- **-99 错误**：音频实例冲突，自动销毁并重新创建
- **网络错误**：记录详细错误信息，便于调试
- **文件错误**：检查音频文件路径和格式

### 5. **音效管理优化**

#### 音效实例缓存
```typescript
// 音效实例缓存
const soundEffectInstances: Map<string, UniApp.InnerAudioContext> = new Map()

playSoundEffect(effectType: keyof typeof SOUND_EFFECTS): void {
  if (!this.settings.soundEffects) {
    console.log('音效已关闭，跳过播放')
    return
  }

  try {
    const src = SOUND_EFFECTS[effectType]
    
    // 检查是否已有该音效的实例
    let effectContext = soundEffectInstances.get(effectType)
    
    if (!effectContext) {
      // 创建新的音效实例
      effectContext = uni.createInnerAudioContext()
      effectContext.src = src
      effectContext.volume = 0.6
      effectContext.loop = false
      effectContext.obeyMuteSwitch = true

      // 设置事件监听
      effectContext.onPlay(() => {
        console.log('音效播放:', effectType)
      })
      
      effectContext.onError((error) => {
        console.error('音效播放失败:', effectType, error)
        soundEffectInstances.delete(effectType)
      })
      
      effectContext.onEnded(() => {
        console.log('音效播放结束:', effectType)
      })

      // 缓存实例
      soundEffectInstances.set(effectType, effectContext)
    }
    
    // 播放音效
    effectContext.play()
  } catch (error) {
    console.error('播放音效失败:', effectType, error)
  }
}
```

### 6. **全局配置集成**

#### 支持自定义音频URL
```typescript
/**
 * 播放背景音乐
 * @param musicType 音乐类型 ('main', 'game', 'menu')
 * @param customUrl 自定义音频URL（来自全局配置）
 */
playBackgroundMusic(musicType: string = 'main', customUrl?: string): void {
  if (!this.settings.backgroundMusic) {
    console.log('背景音乐已关闭，跳过播放')
    return
  }

  try {
    // 使用自定义URL或默认音乐
    const src = customUrl || (DEFAULT_BACKGROUND_MUSIC as any)[musicType] || DEFAULT_BACKGROUND_MUSIC.main
    
    // 如果当前正在播放相同音乐，不重复播放
    if (isPlaying && currentAudioSrc === src) {
      console.log('相同背景音乐正在播放中:', musicType)
      return
    }

    // 如果是暂停状态且是相同音乐，恢复播放
    if (isPaused && currentAudioSrc === src && innerAudioContext) {
      innerAudioContext.play()
      currentMusicType = musicType
      return
    }

    // 创建新的音频上下文并播放
    const context = this.createInnerAudioContext(src)
    context.play()
    currentMusicType = musicType
    
    console.log('开始播放背景音乐:', musicType, src)
  } catch (error) {
    console.error('播放背景音乐失败:', error)
  }
}
```

### 7. **生命周期管理**

#### 页面生命周期集成
```typescript
/**
 * 页面显示时恢复音频
 */
onPageShow(): void {
  if (this.settings.backgroundMusic && isPaused) {
    this.resumeBackgroundMusic()
  }
}

/**
 * 页面隐藏时暂停音频
 */
onPageHide(): void {
  if (isPlaying) {
    this.pauseBackgroundMusic()
  }
}
```

#### 资源销毁
```typescript
/**
 * 销毁音频资源
 */
destroy(): void {
  // 销毁背景音乐
  this.destroyInnerAudioContext()
  
  // 销毁所有音效实例
  soundEffectInstances.forEach((context, key) => {
    try {
      context.destroy()
    } catch (error) {
      console.error('销毁音效实例失败:', key, error)
    }
  })
  soundEffectInstances.clear()
  
  console.log('音频资源已销毁')
}
```

## 🎮 使用方式

### 1. **基本使用**
```typescript
import { audioManager } from '@/utils/audio'

// 播放背景音乐
audioManager.playBackgroundMusic('main')

// 播放音效
audioManager.playSoundEffect('click')

// 触发震动
audioManager.vibrate('short')
```

### 2. **全局配置集成**
```typescript
// 使用全局配置的音频URL
const musicUrl = getBackgroundMusicUrl('main')
audioManager.playBackgroundMusic('main', musicUrl)
```

### 3. **设置管理**
```typescript
// 更新音频设置
audioManager.updateSettings({
  backgroundMusic: false,
  soundEffects: true,
  vibration: true
})

// 获取当前设置
const settings = audioManager.getSettings()
```

### 4. **页面生命周期**
```typescript
// 页面显示时
onShow(() => {
  audioManager.onPageShow()
})

// 页面隐藏时
onHide(() => {
  audioManager.onPageHide()
})
```

## ✅ 升级优势

1. **✅ API 现代化**：使用最新的 uniapp 音频 API
2. **✅ 更好的兼容性**：支持更多平台和音频格式
3. **✅ 完善的错误处理**：详细的错误码映射和处理机制
4. **✅ 音效优化**：音效实例缓存，避免重复创建
5. **✅ 全局配置支持**：支持动态音频URL配置
6. **✅ 生命周期管理**：完善的页面生命周期集成
7. **✅ 资源管理**：自动资源清理，避免内存泄漏
8. **✅ 类型安全**：完整的 TypeScript 类型定义

## 🎉 总结

音频管理器已成功升级到使用 `uni.createInnerAudioContext()` API，提供了：

- **更稳定的音频播放**：使用官方推荐的最新API
- **更好的错误处理**：完善的错误码映射和恢复机制
- **更高的性能**：音效实例缓存和资源优化
- **更强的扩展性**：支持全局配置和自定义音频URL
- **更完善的生命周期**：自动处理页面切换和资源清理

现在音频功能更加稳定可靠，为用户提供更好的游戏体验！🎉
