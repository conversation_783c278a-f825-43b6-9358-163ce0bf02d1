# 微信小程序分享功能集成文档

## 🎯 概述

根据uniapp官方文档（https://uniapp.dcloud.net.cn/api/plugins/share.html#onshareappmessage）实现了完整的微信小程序分享功能，通过 `/api/v1/weixin/share-config` 接口动态获取分享信息。

## 📋 实现的功能

### 1. 分享API接口

#### 获取分享配置接口
```typescript
// GET /api/v1/weixin/share-config
interface ShareConfigRequest {
  page?: string      // 页面路径
  levelId?: string   // 关卡ID
  userId?: string    // 用户ID
}

interface ShareConfigResponse {
  title: string      // 分享标题
  path: string       // 分享路径
  imageUrl: string   // 分享图片
  desc?: string      // 分享描述
  summary?: string   // 分享摘要
  query?: string     // 查询参数
}
```

### 2. 分享工具类

#### ShareUtils 核心功能
- **获取分享配置**：动态从服务器获取分享信息
- **小程序分享处理**：处理 onShareAppMessage 事件
- **App端微信分享**：支持分享到微信好友/朋友圈
- **系统分享**：调用系统分享菜单
- **分享菜单控制**：显示/隐藏分享按钮

### 3. 页面分享集成

#### 首页分享
```typescript
// 首页分享处理
const onShareAppMessage = async (options: any) => {
  return await shareUtils.handleShareAppMessage(options, {
    page: 'pages/index/index',
    userId: userInfo.value?.id
  })
}
```

#### 游戏页面分享
```typescript
// 游戏页面分享处理
const onShareAppMessage = async (options: any) => {
  const shareParams = {
    page: 'pages/game/index',
    levelId: currentLevelDetail.value?.id || selectedLevelData.value?.id,
    userId: userInfo.value?.id
  }
  
  return await shareUtils.handleShareAppMessage(options, shareParams)
}
```

## 🔧 技术实现

### 1. 分享配置获取

```typescript
async getShareConfig(params?: ShareConfigRequest): Promise<ShareConfigResponse> {
  try {
    let url = getWeixinApiUrl('/share-config')
    
    // 构建查询参数
    if (params) {
      const queryParams = new URLSearchParams()
      if (params.page) queryParams.append('page', params.page)
      if (params.levelId) queryParams.append('levelId', params.levelId)
      if (params.userId) queryParams.append('userId', params.userId)
      
      const queryString = queryParams.toString()
      if (queryString) {
        url += `?${queryString}`
      }
    }
    
    const response = await httpClient.get<ShareConfigResponse>(url)
    return response
  } catch (error) {
    // 返回默认分享配置
    return {
      title: '英语单词游戏 - 挑战你的词汇量！',
      path: '/pages/index/index',
      imageUrl: '/static/share-logo.png',
      desc: '快来挑战英语单词游戏，提升你的词汇量！',
      summary: '和朋友一起学英语，看谁的词汇量更丰富！'
    }
  }
}
```

### 2. 小程序分享处理

```typescript
static async handleShareAppMessage(options: {
  from?: string
  target?: any
  webViewUrl?: string
}, params?: ShareConfigRequest): Promise<any> {
  try {
    // 获取分享配置
    const shareConfig = await this.getShareConfig(params)
    
    // 构建分享对象
    const shareData = {
      title: shareConfig.title,
      path: shareConfig.path,
      imageUrl: shareConfig.imageUrl
    }

    // 如果有查询参数，添加到path中
    if (shareConfig.query) {
      const separator = shareData.path.includes('?') ? '&' : '?'
      shareData.path += separator + shareConfig.query
    }

    return shareData
  } catch (error) {
    // 返回默认分享数据
    return {
      title: '英语单词游戏',
      path: '/pages/index/index',
      imageUrl: '/static/share-logo.png'
    }
  }
}
```

### 3. App端微信分享

```typescript
static async shareToWeixin(options: {
  scene: 'WXSceneSession' | 'WXSceneTimeline' | 'WXSceneFavorite'
  type?: number
  params?: ShareConfigRequest
}): Promise<void> {
  try {
    // 获取分享配置
    const shareConfig = await this.getShareConfig(options.params)
    
    // 构建分享参数
    const shareOptions = {
      provider: 'weixin',
      scene: options.scene,
      type: options.type || 0, // 默认图文分享
      title: shareConfig.title,
      summary: shareConfig.desc || shareConfig.summary,
      href: shareConfig.path,
      imageUrl: shareConfig.imageUrl,
      success: (res: any) => {
        uni.showToast({ title: '分享成功', icon: 'success' })
      },
      fail: (err: any) => {
        uni.showToast({ title: '分享失败', icon: 'none' })
      }
    }

    // 调用分享
    uni.share(shareOptions)
  } catch (error) {
    uni.showToast({ title: '分享失败', icon: 'none' })
  }
}
```

### 4. 系统分享

```typescript
static async shareWithSystem(params?: ShareConfigRequest): Promise<void> {
  try {
    // 获取分享配置
    const shareConfig = await this.getShareConfig(params)
    
    // 构建分享参数
    const shareOptions = {
      type: 'text',
      summary: `${shareConfig.title}\n${shareConfig.desc || shareConfig.summary}`,
      href: shareConfig.path,
      success: () => {
        console.log('系统分享成功')
      },
      fail: (err: any) => {
        console.error('系统分享失败:', err)
      }
    }

    // 调用系统分享
    uni.shareWithSystem(shareOptions)
  } catch (error) {
    uni.showToast({ title: '分享失败', icon: 'none' })
  }
}
```

## 🎮 使用方法

### 1. 小程序分享

#### 自动分享（右上角菜单）
- 用户点击小程序右上角的分享按钮
- 自动调用页面的 `onShareAppMessage` 方法
- 动态获取分享配置并返回分享数据

#### 自定义分享按钮
```vue
<template>
  <button open-type="share" class="share-btn">
    分享给好友
  </button>
</template>
```

### 2. App端分享

```typescript
// 分享到微信好友
await shareUtils.shareToWeixin({
  scene: 'WXSceneSession',
  type: 0,
  params: { page: 'pages/index/index', userId: '123' }
})

// 分享到微信朋友圈
await shareUtils.shareToWeixin({
  scene: 'WXSceneTimeline',
  type: 0,
  params: { page: 'pages/index/index', userId: '123' }
})
```

### 3. 系统分享

```typescript
// 调用系统分享菜单
await shareUtils.shareWithSystem({
  page: 'pages/index/index',
  userId: '123'
})
```

## 🛠 调试功能

### 调试页面测试
- **获取分享配置**：测试分享配置API
- **测试微信分享**：测试App端微信分享功能
- **测试系统分享**：测试系统分享功能

### 使用方法
1. 访问首页右上角"API调试"按钮
2. 设置测试参数（关卡ID、用户ID）
3. 点击相应的分享测试按钮

## 📱 平台支持

### 微信小程序
- ✅ **onShareAppMessage**：处理分享事件
- ✅ **自定义分享按钮**：`<button open-type="share">`
- ✅ **分享菜单控制**：显示/隐藏分享按钮

### App端
- ✅ **uni.share**：分享到微信、QQ、微博
- ✅ **uni.shareWithSystem**：系统分享菜单
- ✅ **多种分享类型**：图文、纯文字、纯图片

### H5端
- ⚠️ **有限支持**：浏览器自带分享或微信JS-SDK

## 🔄 数据流

```
用户触发分享
    ↓
调用 shareUtils.handleShareAppMessage()
    ↓
请求 /api/v1/weixin/share-config 获取配置
    ↓
构建分享数据对象
    ↓
返回给小程序分享系统
    ↓
用户选择分享目标
    ↓
分享完成
```

## 🎯 分享配置示例

### 服务端返回示例
```json
{
  "title": "英语单词游戏 - 第3关挑战",
  "path": "/pages/game/index?levelId=level-3&from=share",
  "imageUrl": "https://example.com/share-images/level-3.png",
  "desc": "我正在挑战第3关，快来一起学英语！",
  "summary": "挑战英语单词游戏，提升词汇量",
  "query": "levelId=level-3&from=share&userId=123"
}
```

### 默认配置
```json
{
  "title": "英语单词游戏 - 挑战你的词汇量！",
  "path": "/pages/index/index",
  "imageUrl": "/static/share-logo.png",
  "desc": "快来挑战英语单词游戏，提升你的词汇量！",
  "summary": "和朋友一起学英语，看谁的词汇量更丰富！"
}
```

## ✅ 功能清单

- ✅ **动态分享配置**：通过API获取个性化分享内容
- ✅ **多页面支持**：首页和游戏页面都支持分享
- ✅ **多平台兼容**：小程序、App、H5环境
- ✅ **错误处理**：API失败时使用默认配置
- ✅ **调试工具**：完整的分享功能测试
- ✅ **用户体验**：流畅的分享流程和反馈

## 🎉 总结

现在项目已经完全集成了微信小程序分享功能：

1. **标准化实现**：完全按照uniapp官方文档实现
2. **动态配置**：通过API动态获取分享信息
3. **多场景支持**：支持不同页面和关卡的个性化分享
4. **完整测试**：提供丰富的调试和测试功能
5. **用户友好**：优雅的错误处理和用户反馈

用户现在可以轻松分享游戏内容给好友，提升游戏的传播效果和用户参与度！🎉
