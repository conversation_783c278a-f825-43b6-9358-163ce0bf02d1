# 每日解锁限制功能实现文档

## 🎯 概述

根据 `WEIXIN_DAILY_LIMIT.md` 文档中的接口规范，已完成小程序端的每日解锁限制功能实现。该功能包括每日解锁次数限制、分享奖励机制和VIP特权系统。

## 📋 已实现的功能

### 1. API接口类型定义更新

#### UserInfo接口扩展
```typescript
export interface UserInfo {
  // 原有字段...
  
  // 每日解锁限制相关字段
  isVip: boolean                // VIP状态
  dailyUnlockLimit: number      // 每日解锁限制（默认15）
  dailyUnlockCount: number      // 当日解锁次数
  dailyShared: boolean          // 当日是否已分享
  lastPlayDate: string          // 最后游戏日期（YYYY-MM-DD）
  totalShares: number           // 总分享次数
}
```

#### 新增接口类型
```typescript
// 每日状态响应
export interface DailyStatusResponse {
  id: string
  dailyUnlockCount: number
  dailyUnlockLimit: number
  remainingUnlocks: number
  dailyShared: boolean
  isVip: boolean
  lastPlayDate: string
  totalShares: number
  canUnlock: boolean
}

// 分享接口请求参数
export interface ShareRequest {
  openid: string
}

// 分享接口响应
export interface ShareResponse {
  status: 'success' | 'already_shared' | 'error'
  message: string
  userId?: string
  dailyUnlockCount?: number
  dailyUnlockLimit?: number
  remainingUnlocks?: number
  isVip?: boolean
  totalShares?: number
}

// 完成关卡响应
export interface CompleteLevelResponse {
  message: string
  userId: string
  levelId: string
  unlockedLevelss: number
  totalCompletions: number
  hasUnlockedNewLevel: boolean
  dailyUnlockCount: number
  dailyUnlockLimit: number
  remainingUnlocks: number
  isVip: boolean
}
```

### 2. 微信API服务扩展

#### 新增API方法
```typescript
/**
 * 获取每日状态
 * GET /api/v1/weixin/daily-status?openid={openid}
 */
async getDailyStatus(customOpenid?: string): Promise<DailyStatusResponse>

/**
 * 用户分享（新版本）
 * POST /api/v1/weixin/share
 */
async shareForReward(customOpenid?: string): Promise<ShareResponse>

/**
 * 用户完成关卡（新版本）
 * POST /api/v1/weixin/level/complete
 */
async completeLevel(userId: string, levelId: string): Promise<CompleteLevelResponse>
```

### 3. 首页功能实现

#### 每日状态显示
- **状态卡片**：显示当日解锁进度和剩余次数
- **VIP标识**：VIP用户显示特殊标识
- **分享提示**：解锁次数不足时提示分享获得额外机会

```vue
<!-- 每日状态显示 -->
<view v-if="dailyStatus && !dailyStatus.isVip" class="daily-status">
  <text class="daily-text">
    今日解锁: {{ dailyStatus.dailyUnlockCount }}/{{ dailyStatus.dailyUnlockLimit }}
    <text v-if="dailyStatus.remainingUnlocks > 0" class="remaining">
      (剩余 {{ dailyStatus.remainingUnlocks }} 次)
    </text>
    <text v-else class="no-remaining">
      (已用完)
    </text>
  </text>
  <text v-if="!dailyStatus.dailyShared && dailyStatus.remainingUnlocks <= 3" class="share-tip">
    💡 分享可获得5次额外机会
  </text>
</view>
<view v-if="dailyStatus && dailyStatus.isVip" class="vip-status">
  <text class="vip-text">👑 VIP用户 - 无限解锁</text>
</view>
```

#### 解锁限制检查
```typescript
/**
 * 检查是否可以解锁关卡
 */
const checkCanUnlock = async (level: any): Promise<void> => {
  try {
    // 刷新每日状态
    await loadDailyStatus()
    
    if (!dailyStatus.value) {
      uni.showToast({
        title: '无法获取解锁状态',
        icon: 'none',
        duration: 1500
      })
      return
    }

    const status = dailyStatus.value

    // VIP用户无限制
    if (status.isVip) {
      uni.showToast({
        title: '该关卡尚未解锁',
        icon: 'none',
        duration: 1500
      })
      return
    }

    // 检查每日解锁次数
    if (status.remainingUnlocks <= 0) {
      // 解锁次数不足
      const message = status.dailyShared 
        ? '今日解锁次数已用完，请明天再来！'
        : '今日解锁次数已用完，分享游戏可获得5次额外机会！'
      
      uni.showModal({
        title: '解锁次数不足',
        content: message,
        showCancel: !status.dailyShared,
        cancelText: '明天再来',
        confirmText: status.dailyShared ? '知道了' : '立即分享',
        success: (res) => {
          if (res.confirm && !status.dailyShared) {
            triggerShare()
          }
        }
      })
    } else {
      // 有解锁次数，但关卡被锁定（可能是前置关卡未完成）
      uni.showToast({
        title: '该关卡尚未解锁',
        icon: 'none',
        duration: 1500
      })
    }
  } catch (error) {
    console.error('检查解锁状态失败:', error)
    uni.showToast({
      title: '该关卡尚未解锁',
      icon: 'none',
      duration: 1500
    })
  }
}
```

#### 分享奖励处理更新
```typescript
/**
 * 处理分享奖励
 */
const handleShareReward = async (options: any) => {
  try {
    // 防重复执行检查...
    
    // 延迟获取奖励，确保分享完成
    setTimeout(async () => {
      try {
        // 使用新的分享API
        const shareResponse = await weixinApi.shareForReward()

        if (shareResponse.status === 'success') {
          // 标记今日已获取分享奖励
          markDailyShareReward(userInfo.value!.id)

          // 刷新每日状态
          await loadDailyStatus()

          // 显示奖励获得提示
          uni.showModal({
            title: '分享奖励',
            content: `${shareResponse.message}今日分享奖励已领取完毕。`,
            showCancel: false,
            confirmText: '太棒了'
          })
        } else if (shareResponse.status === 'already_shared') {
          // 今日已分享过
          markDailyShareReward(userInfo.value!.id)
          
          uni.showToast({
            title: shareResponse.message,
            icon: 'none',
            duration: 2000
          })
        }
      } catch (error) {
        console.error('获取分享奖励失败:', error)
      } finally {
        // 重置处理状态
        isHandlingShareReward = false
      }
    }, 2000)
  } catch (error) {
    console.error('处理分享奖励失败:', error)
    isHandlingShareReward = false
  }
}
```

### 4. 游戏页面功能实现

#### 通关限制检查
```typescript
/**
 * 调用通关关卡接口
 */
const callCompleteLevelAPI = async () => {
  try {
    // 获取关卡ID...
    
    const completeLevelResponse = await weixinApi.completeLevel(userInfo.value.id, levelId)

    // 更新关卡详情的完成状态
    if (currentLevelDetail.value) {
      currentLevelDetail.value.isCompleted = true
    }

    // 检查是否解锁了新关卡
    if (completeLevelResponse.hasUnlockedNewLevel) {
      // 显示解锁新关卡的提示
      uni.showModal({
        title: '恭喜通关！',
        content: `${completeLevelResponse.message}\n已解锁 ${completeLevelResponse.unlockedLevelss} 关！`,
        showCancel: false,
        confirmText: '太棒了'
      })
    } else {
      // 检查每日解锁限制
      if (!completeLevelResponse.isVip && completeLevelResponse.remainingUnlocks <= 0) {
        uni.showModal({
          title: '通关成功',
          content: `${completeLevelResponse.message}\n今日解锁次数已用完，明天再来或分享获得额外机会！`,
          showCancel: true,
          cancelText: '明天再来',
          confirmText: '立即分享',
          success: (res) => {
            if (res.confirm) {
              // 触发分享
              uni.showShareMenu({
                withShareTicket: true
              })
            }
          }
        })
      } else {
        // 显示通关成功提示
        showSuccess('恭喜通关！进度已同步', 2000)
      }
    }

    // 刷新用户信息以获取最新状态
    await refreshUserInfo()
  } catch (error) {
    console.error('调用通关接口失败:', error)
    showError('通关记录失败，但不影响游戏', 2000)
  }
}
```

### 5. 调试功能扩展

#### 新增测试功能
```typescript
// 测试每日状态
const testDailyStatus = async () => {
  const dailyStatus = await weixinApi.getDailyStatus()
  // 显示详细状态信息...
}

// 测试新分享API
const testNewShareAPI = async () => {
  const shareResponse = await weixinApi.shareForReward()
  // 显示分享结果...
}
```

## 🎮 用户体验流程

### 1. 首页体验
```
用户进入首页
    ↓
加载用户信息和每日状态
    ↓
显示解锁进度和剩余次数
    ↓
如果解锁次数不足且未分享 → 提示分享获得额外机会
    ↓
用户点击锁定关卡 → 检查解锁限制 → 引导分享或提示明天再来
```

### 2. 通关体验
```
用户完成关卡
    ↓
调用通关接口
    ↓
检查是否解锁新关卡
    ↓
如果解锁次数用完 → 提示分享获得额外机会
    ↓
如果是VIP用户 → 无限制继续游戏
```

### 3. 分享体验
```
用户触发分享
    ↓
检查今日是否已分享
    ↓
如果未分享 → 调用分享奖励接口 → 获得5次额外解锁机会
    ↓
如果已分享 → 提示今日已获得分享奖励
    ↓
更新每日状态显示
```

## 🔧 技术实现亮点

### 1. **完整的类型安全**
- 所有API接口都有完整的TypeScript类型定义
- 响应数据结构与服务端文档完全一致

### 2. **用户体验优化**
- 实时显示解锁进度和剩余次数
- 智能提示分享获得额外机会
- VIP用户特殊标识和无限制体验

### 3. **防重复执行机制**
- 分享奖励处理的防重复执行
- 本地状态缓存避免重复请求

### 4. **兼容性保证**
- 保留旧版API方法确保向后兼容
- 渐进式升级不影响现有功能

### 5. **完善的调试功能**
- 每日状态查询测试
- 新分享API测试
- 防重复执行测试

## ✅ 功能清单

- ✅ **UserInfo接口扩展**：添加每日解锁限制相关字段
- ✅ **新增API接口类型**：DailyStatusResponse、ShareResponse、CompleteLevelResponse
- ✅ **微信API服务扩展**：getDailyStatus、shareForReward、completeLevel
- ✅ **首页每日状态显示**：解锁进度、剩余次数、VIP标识
- ✅ **首页解锁限制检查**：点击锁定关卡时的限制检查和引导
- ✅ **首页分享奖励更新**：使用新的分享API和状态刷新
- ✅ **游戏页面通关限制**：通关后的解锁限制检查和提示
- ✅ **调试功能扩展**：每日状态测试、新分享API测试
- ✅ **样式优化**：每日状态和VIP状态的美观显示
- ✅ **用户体验优化**：智能提示、引导分享、状态反馈

## 🎉 总结

现在小程序端已完全实现了每日解锁限制功能：

1. **✅ 完整的API集成**：与服务端接口完全对接
2. **✅ 用户友好的界面**：清晰显示解锁状态和限制信息
3. **✅ 智能的引导机制**：适时提示用户分享获得额外机会
4. **✅ VIP特权支持**：VIP用户享受无限制体验
5. **✅ 完善的调试工具**：便于测试和问题排查
6. **✅ 防重复执行保护**：确保系统稳定性

用户现在可以享受到完整的每日解锁限制体验，包括解锁进度显示、分享奖励机制和VIP特权系统！🎉
