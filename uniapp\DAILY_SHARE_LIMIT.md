# 每日分享限制功能文档

## 🎯 概述

为了防止用户重复获取分享奖励，实现了每日分享限制功能。用户每天只能通过分享获得一次奖励，提升奖励的价值感和用户分享的质量。

## 📋 实现的功能

### 1. 每日分享状态检查

#### 检查函数
```typescript
/**
 * 检查今日是否已经获取过分享奖励
 */
const checkDailyShareReward = (userId: string): boolean => {
  try {
    const today = new Date().toDateString() // 获取今日日期字符串，如 "Mon Dec 25 2023"
    const storageKey = `daily_share_reward_${userId}_${today}`
    const hasSharedToday = uni.getStorageSync(storageKey)
    
    console.log(`检查每日分享奖励状态 - 用户: ${userId}, 日期: ${today}, 已分享: ${!!hasSharedToday}`)
    return !!hasSharedToday
  } catch (error) {
    console.error('检查每日分享奖励状态失败:', error)
    return false
  }
}
```

#### 存储键格式
- **格式**：`daily_share_reward_{userId}_{date}`
- **示例**：`daily_share_reward_12345678_Mon Dec 25 2023`
- **说明**：使用用户ID和日期组合，确保每个用户每天独立计算

### 2. 每日分享状态标记

#### 标记函数
```typescript
/**
 * 标记今日已获取分享奖励
 */
const markDailyShareReward = (userId: string): void => {
  try {
    const today = new Date().toDateString()
    const storageKey = `daily_share_reward_${userId}_${today}`
    uni.setStorageSync(storageKey, true)
    
    console.log(`标记每日分享奖励完成 - 用户: ${userId}, 日期: ${today}`)
  } catch (error) {
    console.error('标记每日分享奖励失败:', error)
  }
}
```

#### 标记时机
- **成功获取奖励后**：API返回成功时立即标记
- **服务端已领取**：服务端返回"今日已领取"消息时也标记本地状态

### 3. 首页分享限制集成

#### 修改后的handleShareReward函数
```typescript
const handleShareReward = async (options: any) => {
  try {
    if (!userInfo.value?.id) {
      console.warn('用户信息不存在，无法获取分享奖励')
      return
    }

    // 检查今日是否已经获取过分享奖励
    const hasSharedToday = checkDailyShareReward(userInfo.value.id)
    if (hasSharedToday) {
      console.log('今日已获取过分享奖励，跳过本次请求')
      uni.showToast({
        title: '今日已获得分享奖励',
        icon: 'none',
        duration: 2000
      })
      return
    }

    // 延迟获取奖励，确保分享完成
    setTimeout(async () => {
      try {
        const rewardResponse = await weixinApi.getShareReward({
          openid: userInfo.value!.id,
          shareType: 'app_message',
          page: 'pages/index/index',
          timestamp: Date.now()
        })

        if (rewardResponse.success) {
          // 标记今日已获取分享奖励
          markDailyShareReward(userInfo.value!.id)

          // 显示奖励获得提示
          uni.showModal({
            title: '分享奖励',
            content: `恭喜获得${rewardResponse.reward.description}！今日分享奖励已领取完毕。`,
            showCancel: false,
            confirmText: '太棒了'
          })
        } else {
          // 如果服务端返回今日已领取的消息，也标记本地状态
          if (rewardResponse.message.includes('今日') || rewardResponse.message.includes('已领取')) {
            markDailyShareReward(userInfo.value!.id)
          }
        }
      } catch (error) {
        console.error('获取分享奖励失败:', error)
      }
    }, 2000)
  } catch (error) {
    console.error('处理分享奖励失败:', error)
  }
}
```

### 4. 游戏页面分享限制集成

#### 修改后的handleGameShareReward函数
```typescript
const handleGameShareReward = async (options: any, shareParams: any) => {
  try {
    if (!userInfo.value?.id) {
      console.warn('用户信息不存在，无法获取分享奖励')
      return
    }

    // 检查今日是否已经获取过分享奖励
    const hasSharedToday = checkDailyShareReward(userInfo.value.id)
    if (hasSharedToday) {
      console.log('今日已获取过分享奖励，跳过本次请求')
      uni.showToast({
        title: '今日已获得分享奖励',
        icon: 'none',
        duration: 2000
      })
      return
    }

    // 延迟获取奖励，确保分享完成
    setTimeout(async () => {
      try {
        const rewardResponse = await weixinApi.getShareReward({
          userId: userInfo.value!.id,
          shareType: 'app_message',
          page: shareParams.page,
          levelId: shareParams.levelId,
          timestamp: Date.now()
        })

        if (rewardResponse.success) {
          // 标记今日已获取分享奖励
          markDailyShareReward(userInfo.value!.id)

          // 显示奖励获得提示
          uni.showModal({
            title: '分享奖励',
            content: `恭喜获得${rewardResponse.reward.description}！可以继续挑战更多关卡了！今日分享奖励已领取完毕。`,
            showCancel: false,
            confirmText: '太棒了'
          })
        } else {
          // 如果服务端返回今日已领取的消息，也标记本地状态
          if (rewardResponse.message.includes('今日') || rewardResponse.message.includes('已领取')) {
            markDailyShareReward(userInfo.value!.id)
          }
        }
      } catch (error) {
        console.error('获取游戏分享奖励失败:', error)
      }
    }, 2000)
  } catch (error) {
    console.error('处理游戏分享奖励失败:', error)
  }
}
```

### 5. 分享工具类集成

#### ShareUtils中的每日限制方法
```typescript
/**
 * 检查今日是否已经获取过分享奖励
 */
static checkDailyShareReward(userId: string): boolean {
  try {
    const today = new Date().toDateString()
    const storageKey = `daily_share_reward_${userId}_${today}`
    const hasSharedToday = uni.getStorageSync(storageKey)
    
    return !!hasSharedToday
  } catch (error) {
    console.error('检查每日分享奖励状态失败:', error)
    return false
  }
}

/**
 * 标记今日已获取分享奖励
 */
static markDailyShareReward(userId: string): void {
  try {
    const today = new Date().toDateString()
    const storageKey = `daily_share_reward_${userId}_${today}`
    uni.setStorageSync(storageKey, true)
  } catch (error) {
    console.error('标记每日分享奖励失败:', error)
  }
}

/**
 * 获取分享奖励（带每日限制检查）
 */
static async claimShareReward(params: ShareRewardRequest): Promise<void> {
  // 检查今日是否已经获取过分享奖励
  const hasSharedToday = this.checkDailyShareReward(params.userId)
  if (hasSharedToday) {
    uni.showToast({
      title: '今日已获得分享奖励',
      icon: 'none',
      duration: 2000
    })
    return
  }

  const rewardResponse = await weixinApi.getShareReward(params)
  
  if (rewardResponse.success) {
    // 标记今日已获取分享奖励
    this.markDailyShareReward(params.userId)
    
    uni.showToast({
      title: `获得${rewardResponse.reward.description}！`,
      icon: 'success',
      duration: 3000
    })
  }
}
```

## 🛠 调试功能

### 调试页面新增功能

#### 1. 检查每日分享状态
```typescript
const testCheckDailyShare = async () => {
  const hasSharedToday = shareUtils.checkDailyShareReward(testUserId.value.trim())
  const today = new Date().toDateString()
  
  apiResult.value = JSON.stringify({
    userId: testUserId.value.trim(),
    date: today,
    hasSharedToday: hasSharedToday,
    message: hasSharedToday ? '今日已获取过分享奖励' : '今日尚未获取分享奖励'
  }, null, 2)
}
```

#### 2. 重置每日分享状态
```typescript
const testResetDailyShare = async () => {
  const today = new Date().toDateString()
  const storageKey = `daily_share_reward_${testUserId.value.trim()}_${today}`
  
  // 删除今日分享记录
  uni.removeStorageSync(storageKey)
  
  uni.showToast({
    title: '分享状态已重置',
    icon: 'success',
    duration: 2000
  })
}
```

### 使用方法
1. **访问调试页面**：首页右上角"API调试"按钮
2. **设置测试参数**：输入用户ID
3. **测试功能**：
   - 点击"检查每日分享状态"查看当前状态
   - 点击"获取分享奖励"测试奖励获取
   - 点击"重置每日分享状态"重置今日状态（用于测试）

## 🔧 技术实现亮点

### 1. **日期标识**
- 使用 `new Date().toDateString()` 获取日期字符串
- 格式：`"Mon Dec 25 2023"`
- 跨时区兼容，基于用户本地时间

### 2. **存储策略**
- 使用 `uni.getStorageSync()` 和 `uni.setStorageSync()`
- 键名包含用户ID和日期，避免用户间冲突
- 轻量级存储，只存储布尔值

### 3. **双重检查机制**
- **本地检查**：优先检查本地存储状态
- **服务端同步**：服务端返回已领取时同步本地状态
- **容错处理**：检查失败时默认允许请求

### 4. **用户体验优化**
- **友好提示**：显示"今日已获得分享奖励"而非错误信息
- **状态同步**：成功获取奖励后立即标记状态
- **无感知处理**：不影响正常分享流程

## 🎮 用户体验

### 分享奖励流程（带每日限制）
```
用户点击分享
    ↓
检查今日是否已获取奖励
    ↓
如果已获取 → 显示"今日已获得分享奖励"提示
    ↓
如果未获取 → 继续分享流程
    ↓
分享完成后请求奖励
    ↓
获取奖励成功 → 标记今日已获取
    ↓
显示奖励获得提示（包含"今日分享奖励已领取完毕"）
```

### 用户反馈
- **首次分享**：正常获得奖励，提示"今日分享奖励已领取完毕"
- **重复分享**：显示"今日已获得分享奖励"，不会重复请求
- **跨日分享**：新的一天可以重新获得分享奖励

## 🎯 核心优势

1. **防止重复奖励**：确保用户每天只能获得一次分享奖励
2. **提升奖励价值**：限制频率让奖励更有价值感
3. **优化用户体验**：友好的提示信息，不影响分享体验
4. **数据一致性**：本地和服务端状态同步
5. **调试友好**：完整的测试和重置功能
6. **跨页面支持**：首页和游戏页面统一的限制逻辑

## ✅ 功能清单

- ✅ **每日状态检查**：检查用户今日是否已获取分享奖励
- ✅ **状态标记**：成功获取奖励后标记今日状态
- ✅ **首页集成**：首页分享奖励带每日限制
- ✅ **游戏页面集成**：游戏页面分享奖励带每日限制
- ✅ **工具类封装**：ShareUtils中的通用每日限制方法
- ✅ **双重检查**：本地检查 + 服务端同步
- ✅ **用户友好提示**：清晰的状态反馈
- ✅ **调试功能**：检查状态、重置状态的测试功能

## 🎉 总结

现在 `handleShareReward` 函数已经完全实现了每天只执行一次分享请求的限制：

1. **✅ 每日限制检查**：在请求奖励前检查今日是否已获取
2. **✅ 状态标记**：成功获取奖励后立即标记今日状态
3. **✅ 友好提示**：重复分享时显示友好提示信息
4. **✅ 跨页面支持**：首页和游戏页面都实现了每日限制
5. **✅ 调试功能**：提供完整的测试和重置功能

用户现在每天只能通过分享获得一次奖励，提升了奖励的价值感和分享的质量！🎉
