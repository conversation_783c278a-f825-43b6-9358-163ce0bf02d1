{
  "easycom": {
    "autoscan": true,
    "custom": {
      // uni-ui 规则如下配置
      "^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"
    }
  },
  "pages": [
    //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "趣护消消乐"
      }
    },
    {
      "path": "pages/game/index",
      "style": {
        "navigationBarTitleText": "游戏中心"
      }
    },
    {
      "path": "pages/debug/index",
      "style": {
        "navigationBarTitleText": "API调试"
      }
    },
    {
      "path": "pages/webview/index",
      "style": {
        "navigationBarTitleText": "加载中...",
        "navigationBarBackgroundColor": "#667eea",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/test-audio/index",
      "style": {
        "navigationBarTitleText": "音频测试",
        "navigationBarBackgroundColor": "#667eea",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/level-selection/index",
      "style": {
        "navigationBarTitleText": "选择关卡",
        "navigationBarBackgroundColor": "#667eea",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/help/index",
      "style": {
        "navigationBarTitleText": "帮助"
      }
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "趣护消消乐",
    "navigationBarBackgroundColor": "#fdf9f9",
    "backgroundColor": "#fdf9f9"
  }
}
