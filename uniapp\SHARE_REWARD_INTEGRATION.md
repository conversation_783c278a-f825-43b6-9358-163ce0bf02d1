# 分享奖励功能集成文档

## 🎯 概述

在todo位置完成了分享后获取额外通关机会的功能，通过 `/api/v1/weixin/share` 接口实现分享奖励机制，提升用户分享积极性和游戏参与度。

## 📋 实现的功能

### 1. 分享奖励API接口

#### 获取分享奖励接口
```typescript
// POST /api/v1/weixin/share
interface ShareRewardRequest {
  userId: string                                    // 用户ID（必填）
  shareType: 'app_message' | 'timeline' | 'system' // 分享类型
  page?: string                                     // 分享页面
  levelId?: string                                  // 关卡ID（可选）
  timestamp?: number                                // 分享时间戳
}

interface ShareRewardResponse {
  success: boolean                                  // 是否成功
  message: string                                   // 响应消息
  reward: {
    type: 'extra_chance' | 'coins' | 'experience'  // 奖励类型
    amount: number                                  // 奖励数量
    description: string                             // 奖励描述
  }
  userInfo?: UserInfo                              // 更新后的用户信息
}
```

### 2. 核心功能实现

#### 微信API分享奖励方法
```typescript
/**
 * 分享后获取奖励
 * POST /api/v1/weixin/share
 */
async getShareReward(params: ShareRewardRequest): Promise<ShareRewardResponse> {
  try {
    const url = getWeixinApiUrl('/share')
    const response = await httpClient.post<ShareRewardResponse>(url, params)
    
    // 如果返回了更新的用户信息，保存到本地
    if (response.userInfo) {
      uni.setStorageSync(STORAGE_KEYS.USER_INFO, JSON.stringify(response.userInfo))
    }
    
    return response
  } catch (error) {
    console.error('获取分享奖励失败:', error)
    throw error
  }
}
```

#### 分享工具类奖励处理
```typescript
/**
 * 获取分享奖励
 */
static async claimShareReward(params: ShareRewardRequest): Promise<void> {
  try {
    const rewardResponse = await weixinApi.getShareReward(params)
    
    if (rewardResponse.success) {
      // 显示奖励获得提示
      uni.showToast({
        title: `获得${rewardResponse.reward.description}！`,
        icon: 'success',
        duration: 3000
      })
    }
  } catch (error) {
    console.error('获取分享奖励失败:', error)
  }
}
```

### 3. 页面集成实现

#### 🏠 首页分享奖励
```typescript
/**
 * 处理分享奖励
 */
const handleShareReward = async (options: any) => {
  try {
    if (!userInfo.value?.id) {
      console.warn('用户信息不存在，无法获取分享奖励')
      return
    }

    // 延迟获取奖励，确保分享完成
    setTimeout(async () => {
      try {
        const rewardResponse = await weixinApi.getShareReward({
          userId: userInfo.value!.id,
          shareType: 'app_message',
          page: 'pages/index/index',
          timestamp: Date.now()
        })

        if (rewardResponse.success) {
          // 更新本地用户信息
          if (rewardResponse.userInfo) {
            userInfo.value = rewardResponse.userInfo
          }

          // 显示奖励获得提示
          uni.showModal({
            title: '分享奖励',
            content: `恭喜获得${rewardResponse.reward.description}！`,
            showCancel: false,
            confirmText: '太棒了'
          })
        }
      } catch (error) {
        console.error('获取分享奖励失败:', error)
      }
    }, 2000) // 延迟2秒，确保分享完成
  } catch (error) {
    console.error('处理分享奖励失败:', error)
  }
}

// 在onShareAppMessage中调用
onShareAppMessage((options) => {
  if(options.from === 'menu') {
    // 触发分享以后，通过 /api/v1/weixin/share 接口获取额外的通关机会
    handleShareReward(options)
  }

  return {
    title: "趣护消消乐",
    path: "/pages/index/index",
    success: function() {
      // 分享成功后也可以获取奖励（如果不是从菜单触发的）
      if (options.from !== 'menu') {
        handleShareReward(options)
      }
    }
  }
})
```

#### 🎮 游戏页面分享奖励
```typescript
/**
 * 处理游戏页面分享奖励
 */
const handleGameShareReward = async (options: any, shareParams: any) => {
  try {
    // 延迟获取奖励，确保分享完成
    setTimeout(async () => {
      try {
        const rewardResponse = await weixinApi.getShareReward({
          userId: userInfo.value!.id,
          shareType: 'app_message',
          page: shareParams.page,
          levelId: shareParams.levelId,
          timestamp: Date.now()
        })

        if (rewardResponse.success) {
          // 更新本地用户信息
          if (rewardResponse.userInfo) {
            userInfo.value = rewardResponse.userInfo
            // 保存到本地存储
            uni.setStorageSync('userInfo', JSON.stringify(rewardResponse.userInfo))
          }

          // 显示奖励获得提示
          uni.showModal({
            title: '分享奖励',
            content: `恭喜获得${rewardResponse.reward.description}！可以继续挑战更多关卡了！`,
            showCancel: false,
            confirmText: '太棒了'
          })
        }
      } catch (error) {
        console.error('获取游戏分享奖励失败:', error)
      }
    }, 2000)
  } catch (error) {
    console.error('处理游戏分享奖励失败:', error)
  }
}

// 在分享处理中调用
const onShareAppMessage = async (options: any) => {
  const shareParams = {
    page: 'pages/game/index',
    levelId: currentLevelDetail.value?.id || selectedLevelData.value?.id,
    userId: userInfo.value?.id
  }
  
  // 处理分享奖励
  if (userInfo.value?.id) {
    handleGameShareReward(options, shareParams)
  }
  
  return await shareUtils.handleShareAppMessage(options, shareParams)
}
```

## 🔧 技术实现亮点

### 1. **智能延迟机制**
```typescript
// 延迟获取奖励，确保分享完成
setTimeout(async () => {
  // 获取分享奖励的逻辑
}, 2000) // 延迟2秒
```
- 确保用户完成分享操作后再请求奖励
- 避免分享未完成就请求奖励的问题
- 提升用户体验和奖励获取成功率

### 2. **多触发点支持**
```typescript
if(options.from === 'menu') {
  // 从右上角菜单分享
  handleShareReward(options)
}

// 分享成功回调
success: function() {
  if (options.from !== 'menu') {
    // 从自定义按钮分享
    handleShareReward(options)
  }
}
```
- 支持右上角菜单分享和自定义按钮分享
- 确保所有分享方式都能获得奖励
- 避免重复奖励的问题

### 3. **用户信息同步**
```typescript
if (rewardResponse.userInfo) {
  // 更新内存中的用户信息
  userInfo.value = rewardResponse.userInfo
  // 保存到本地存储
  uni.setStorageSync('userInfo', JSON.stringify(rewardResponse.userInfo))
}
```
- 实时更新用户信息
- 同步内存和本地存储
- 确保数据一致性

### 4. **优雅的错误处理**
```typescript
try {
  // 分享奖励逻辑
} catch (error) {
  console.error('获取分享奖励失败:', error)
  // 不显示错误提示，避免影响用户体验
}
```
- 静默处理错误，不影响正常分享流程
- 详细的错误日志记录
- 保证用户体验的流畅性

## 🎮 用户体验

### 1. **分享奖励流程**
```
用户点击分享
    ↓
选择分享目标（微信好友/群聊）
    ↓
完成分享操作
    ↓
延迟2秒后自动请求奖励
    ↓
显示奖励获得弹窗
    ↓
更新用户信息和本地存储
```

### 2. **奖励类型**
- **额外通关机会**：`extra_chance` - 增加游戏次数
- **金币奖励**：`coins` - 游戏内货币
- **经验值**：`experience` - 提升用户等级

### 3. **用户反馈**
```typescript
// 成功获得奖励
uni.showModal({
  title: '分享奖励',
  content: `恭喜获得${rewardResponse.reward.description}！`,
  showCancel: false,
  confirmText: '太棒了'
})

// 简单提示
uni.showToast({
  title: `获得${rewardResponse.reward.description}！`,
  icon: 'success',
  duration: 3000
})
```

## 🛠 调试功能

### 调试页面测试
```typescript
const testGetShareReward = async () => {
  try {
    const rewardResponse = await weixinApi.getShareReward({
      userId: testUserId.value.trim(),
      shareType: 'app_message',
      page: 'pages/index/index',
      levelId: testLevelId.value.trim() || undefined,
      timestamp: Date.now()
    })
    
    if (rewardResponse.success) {
      uni.showToast({
        title: `获得${rewardResponse.reward.description}！`,
        icon: 'success',
        duration: 3000
      })
    }
  } catch (error) {
    console.error('测试分享奖励失败:', error)
  }
}
```

### 使用方法
1. **访问调试页面**：首页右上角"API调试"按钮
2. **设置测试参数**：
   - 用户ID：12345678
   - 关卡ID：level-uuid-123（可选）
3. **点击测试按钮**："获取分享奖励"
4. **查看结果**：API响应和奖励提示

## 🔄 数据流

```
用户完成分享
    ↓
延迟2秒触发奖励请求
    ↓
POST /api/v1/weixin/share
    ↓
服务端验证分享有效性
    ↓
返回奖励信息和更新的用户数据
    ↓
更新本地用户信息
    ↓
显示奖励获得提示
    ↓
用户继续游戏
```

## 🎯 核心优势

1. **无缝集成**：在现有分享流程中无缝添加奖励机制
2. **用户友好**：不影响正常分享体验，奖励作为额外惊喜
3. **数据同步**：实时更新用户信息，确保数据一致性
4. **错误容错**：优雅处理错误，不影响主要功能
5. **多场景支持**：首页和游戏页面都支持分享奖励
6. **调试完善**：提供完整的测试和调试功能

## ✅ 功能清单

- ✅ **分享奖励API**：完整的服务端接口集成
- ✅ **首页分享奖励**：右上角菜单和成功回调双重触发
- ✅ **游戏页面分享奖励**：关卡相关的个性化奖励
- ✅ **用户信息同步**：实时更新用户数据
- ✅ **优雅错误处理**：不影响用户体验的错误处理
- ✅ **调试测试功能**：完整的测试和调试工具
- ✅ **多种奖励类型**：支持通关机会、金币、经验值等

## 🎉 总结

现在项目已经完全实现了分享奖励功能：

1. **完整的API集成**：通过 `/api/v1/weixin/share` 接口获取分享奖励
2. **智能触发机制**：在分享完成后自动获取奖励
3. **多页面支持**：首页和游戏页面都支持分享奖励
4. **用户体验优化**：流畅的奖励获取和反馈机制
5. **完善的调试工具**：方便开发和测试

用户现在可以通过分享获得额外的通关机会，大大提升了分享的积极性和游戏的传播效果！🎉
